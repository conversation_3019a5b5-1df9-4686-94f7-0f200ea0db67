// Subscription Admin Panel JavaScript
// إدارة نظام الاشتراك المجاني المتقدم

// Supabase configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Global variables
let campaigns = [];
let currentEditingCampaign = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Subscription Admin Panel loaded');
    loadDashboardStats();
    loadCampaigns();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Form submission
    document.getElementById('createCampaignForm').addEventListener('submit', handleCreateCampaign);
}

// Switch between tabs
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName + '-tab').classList.add('active');
    event.target.classList.add('active');

    // Load data based on selected tab
    switch(tabName) {
        case 'campaigns':
            loadCampaigns();
            break;
        case 'tasks':
            // Load tasks data
            break;
        case 'subscribers':
            // Load subscribers data
            break;
        case 'analytics':
            // Load analytics data
            break;
    }
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        // Initialize with default values
        let campaignsCount = 0;
        let subscribersCount = 0;
        let tasksCount = 0;
        let completionRate = 0;

        // Get total campaigns
        try {
            const { count } = await supabaseClient
                .from('free_subscription_campaigns')
                .select('*', { count: 'exact', head: true });
            campaignsCount = count || 0;
        } catch (error) {
            console.log('Campaigns table not found or empty:', error.message);
        }

        // Get active subscribers
        try {
            const { count } = await supabaseClient
                .from('user_subscriptions')
                .select('*', { count: 'exact', head: true })
                .eq('status', 'active');
            subscribersCount = count || 0;
        } catch (error) {
            console.log('User subscriptions table not found or empty:', error.message);
        }

        // Get total tasks
        try {
            const { count } = await supabaseClient
                .from('campaign_tasks')
                .select('*', { count: 'exact', head: true });
            tasksCount = count || 0;
        } catch (error) {
            console.log('Campaign tasks table not found or empty:', error.message);
        }

        // Calculate completion rate (simplified)
        try {
            const { data: completedTasks } = await supabaseClient
                .from('user_task_progress')
                .select('id')
                .eq('status', 'completed');

            const { data: allTasks } = await supabaseClient
                .from('user_task_progress')
                .select('id');

            completionRate = allTasks && allTasks.length > 0
                ? Math.round((completedTasks?.length || 0) / allTasks.length * 100)
                : 0;
        } catch (error) {
            console.log('Task progress table not found or empty:', error.message);
        }

        // Update UI
        document.getElementById('totalCampaigns').textContent = campaignsCount;
        document.getElementById('totalSubscribers').textContent = subscribersCount;
        document.getElementById('totalTasks').textContent = tasksCount;
        document.getElementById('completionRate').textContent = completionRate + '%';

    } catch (error) {
        console.error('Error loading dashboard stats:', error);
        // Set default values on error
        document.getElementById('totalCampaigns').textContent = '0';
        document.getElementById('totalSubscribers').textContent = '0';
        document.getElementById('totalTasks').textContent = '0';
        document.getElementById('completionRate').textContent = '0%';
    }
}

// Load campaigns
async function loadCampaigns() {
    try {
        showLoading('campaignsList');

        // Try to load campaigns, handle table not existing
        try {
            const { data, error } = await supabaseClient
                .from('free_subscription_campaigns')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) {
                throw error;
            }

            campaigns = data || [];
            displayCampaigns(campaigns);

        } catch (error) {
            console.log('Campaigns table error:', error.message);

            if (error.message.includes('relation') && error.message.includes('does not exist')) {
                // Table doesn't exist
                showTableNotExistError('campaignsList');
            } else {
                console.error('Error loading campaigns:', error);
                showError('campaignsList', 'حدث خطأ أثناء تحميل الحملات: ' + error.message);
            }
        }

    } catch (error) {
        console.error('Error in loadCampaigns:', error);
        showError('campaignsList', 'حدث خطأ أثناء تحميل الحملات');
    }
}

// Display campaigns
function displayCampaigns(campaignsData) {
    const container = document.getElementById('campaignsList');

    if (!campaignsData || campaignsData.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 50px; color: #ccc;">
                <i class="fas fa-bullhorn" style="font-size: 4rem; margin-bottom: 20px; color: #ffd700;"></i>
                <h3>لا توجد حملات حتى الآن</h3>
                <p>ابدأ بإنشاء حملة اشتراك مجاني جديدة</p>
                <button class="btn btn-primary" onclick="showCreateCampaignModal()" style="margin-top: 20px;">
                    <i class="fas fa-plus"></i> إنشاء حملة جديدة
                </button>
            </div>
        `;
        return;
    }

    const campaignsHTML = campaignsData.map(campaign => {
        const subscribersCount = campaign.user_subscriptions?.length || 0;
        const isActive = campaign.is_active;
        const endDate = campaign.end_date ? new Date(campaign.end_date) : null;
        const isExpired = endDate && endDate < new Date();

        return `
            <div class="campaign-card">
                <div class="campaign-header">
                    <div class="campaign-title">${campaign.title_ar}</div>
                    <div class="campaign-status ${isActive && !isExpired ? 'status-active' : 'status-inactive'}">
                        ${isActive && !isExpired ? 'نشطة' : isExpired ? 'منتهية' : 'غير نشطة'}
                    </div>
                </div>

                <div class="campaign-info">
                    <div class="info-item">
                        <div class="info-label">المشتركون</div>
                        <div class="info-value">${subscribersCount}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">مدة الاشتراك</div>
                        <div class="info-value">${campaign.subscription_duration_days} يوم</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الحد الأقصى</div>
                        <div class="info-value">${campaign.max_users || 'غير محدود'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">تاريخ الإنشاء</div>
                        <div class="info-value">${new Date(campaign.created_at).toLocaleDateString('ar-SA')}</div>
                    </div>
                </div>

                <div style="margin-bottom: 15px; color: #ccc; line-height: 1.5;">
                    ${campaign.description_ar}
                </div>

                <div class="campaign-actions">
                    <button class="btn btn-primary" onclick="editCampaign('${campaign.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-secondary" onclick="viewCampaignTasks('${campaign.id}')">
                        <i class="fas fa-tasks"></i> المهام
                    </button>
                    <button class="btn btn-secondary" onclick="viewCampaignSubscribers('${campaign.id}')">
                        <i class="fas fa-users"></i> المشتركون
                    </button>
                    <button class="btn ${isActive ? 'btn-danger' : 'btn-success'}" onclick="toggleCampaignStatus('${campaign.id}', ${!isActive})">
                        <i class="fas fa-${isActive ? 'pause' : 'play'}"></i> ${isActive ? 'إيقاف' : 'تفعيل'}
                    </button>
                    <button class="btn btn-danger" onclick="deleteCampaign('${campaign.id}')" style="margin-right: 10px;">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = campaignsHTML;
}

// Show create campaign modal
function showCreateCampaignModal() {
    document.getElementById('createCampaignModal').style.display = 'flex';
    // Reset form
    document.getElementById('createCampaignForm').reset();
    document.getElementById('subscriptionDuration').value = '30';
    document.getElementById('campaignActive').checked = true;
}

// Close modal
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Handle create campaign form submission
async function handleCreateCampaign(event) {
    event.preventDefault();

    try {
        showFormLoading(true);

        const formData = {
            title_ar: document.getElementById('campaignTitleAr').value,
            title_en: document.getElementById('campaignTitleEn').value,
            description_ar: document.getElementById('campaignDescAr').value,
            description_en: document.getElementById('campaignDescEn').value,
            subscription_duration_days: parseInt(document.getElementById('subscriptionDuration').value),
            max_users: document.getElementById('maxUsers').value ? parseInt(document.getElementById('maxUsers').value) : null,
            end_date: document.getElementById('campaignEndDate').value ? new Date(document.getElementById('campaignEndDate').value).toISOString() : null,
            is_active: document.getElementById('campaignActive').checked,
            banner_image_url: '', // Will be updated when banner is created
            popup_image_url: null
        };

        const { data, error } = await supabaseClient
            .from('free_subscription_campaigns')
            .insert([formData])
            .select();

        if (error) {
            console.error('Error creating campaign:', error);
            showMessage('حدث خطأ أثناء إنشاء الحملة: ' + error.message, 'error');
            return;
        }

        showMessage('تم إنشاء الحملة بنجاح!', 'success');
        closeModal('createCampaignModal');
        loadCampaigns();
        loadDashboardStats();

    } catch (error) {
        console.error('Error in handleCreateCampaign:', error);
        showMessage('حدث خطأ أثناء إنشاء الحملة', 'error');
    } finally {
        showFormLoading(false);
    }
}

// Toggle campaign status
async function toggleCampaignStatus(campaignId, newStatus) {
    try {
        const { error } = await supabaseClient
            .from('free_subscription_campaigns')
            .update({ is_active: newStatus })
            .eq('id', campaignId);

        if (error) {
            console.error('Error updating campaign status:', error);
            showMessage('حدث خطأ أثناء تحديث حالة الحملة', 'error');
            return;
        }

        showMessage(`تم ${newStatus ? 'تفعيل' : 'إيقاف'} الحملة بنجاح`, 'success');
        loadCampaigns();

    } catch (error) {
        console.error('Error in toggleCampaignStatus:', error);
        showMessage('حدث خطأ أثناء تحديث حالة الحملة', 'error');
    }
}

// Delete campaign
async function deleteCampaign(campaignId) {
    if (!confirm('هل أنت متأكد من حذف هذه الحملة؟ سيتم حذف جميع البيانات المرتبطة بها.')) {
        return;
    }

    try {
        const { error } = await supabaseClient
            .from('free_subscription_campaigns')
            .delete()
            .eq('id', campaignId);

        if (error) {
            console.error('Error deleting campaign:', error);
            showMessage('حدث خطأ أثناء حذف الحملة', 'error');
            return;
        }

        showMessage('تم حذف الحملة بنجاح', 'success');
        loadCampaigns();
        loadDashboardStats();

    } catch (error) {
        console.error('Error in deleteCampaign:', error);
        showMessage('حدث خطأ أثناء حذف الحملة', 'error');
    }
}

// Edit campaign (placeholder)
function editCampaign(campaignId) {
    showMessage('ميزة التعديل قيد التطوير', 'info');
}

// View campaign tasks (placeholder)
function viewCampaignTasks(campaignId) {
    showMessage('ميزة عرض المهام قيد التطوير', 'info');
}

// View campaign subscribers (placeholder)
function viewCampaignSubscribers(campaignId) {
    showMessage('ميزة عرض المشتركين قيد التطوير', 'info');
}

// Utility functions
function showLoading(containerId) {
    document.getElementById(containerId).innerHTML = `
        <div class="loading">
            <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
        </div>
    `;
}

function showError(containerId, message) {
    document.getElementById(containerId).innerHTML = `
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i> ${message}
        </div>
    `;
}

function showTableNotExistError(containerId) {
    document.getElementById(containerId).innerHTML = `
        <div style="text-align: center; padding: 50px; background: rgba(255, 215, 0, 0.1); border-radius: 15px; border: 2px solid #ffd700;">
            <i class="fas fa-database" style="font-size: 4rem; color: #ffd700; margin-bottom: 20px;"></i>
            <h3 style="color: #ffd700; margin-bottom: 15px;">قاعدة البيانات غير مُعدة</h3>
            <p style="color: white; margin-bottom: 20px; line-height: 1.6;">
                يبدو أن جداول نظام الاشتراك المجاني غير موجودة في قاعدة البيانات.
                <br>يرجى تشغيل كود SQL أولاً لإنشاء الجداول المطلوبة.
            </p>

            <div style="background: rgba(0, 0, 0, 0.3); padding: 20px; border-radius: 10px; margin: 20px 0; text-align: left;">
                <h4 style="color: #ffd700; margin-bottom: 10px;">خطوات الإعداد:</h4>
                <ol style="color: #ccc; line-height: 1.8;">
                    <li>افتح Supabase Dashboard</li>
                    <li>اذهب إلى SQL Editor</li>
                    <li>انسخ والصق محتوى ملف: <code style="background: rgba(255, 215, 0, 0.2); padding: 2px 5px; border-radius: 3px;">database/free_subscription_system.sql</code></li>
                    <li>اضغط "Run" لتشغيل الكود</li>
                    <li>أعد تحميل هذه الصفحة</li>
                </ol>
            </div>

            <button class="btn btn-primary" onclick="window.location.reload()" style="margin-top: 15px;">
                <i class="fas fa-refresh"></i> إعادة تحميل الصفحة
            </button>

            <a href="https://supabase.com/dashboard" target="_blank" class="btn btn-secondary" style="margin-top: 15px; margin-right: 10px;">
                <i class="fas fa-external-link-alt"></i> فتح Supabase Dashboard
            </a>
        </div>
    `;
}

function showFormLoading(show) {
    const submitBtn = document.querySelector('#createCampaignForm button[type="submit"]');
    if (show) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';
    } else {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save"></i> إنشاء الحملة';
    }
}

function showMessage(message, type = 'info') {
    // Remove existing messages
    document.querySelectorAll('.app-message').forEach(msg => msg.remove());

    const messageDiv = document.createElement('div');
    messageDiv.className = 'app-message';
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? 'linear-gradient(45deg, #22c55e, #16a34a)' :
                     type === 'error' ? 'linear-gradient(45deg, #ef4444, #dc2626)' :
                     type === 'warning' ? 'linear-gradient(45deg, #f59e0b, #d97706)' :
                     'linear-gradient(45deg, #3b82f6, #2563eb)'};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-weight: bold;
        z-index: 10002;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        animation: slideInDown 0.3s ease-out;
    `;

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 5000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-100%);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }

    @keyframes slideOutUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-100%);
        }
    }
`;
document.head.appendChild(style);
