<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأقسام المخصصة - Mod Etaris Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .header h1 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            margin-bottom: 40px;
        }

        .sections-list {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 215, 0, 0.2);
        }

        .section-form {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 215, 0, 0.2);
            position: sticky;
            top: 20px;
            height: fit-content;
        }

        .form-title {
            color: #ffd700;
            font-size: 1.5rem;
            margin-bottom: 25px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            color: #ffd700;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .form-input option {
            background: #1a1a2e;
            color: white;
        }

        .icon-selector {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.2);
        }

        .icon-option {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.05);
        }

        .icon-option:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.2);
            transform: scale(1.1);
        }

        .icon-option.selected {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.3);
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .icon-option i {
            color: #ffd700;
            font-size: 18px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 5px;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .section-item {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border: 2px solid rgba(255, 215, 0, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .section-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
            border-color: #ffd700;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .section-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 215, 0, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #ffd700;
        }

        .section-icon i {
            color: #ffd700;
            font-size: 20px;
        }

        .section-details h3 {
            color: #ffd700;
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .section-details p {
            color: #ccc;
            font-size: 0.9rem;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .section-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .stat-value {
            color: #ffd700;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .stat-label {
            color: #ccc;
            font-size: 0.8rem;
        }

        .order-controls {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .order-btn {
            width: 30px;
            height: 30px;
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid #ffd700;
            border-radius: 50%;
            color: #ffd700;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .order-btn:hover {
            background: rgba(255, 215, 0, 0.4);
            transform: scale(1.1);
        }

        .success-message, .error-message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            display: none;
        }

        .success-message {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid #22c55e;
            color: #86efac;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #fca5a5;
        }

        .mod-selector {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
        }

        .mod-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 5px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mod-item:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .mod-item.selected {
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid #ffd700;
        }

        .mod-checkbox {
            width: 18px;
            height: 18px;
            accent-color: #ffd700;
        }

        .mod-info {
            flex: 1;
        }

        .mod-name {
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .mod-category {
            color: #ccc;
            font-size: 0.8rem;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .section-form {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .section-actions {
                width: 100%;
                justify-content: center;
            }
            
            .icon-selector {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-layer-group"></i> إدارة الأقسام المخصصة</h1>
            <p>إنشاء وإدارة أقسام مخصصة لعرض المودات في الواجهة الرئيسية</p>
        </div>

        <div id="successMessage" class="success-message"></div>
        <div id="errorMessage" class="error-message"></div>

        <div class="main-content">
            <!-- قائمة الأقسام الحالية -->
            <div class="sections-list">
                <h2 style="color: #ffd700; margin-bottom: 25px; text-align: center;">
                    <i class="fas fa-list"></i> الأقسام الحالية
                </h2>
                <div id="sectionsList">
                    <div style="text-align: center; color: #ccc; padding: 50px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i>
                        <p>جاري تحميل الأقسام...</p>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل قسم -->
            <div class="section-form">
                <h2 class="form-title">
                    <i class="fas fa-plus-circle"></i>
                    <span id="formTitle">إضافة قسم جديد</span>
                </h2>

                <form id="sectionForm">
                    <input type="hidden" id="sectionId" value="">
                    
                    <div class="form-group">
                        <label class="form-label" for="sectionNameAr">
                            <i class="fas fa-tag"></i> اسم القسم (عربي)
                        </label>
                        <input type="text" id="sectionNameAr" class="form-input" placeholder="مثال: المودات المميزة" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="sectionNameEn">
                            <i class="fas fa-tag"></i> اسم القسم (إنجليزي)
                        </label>
                        <input type="text" id="sectionNameEn" class="form-input" placeholder="Example: Featured Mods" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-icons"></i> أيقونة القسم
                        </label>
                        <input type="text" id="selectedIcon" class="form-input" placeholder="fas fa-star" readonly>
                        <div class="icon-selector" id="iconSelector">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="sectionDescription">
                            <i class="fas fa-align-left"></i> وصف القسم
                        </label>
                        <textarea id="sectionDescription" class="form-input" rows="3" placeholder="وصف مختصر للقسم..."></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="maxMods">
                            <i class="fas fa-hashtag"></i> عدد المودات الأقصى
                        </label>
                        <input type="number" id="maxMods" class="form-input" value="10" min="1" max="50">
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-cubes"></i> اختيار المودات
                        </label>
                        <div class="mod-selector" id="modSelector">
                            <div style="text-align: center; color: #ccc; padding: 20px;">
                                <i class="fas fa-spinner fa-spin"></i> جاري تحميل المودات...
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="isActive">
                            <i class="fas fa-toggle-on"></i> حالة القسم
                        </label>
                        <select id="isActive" class="form-input">
                            <option value="true">نشط</option>
                            <option value="false">غير نشط</option>
                        </select>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ القسم
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="custom_sections_admin.js"></script>
</body>
</html>
