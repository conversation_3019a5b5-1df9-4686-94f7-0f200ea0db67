/* تنسيقات لوحة إدارة أخطاء التحميل */
/* Download Error Manager Styles */

/* الحاوي الرئيسي */
.error-manager-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* رأس الصفحة */
.error-manager-header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.error-manager-header h1 {
    color: #2d3748;
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: 700;
    text-align: center;
}

.error-manager-header p {
    color: #718096;
    text-align: center;
    font-size: 1.1em;
    margin: 0;
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-card h3 {
    color: #2d3748;
    margin: 0 0 15px 0;
    font-size: 1.2em;
    font-weight: 600;
}

.stat-number {
    display: block;
    font-size: 2.5em;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #718096;
    font-size: 0.9em;
}

/* أدوات التحكم */
.controls-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.controls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.control-group {
    display: flex;
    flex-direction: column;
}

.control-group label {
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.control-group select,
.control-group input {
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9em;
    transition: border-color 0.3s ease;
    background: white;
}

.control-group select:focus,
.control-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* الأزرار */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #e2e8f0;
    color: #2d3748;
}

.btn-secondary:hover {
    background: #cbd5e0;
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

/* قائمة الأخطاء */
.errors-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.errors-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* بطاقة الخطأ */
.error-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.error-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* مستويات الخطورة */
.error-critical {
    border-right: 4px solid #f56565;
    background: linear-gradient(90deg, rgba(245, 101, 101, 0.05) 0%, transparent 100%);
}

.error-high {
    border-right: 4px solid #ed8936;
    background: linear-gradient(90deg, rgba(237, 137, 54, 0.05) 0%, transparent 100%);
}

.error-medium {
    border-right: 4px solid #ecc94b;
    background: linear-gradient(90deg, rgba(236, 201, 75, 0.05) 0%, transparent 100%);
}

.error-low {
    border-right: 4px solid #48bb78;
    background: linear-gradient(90deg, rgba(72, 187, 120, 0.05) 0%, transparent 100%);
}

/* حالة الخطأ */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
}

.status-resolved {
    background: #c6f6d5;
    color: #22543d;
}

.status-unresolved {
    background: #fed7d7;
    color: #742a2a;
}

/* تفاصيل الخطأ */
.error-details {
    margin-top: 10px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 8px;
    font-size: 0.9em;
    line-height: 1.5;
}

.error-details strong {
    color: #2d3748;
    display: block;
    margin-bottom: 5px;
}

.error-details small {
    color: #718096;
    word-break: break-all;
}

/* أزرار الإجراءات */
.action-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    font-size: 0.8em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 2px;
    min-width: 80px;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* التصفح */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    padding: 20px;
}

.pagination .page-item {
    list-style: none;
}

.pagination .page-link {
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    color: #2d3748;
    text-decoration: none;
    transition: all 0.3s ease;
    background: white;
}

.pagination .page-link:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination .page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.modal-header h3 {
    margin: 0;
    color: #2d3748;
    font-size: 1.5em;
}

.modal-body {
    margin-bottom: 20px;
}

.modal-footer {
    border-top: 2px solid #e2e8f0;
    padding-top: 15px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* حقول النموذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1em;
    transition: border-color 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* حالة التحميل */
.loading {
    text-align: center;
    padding: 40px;
    color: #718096;
    font-size: 1.1em;
}

.loading i {
    font-size: 2em;
    margin-bottom: 15px;
    color: #667eea;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 4em;
    margin-bottom: 20px;
    color: #cbd5e0;
}

.empty-state h4 {
    color: #2d3748;
    margin-bottom: 10px;
}

/* التنبيهات */
.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
    color: #22543d;
}

.alert-warning {
    background: linear-gradient(135deg, #feebc8 0%, #fbd38d 100%);
    color: #744210;
}

.alert-danger {
    background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
    color: #742a2a;
}

.alert-info {
    background: linear-gradient(135deg, #bee3f8 0%, #90cdf4 100%);
    color: #2a4365;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .error-manager-container {
        padding: 15px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .controls-grid {
        grid-template-columns: 1fr;
    }
    
    .error-card .row {
        flex-direction: column;
    }
    
    .error-card .col-md-2,
    .error-card .col-md-6,
    .error-card .col-md-3 {
        width: 100%;
        margin-bottom: 15px;
    }
    
    .btn-group-vertical {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
}
