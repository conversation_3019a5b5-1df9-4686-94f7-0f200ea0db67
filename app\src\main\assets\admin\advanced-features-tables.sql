-- ========================================
-- إنشاء جداول الميزات المتقدمة للأدمن
-- Advanced Admin Features Database Tables
-- ========================================

-- 1. جدول إحصائيات المستخدمين
-- User Statistics Table
CREATE TABLE IF NOT EXISTS user_statistics (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_end TIMESTAMP WITH TIME ZONE,
    session_duration INTEGER, -- in seconds
    pages_visited INTEGER DEFAULT 0,
    mods_viewed INTEGER DEFAULT 0,
    mods_downloaded INTEGER DEFAULT 0,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. جدول تتبع التحميلات
-- Downloads Tracking Table
CREATE TABLE IF NOT EXISTS download_analytics (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255),
    mod_id UUID NOT NULL,
    download_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    download_source VARCHAR(100), -- 'app', 'web', 'api'
    device_type VARCHAR(50),
    country_code VARCHAR(2),
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    file_size BIGINT,
    download_duration INTEGER, -- in seconds
    CONSTRAINT fk_download_analytics_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE
);

-- 3. جدول تحليل المشاهدات
-- Views Analytics Table
CREATE TABLE IF NOT EXISTS view_analytics (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255),
    mod_id UUID NOT NULL,
    view_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    view_duration INTEGER, -- in seconds
    device_type VARCHAR(50),
    referrer VARCHAR(255),
    country_code VARCHAR(2),
    CONSTRAINT fk_view_analytics_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE
);

-- 4. جدول الإشعارات
-- Notifications Table
CREATE TABLE IF NOT EXISTS admin_notifications (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    notification_type VARCHAR(50) DEFAULT 'general', -- 'general', 'update', 'promotion', 'warning'
    target_audience VARCHAR(50) DEFAULT 'all', -- 'all', 'active', 'new', 'premium'
    sent_by VARCHAR(255), -- admin user id
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    scheduled_for TIMESTAMP WITH TIME ZONE,
    is_sent BOOLEAN DEFAULT false,
    recipients_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 5. جدول سجل الإشعارات للمستخدمين
-- User Notifications Log Table
CREATE TABLE IF NOT EXISTS user_notifications_log (
    id SERIAL PRIMARY KEY,
    notification_id INTEGER NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    is_read BOOLEAN DEFAULT false,
    CONSTRAINT fk_user_notifications_log_notification_id FOREIGN KEY (notification_id) REFERENCES admin_notifications(id) ON DELETE CASCADE
);

-- 6. جدول المستخدمين المحظورين
-- Banned Users Table
CREATE TABLE IF NOT EXISTS banned_users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    banned_by VARCHAR(255), -- admin user id
    ban_reason TEXT,
    banned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ban_expires_at TIMESTAMP WITH TIME ZONE,
    is_permanent BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 7. جدول تقييمات المودات
-- Mod Reviews Table
CREATE TABLE IF NOT EXISTS mod_reviews (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_approved BOOLEAN DEFAULT false,
    approved_by VARCHAR(255), -- admin user id
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_mod_reviews_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_user_mod_review UNIQUE (mod_id, user_id)
);

-- 8. جدول سجل أنشطة الأدمن
-- Admin Activity Log Table
CREATE TABLE IF NOT EXISTS admin_activity_log (
    id SERIAL PRIMARY KEY,
    admin_user_id VARCHAR(255) NOT NULL,
    action_type VARCHAR(100) NOT NULL, -- 'create', 'update', 'delete', 'approve', 'ban', etc.
    target_type VARCHAR(100), -- 'mod', 'user', 'notification', 'banner', etc.
    target_id VARCHAR(255),
    action_details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 9. جدول إعدادات النظام
-- System Settings Table
CREATE TABLE IF NOT EXISTS system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(255) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    updated_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 10. جدول تقارير الأخطاء
-- Error Reports Table
CREATE TABLE IF NOT EXISTS error_reports (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255),
    error_type VARCHAR(100),
    error_message TEXT,
    stack_trace TEXT,
    page_url VARCHAR(500),
    user_agent TEXT,
    device_info JSONB,
    is_resolved BOOLEAN DEFAULT false,
    resolved_by VARCHAR(255),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- إنشاء الفهارس للأداء
-- Create Indexes for Performance
-- ========================================

-- User Statistics Indexes
CREATE INDEX IF NOT EXISTS idx_user_statistics_user_id ON user_statistics(user_id);
CREATE INDEX IF NOT EXISTS idx_user_statistics_session_start ON user_statistics(session_start);

-- Download Analytics Indexes
CREATE INDEX IF NOT EXISTS idx_download_analytics_user_id ON download_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_download_analytics_mod_id ON download_analytics(mod_id);
CREATE INDEX IF NOT EXISTS idx_download_analytics_download_date ON download_analytics(download_date);

-- View Analytics Indexes
CREATE INDEX IF NOT EXISTS idx_view_analytics_user_id ON view_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_view_analytics_mod_id ON view_analytics(mod_id);
CREATE INDEX IF NOT EXISTS idx_view_analytics_view_date ON view_analytics(view_date);

-- Notifications Indexes
CREATE INDEX IF NOT EXISTS idx_admin_notifications_sent_at ON admin_notifications(sent_at);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_type ON admin_notifications(notification_type);
CREATE INDEX IF NOT EXISTS idx_user_notifications_log_user_id ON user_notifications_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_log_notification_id ON user_notifications_log(notification_id);

-- Reviews Indexes
CREATE INDEX IF NOT EXISTS idx_mod_reviews_mod_id ON mod_reviews(mod_id);
CREATE INDEX IF NOT EXISTS idx_mod_reviews_user_id ON mod_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_mod_reviews_rating ON mod_reviews(rating);

-- Admin Activity Log Indexes
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_admin_user_id ON admin_activity_log(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_action_type ON admin_activity_log(action_type);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created_at ON admin_activity_log(created_at);

-- Error Reports Indexes
CREATE INDEX IF NOT EXISTS idx_error_reports_user_id ON error_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_error_reports_error_type ON error_reports(error_type);
CREATE INDEX IF NOT EXISTS idx_error_reports_created_at ON error_reports(created_at);

-- ========================================
-- إنشاء Views للتقارير
-- Create Views for Reports
-- ========================================

-- View for Daily Download Statistics
CREATE OR REPLACE VIEW daily_download_stats AS
SELECT 
    DATE(download_date) as download_day,
    COUNT(*) as total_downloads,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT mod_id) as unique_mods,
    AVG(download_duration) as avg_download_duration
FROM download_analytics 
WHERE success = true
GROUP BY DATE(download_date)
ORDER BY download_day DESC;

-- View for Popular Mods
CREATE OR REPLACE VIEW popular_mods_stats AS
SELECT 
    m.id,
    m.name,
    m.category,
    COUNT(DISTINCT da.user_id) as unique_downloaders,
    COUNT(da.id) as total_downloads,
    COUNT(DISTINCT va.user_id) as unique_viewers,
    COUNT(va.id) as total_views,
    AVG(mr.rating) as average_rating,
    COUNT(mr.id) as total_reviews
FROM mods m
LEFT JOIN download_analytics da ON m.id = da.mod_id
LEFT JOIN view_analytics va ON m.id = va.mod_id
LEFT JOIN mod_reviews mr ON m.id = mr.mod_id AND mr.is_approved = true
GROUP BY m.id, m.name, m.category
ORDER BY total_downloads DESC;

-- View for User Activity Summary
CREATE OR REPLACE VIEW user_activity_summary AS
SELECT 
    us.user_id,
    ul.selected_language,
    COUNT(DISTINCT us.id) as total_sessions,
    AVG(us.session_duration) as avg_session_duration,
    SUM(us.mods_downloaded) as total_downloads,
    SUM(us.mods_viewed) as total_views,
    MAX(us.session_start) as last_activity
FROM user_statistics us
LEFT JOIN user_languages ul ON us.user_id = ul.user_id
GROUP BY us.user_id, ul.selected_language
ORDER BY last_activity DESC;

-- ========================================
-- إنشاء Functions مساعدة
-- Create Helper Functions
-- ========================================

-- Function to get download statistics for a date range
CREATE OR REPLACE FUNCTION get_download_stats(start_date DATE, end_date DATE)
RETURNS TABLE(
    total_downloads BIGINT,
    unique_users BIGINT,
    unique_mods BIGINT,
    avg_duration NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::BIGINT as total_downloads,
        COUNT(DISTINCT user_id)::BIGINT as unique_users,
        COUNT(DISTINCT mod_id)::BIGINT as unique_mods,
        AVG(download_duration)::NUMERIC as avg_duration
    FROM download_analytics 
    WHERE DATE(download_date) BETWEEN start_date AND end_date
    AND success = true;
END;
$$ LANGUAGE plpgsql;

-- Function to get top mods by category
CREATE OR REPLACE FUNCTION get_top_mods_by_category(category_name TEXT, limit_count INTEGER DEFAULT 10)
RETURNS TABLE(
    mod_id UUID,
    mod_name TEXT,
    download_count BIGINT,
    view_count BIGINT,
    average_rating NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id as mod_id,
        m.name as mod_name,
        COUNT(DISTINCT da.id)::BIGINT as download_count,
        COUNT(DISTINCT va.id)::BIGINT as view_count,
        AVG(mr.rating)::NUMERIC as average_rating
    FROM mods m
    LEFT JOIN download_analytics da ON m.id = da.mod_id
    LEFT JOIN view_analytics va ON m.id = va.mod_id
    LEFT JOIN mod_reviews mr ON m.id = mr.mod_id AND mr.is_approved = true
    WHERE m.category = category_name
    GROUP BY m.id, m.name
    ORDER BY download_count DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- منح الصلاحيات
-- Grant Permissions
-- ========================================

-- Grant permissions to anon and authenticated users
GRANT SELECT ON daily_download_stats TO anon, authenticated;
GRANT SELECT ON popular_mods_stats TO anon, authenticated;
GRANT SELECT ON user_activity_summary TO anon, authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_download_stats(DATE, DATE) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_top_mods_by_category(TEXT, INTEGER) TO anon, authenticated;

-- Grant table permissions (adjust as needed for security)
GRANT SELECT, INSERT, UPDATE ON user_statistics TO anon, authenticated;
GRANT SELECT, INSERT ON download_analytics TO anon, authenticated;
GRANT SELECT, INSERT ON view_analytics TO anon, authenticated;
GRANT SELECT ON admin_notifications TO anon, authenticated;
GRANT SELECT, INSERT ON user_notifications_log TO anon, authenticated;
GRANT SELECT ON banned_users TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON mod_reviews TO anon, authenticated;
GRANT SELECT, INSERT ON error_reports TO anon, authenticated;
GRANT SELECT ON system_settings WHERE is_public = true TO anon, authenticated;

-- ========================================
-- إدراج بيانات أولية
-- Insert Initial Data
-- ========================================

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_version', '2.0.0', 'string', 'Current app version', true),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', false),
('max_download_size', '100', 'number', 'Maximum download size in MB', true),
('notification_enabled', 'true', 'boolean', 'Enable push notifications', false),
('analytics_enabled', 'true', 'boolean', 'Enable analytics tracking', false),
('auto_backup_enabled', 'true', 'boolean', 'Enable automatic backups', false),
('backup_retention_days', '30', 'number', 'Number of days to keep backups', false)
ON CONFLICT (setting_key) DO NOTHING;

-- ========================================
-- التحقق من نجاح العملية
-- Verify Success
-- ========================================

SELECT 
    schemaname, 
    tablename,
    'Table created successfully' as status
FROM pg_tables 
WHERE tablename IN (
    'user_statistics', 'download_analytics', 'view_analytics', 
    'admin_notifications', 'user_notifications_log', 'banned_users',
    'mod_reviews', 'admin_activity_log', 'system_settings', 'error_reports'
)
ORDER BY tablename;
