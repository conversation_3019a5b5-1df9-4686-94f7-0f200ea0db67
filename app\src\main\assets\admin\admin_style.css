/* أنماط عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #121212;
    color: #ffffff;
    line-height: 1.6;
    padding: 20px;
}

.admin-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #1e1e1e;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

h1 {
    color: #ffcc00;
    margin-bottom: 10px;
    text-align: center;
    font-size: 2rem;
}

h2 {
    color: #ffcc00;
    margin-bottom: 15px;
    font-size: 1.5rem;
    border-bottom: 2px solid #333;
    padding-bottom: 5px;
}

h3 {
    color: #ffffff;
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.description {
    color: #aaaaaa;
    margin-bottom: 30px;
    text-align: center;
}

.section {
    margin-bottom: 30px;
    background-color: #252525;
    padding: 20px;
    border-radius: 8px;
}

/* أنماط القائمة الرئيسية */
.admin-menu {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.admin-menu-item {
    display: flex;
    align-items: center;
    background-color: #252525;
    padding: 20px;
    border-radius: 8px;
    text-decoration: none;
    color: #ffffff;
    transition: transform 0.2s, box-shadow 0.2s;
}

.admin-menu-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    background-color: #2a2a2a;
}

.admin-menu-icon {
    font-size: 2rem;
    margin-left: 15px;
    background-color: #333;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.admin-menu-text {
    flex: 1;
}

.admin-menu-text p {
    color: #aaaaaa;
    font-size: 0.9rem;
}

/* أنماط النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.input-field, .textarea-field, .select-field {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #444;
    background-color: #333;
    color: #ffffff;
    font-size: 1rem;
}

.textarea-field {
    min-height: 100px;
    resize: vertical;
}

.input-field:focus, .textarea-field:focus, .select-field:focus {
    outline: none;
    border-color: #ffcc00;
}

.button {
    padding: 10px 20px;
    background-color: #ffcc00;
    color: #000000;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: background-color 0.2s;
}

.button:hover {
    background-color: #ffd700;
}

.button.danger {
    background-color: #f44336;
    color: #ffffff;
}

.button.danger:hover {
    background-color: #ff5252;
}

/* أنماط الرسائل */
.success-message, .error-message, .info-message {
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.success-message {
    background-color: #4caf50;
    color: #ffffff;
}

.error-message {
    background-color: #f44336;
    color: #ffffff;
}

.info-message {
    background-color: #2196f3;
    color: #ffffff;
}

/* أنماط القائمة السفلية */
.admin-footer {
    margin-top: 30px;
    text-align: center;
}

.back-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #333;
    color: #ffffff;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.back-button:hover {
    background-color: #444;
}

/* أنماط الجداول */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.admin-table th, .admin-table td {
    padding: 10px;
    text-align: right;
    border-bottom: 1px solid #333;
}

.admin-table th {
    background-color: #333;
    color: #ffcc00;
}

.admin-table tr:hover {
    background-color: #2a2a2a;
}

/* أنماط الصور */
.image-preview {
    max-width: 100%;
    height: auto;
    margin-top: 10px;
    border-radius: 5px;
}

/* أنماط التحميل */
.loading-spinner {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffcc00;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* أنماط الاستجابة */
@media (max-width: 768px) {
    .admin-menu {
        grid-template-columns: 1fr;
    }
    
    .admin-container {
        padding: 15px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    h2 {
        font-size: 1.3rem;
    }
}
