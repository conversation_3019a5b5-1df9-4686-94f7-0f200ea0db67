// Advanced Media and File Management System
// نظام إدارة الملفات والوسائط المتقدم

// Global variables
let mediaFiles = [];
let selectedFiles = [];
let currentFolder = '/';
let mediaSettings = {
    maxFileSize: 100, // MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/zip'],
    autoCompress: true,
    compressionQuality: 0.8,
    generateThumbnails: true
};

let uploadQueue = [];
let isUploading = false;

// Initialize media manager
document.addEventListener('DOMContentLoaded', function() {
    console.log('Media Manager loaded');
    initializeMediaManager();
});

// Initialize media manager
async function initializeMediaManager() {
    try {
        // Load media settings
        loadMediaSettings();
        
        // Load media files
        await loadMediaFiles();
        
        // Setup media UI
        setupMediaUI();
        
        // Setup drag and drop
        setupDragAndDrop();
        
        // Setup file upload
        setupFileUpload();
        
        console.log('✅ Media manager initialized');
    } catch (error) {
        console.error('Error initializing media manager:', error);
    }
}

// Load media settings
function loadMediaSettings() {
    try {
        const savedSettings = localStorage.getItem('mediaSettings');
        if (savedSettings) {
            mediaSettings = { ...mediaSettings, ...JSON.parse(savedSettings) };
        }
    } catch (error) {
        console.error('Error loading media settings:', error);
    }
}

// Save media settings
function saveMediaSettings() {
    try {
        localStorage.setItem('mediaSettings', JSON.stringify(mediaSettings));
    } catch (error) {
        console.error('Error saving media settings:', error);
    }
}

// Load media files
async function loadMediaFiles() {
    try {
        // Load from localStorage (in real app, this would be from server/database)
        const savedFiles = localStorage.getItem('mediaFiles');
        if (savedFiles) {
            mediaFiles = JSON.parse(savedFiles);
        } else {
            // Generate sample media files
            mediaFiles = generateSampleMediaFiles();
            saveMediaFiles();
        }
        
        // Update media display
        updateMediaDisplay();
        
    } catch (error) {
        console.error('Error loading media files:', error);
    }
}

// Save media files
function saveMediaFiles() {
    try {
        localStorage.setItem('mediaFiles', JSON.stringify(mediaFiles));
    } catch (error) {
        console.error('Error saving media files:', error);
    }
}

// Generate sample media files
function generateSampleMediaFiles() {
    const files = [];
    const fileTypes = ['image/jpeg', 'image/png', 'application/zip'];
    const folders = ['/', '/mods/', '/images/', '/thumbnails/'];
    
    for (let i = 0; i < 20; i++) {
        const type = fileTypes[Math.floor(Math.random() * fileTypes.length)];
        const folder = folders[Math.floor(Math.random() * folders.length)];
        const extension = type.includes('image') ? (type.includes('jpeg') ? '.jpg' : '.png') : '.zip';
        
        files.push({
            id: `file_${Date.now()}_${i}`,
            name: `sample_file_${i}${extension}`,
            path: folder,
            type: type,
            size: Math.floor(Math.random() * 50 * 1024 * 1024), // 0-50MB
            uploadDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
            uploadedBy: 'admin_001',
            thumbnail: type.includes('image') ? `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><rect width="100" height="100" fill="%23${Math.floor(Math.random()*16777215).toString(16)}"/></svg>` : null,
            downloads: Math.floor(Math.random() * 1000),
            isPublic: Math.random() > 0.3
        });
    }
    
    return files;
}

// Setup media UI
function setupMediaUI() {
    // Setup folder navigation
    setupFolderNavigation();
    
    // Setup file actions
    setupFileActions();
    
    // Setup media settings
    setupMediaSettingsUI();
    
    // Setup search and filters
    setupSearchAndFilters();
}

// Setup folder navigation
function setupFolderNavigation() {
    const folderNav = document.getElementById('folder-navigation');
    if (!folderNav) return;
    
    const folders = [...new Set(mediaFiles.map(file => file.path))].sort();
    
    folderNav.innerHTML = folders.map(folder => `
        <button class="folder-btn ${folder === currentFolder ? 'active' : ''}" 
                onclick="navigateToFolder('${folder}')">
            <i class="fas fa-folder"></i>
            ${folder === '/' ? 'الجذر' : folder.replace(/\//g, '')}
        </button>
    `).join('');
}

// Navigate to folder
function navigateToFolder(folder) {
    currentFolder = folder;
    updateMediaDisplay();
    setupFolderNavigation();
}

// Setup file actions
function setupFileActions() {
    // Select all button
    const selectAllBtn = document.getElementById('select-all-files');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', selectAllFiles);
    }
    
    // Delete selected button
    const deleteSelectedBtn = document.getElementById('delete-selected-files');
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', deleteSelectedFiles);
    }
    
    // Create folder button
    const createFolderBtn = document.getElementById('create-folder-btn');
    if (createFolderBtn) {
        createFolderBtn.addEventListener('click', createNewFolder);
    }
}

// Setup media settings UI
function setupMediaSettingsUI() {
    // Max file size
    const maxFileSizeInput = document.getElementById('max-file-size');
    if (maxFileSizeInput) {
        maxFileSizeInput.value = mediaSettings.maxFileSize;
        maxFileSizeInput.addEventListener('change', (e) => {
            mediaSettings.maxFileSize = parseInt(e.target.value);
            saveMediaSettings();
        });
    }
    
    // Auto compress toggle
    const autoCompressToggle = document.getElementById('auto-compress');
    if (autoCompressToggle) {
        autoCompressToggle.checked = mediaSettings.autoCompress;
        autoCompressToggle.addEventListener('change', (e) => {
            mediaSettings.autoCompress = e.target.checked;
            saveMediaSettings();
        });
    }
    
    // Compression quality
    const compressionQualityInput = document.getElementById('compression-quality');
    if (compressionQualityInput) {
        compressionQualityInput.value = mediaSettings.compressionQuality;
        compressionQualityInput.addEventListener('change', (e) => {
            mediaSettings.compressionQuality = parseFloat(e.target.value);
            saveMediaSettings();
        });
    }
}

// Setup search and filters
function setupSearchAndFilters() {
    // Search input
    const searchInput = document.getElementById('media-search');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            filterMediaFiles(e.target.value);
        });
    }
    
    // Type filter
    const typeFilter = document.getElementById('media-type-filter');
    if (typeFilter) {
        typeFilter.addEventListener('change', (e) => {
            filterMediaByType(e.target.value);
        });
    }
    
    // Sort options
    const sortSelect = document.getElementById('media-sort');
    if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
            sortMediaFiles(e.target.value);
        });
    }
}

// Setup drag and drop
function setupDragAndDrop() {
    const dropZone = document.getElementById('media-drop-zone');
    if (!dropZone) return;
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('drag-over');
    });
    
    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
    });
    
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        handleFileUpload(files);
    });
}

// Setup file upload
function setupFileUpload() {
    const fileInput = document.getElementById('media-file-input');
    const uploadBtn = document.getElementById('upload-files-btn');
    
    if (uploadBtn && fileInput) {
        uploadBtn.addEventListener('click', () => {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            handleFileUpload(files);
            e.target.value = ''; // Reset input
        });
    }
}

// Handle file upload
async function handleFileUpload(files) {
    // Validate files
    const validFiles = files.filter(file => validateFile(file));
    
    if (validFiles.length === 0) {
        alert('لا توجد ملفات صالحة للرفع');
        return;
    }
    
    // Add to upload queue
    validFiles.forEach(file => {
        uploadQueue.push({
            id: `upload_${Date.now()}_${Math.random().toString(36).substring(2)}`,
            file: file,
            progress: 0,
            status: 'pending'
        });
    });
    
    // Start upload process
    if (!isUploading) {
        processUploadQueue();
    }
    
    // Update upload UI
    updateUploadQueueDisplay();
}

// Validate file
function validateFile(file) {
    // Check file size
    if (file.size > mediaSettings.maxFileSize * 1024 * 1024) {
        alert(`الملف ${file.name} كبير جداً. الحد الأقصى ${mediaSettings.maxFileSize}MB`);
        return false;
    }
    
    // Check file type
    if (!mediaSettings.allowedTypes.includes(file.type)) {
        alert(`نوع الملف ${file.name} غير مدعوم`);
        return false;
    }
    
    return true;
}

// Process upload queue
async function processUploadQueue() {
    if (isUploading || uploadQueue.length === 0) return;
    
    isUploading = true;
    
    while (uploadQueue.length > 0) {
        const uploadItem = uploadQueue[0];
        
        try {
            await uploadFile(uploadItem);
            uploadQueue.shift(); // Remove completed upload
        } catch (error) {
            console.error('Upload error:', error);
            uploadItem.status = 'error';
            uploadItem.error = error.message;
            uploadQueue.shift(); // Remove failed upload
        }
        
        updateUploadQueueDisplay();
    }
    
    isUploading = false;
}

// Upload file
async function uploadFile(uploadItem) {
    return new Promise((resolve, reject) => {
        uploadItem.status = 'uploading';
        
        // Simulate upload progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            uploadItem.progress = Math.min(progress, 100);
            
            updateUploadQueueDisplay();
            
            if (progress >= 100) {
                clearInterval(interval);
                
                // Create media file entry
                const mediaFile = {
                    id: `file_${Date.now()}_${Math.random().toString(36).substring(2)}`,
                    name: uploadItem.file.name,
                    path: currentFolder,
                    type: uploadItem.file.type,
                    size: uploadItem.file.size,
                    uploadDate: new Date().toISOString(),
                    uploadedBy: getCurrentUserId(),
                    thumbnail: null,
                    downloads: 0,
                    isPublic: true
                };
                
                // Generate thumbnail for images
                if (uploadItem.file.type.startsWith('image/')) {
                    generateThumbnail(uploadItem.file).then(thumbnail => {
                        mediaFile.thumbnail = thumbnail;
                        saveMediaFiles();
                        updateMediaDisplay();
                    });
                }
                
                // Add to media files
                mediaFiles.unshift(mediaFile);
                saveMediaFiles();
                updateMediaDisplay();
                
                uploadItem.status = 'completed';
                resolve(mediaFile);
            }
        }, 100);
    });
}

// Generate thumbnail
function generateThumbnail(file) {
    return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = new Image();
            img.onload = function() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // Set thumbnail size
                const maxSize = 150;
                let { width, height } = img;
                
                if (width > height) {
                    if (width > maxSize) {
                        height = (height * maxSize) / width;
                        width = maxSize;
                    }
                } else {
                    if (height > maxSize) {
                        width = (width * maxSize) / height;
                        height = maxSize;
                    }
                }
                
                canvas.width = width;
                canvas.height = height;
                
                ctx.drawImage(img, 0, 0, width, height);
                resolve(canvas.toDataURL('image/jpeg', 0.8));
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    });
}

// Update upload queue display
function updateUploadQueueDisplay() {
    const queueContainer = document.getElementById('upload-queue');
    if (!queueContainer) return;
    
    queueContainer.innerHTML = uploadQueue.map(item => `
        <div class="upload-item ${item.status}">
            <div class="upload-info">
                <span class="upload-name">${item.file.name}</span>
                <span class="upload-size">${formatFileSize(item.file.size)}</span>
            </div>
            <div class="upload-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${item.progress}%"></div>
                </div>
                <span class="progress-text">${Math.round(item.progress)}%</span>
            </div>
            <div class="upload-status">
                ${getUploadStatusIcon(item.status)}
            </div>
        </div>
    `).join('');
}

// Get upload status icon
function getUploadStatusIcon(status) {
    const icons = {
        'pending': '<i class="fas fa-clock"></i>',
        'uploading': '<i class="fas fa-spinner fa-spin"></i>',
        'completed': '<i class="fas fa-check text-success"></i>',
        'error': '<i class="fas fa-times text-error"></i>'
    };
    return icons[status] || '';
}

// Update media display
function updateMediaDisplay() {
    const mediaContainer = document.getElementById('media-files-grid');
    if (!mediaContainer) return;
    
    const currentFiles = mediaFiles.filter(file => file.path === currentFolder);
    
    mediaContainer.innerHTML = currentFiles.map(file => `
        <div class="media-file-card ${selectedFiles.includes(file.id) ? 'selected' : ''}" 
             data-file-id="${file.id}">
            <div class="file-thumbnail">
                ${file.thumbnail ? 
                    `<img src="${file.thumbnail}" alt="${file.name}">` : 
                    `<i class="fas fa-${getFileIcon(file.type)}"></i>`
                }
            </div>
            <div class="file-info">
                <div class="file-name" title="${file.name}">${file.name}</div>
                <div class="file-meta">
                    <span class="file-size">${formatFileSize(file.size)}</span>
                    <span class="file-date">${new Date(file.uploadDate).toLocaleDateString('ar-SA')}</span>
                </div>
                <div class="file-stats">
                    <span class="file-downloads">
                        <i class="fas fa-download"></i> ${file.downloads}
                    </span>
                    <span class="file-visibility">
                        <i class="fas fa-${file.isPublic ? 'eye' : 'eye-slash'}"></i>
                    </span>
                </div>
            </div>
            <div class="file-actions">
                <button class="file-action-btn" onclick="previewFile('${file.id}')" title="معاينة">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="file-action-btn" onclick="downloadFile('${file.id}')" title="تحميل">
                    <i class="fas fa-download"></i>
                </button>
                <button class="file-action-btn" onclick="editFile('${file.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="file-action-btn danger" onclick="deleteFile('${file.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="file-checkbox">
                <input type="checkbox" onchange="toggleFileSelection('${file.id}', this.checked)">
            </div>
        </div>
    `).join('');
    
    // Update file count
    updateFileCount(currentFiles.length);
}

// Get file icon
function getFileIcon(type) {
    if (type.startsWith('image/')) return 'image';
    if (type.includes('zip')) return 'file-archive';
    if (type.includes('pdf')) return 'file-pdf';
    if (type.includes('video/')) return 'video';
    if (type.includes('audio/')) return 'music';
    return 'file';
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Update file count
function updateFileCount(count) {
    const countElement = document.getElementById('files-count');
    if (countElement) {
        countElement.textContent = `${count} ملف`;
    }
}

// Get current user ID
function getCurrentUserId() {
    return localStorage.getItem('currentUserId') || 'admin_001';
}

// Preview file
function previewFile(fileId) {
    const file = mediaFiles.find(f => f.id === fileId);
    if (!file) return;

    const modal = document.createElement('div');
    modal.className = 'file-preview-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3><i class="fas fa-eye"></i> معاينة الملف</h3>
                <button class="close-btn" onclick="this.closest('.file-preview-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="file-preview-content">
                    ${file.type.startsWith('image/') ?
                        `<img src="${file.thumbnail || 'placeholder.jpg'}" alt="${file.name}" class="preview-image">` :
                        `<div class="preview-placeholder">
                            <i class="fas fa-${getFileIcon(file.type)} fa-5x"></i>
                            <p>معاينة غير متاحة لهذا النوع من الملفات</p>
                        </div>`
                    }
                </div>
                <div class="file-details">
                    <div class="detail-row">
                        <label>اسم الملف:</label>
                        <span>${file.name}</span>
                    </div>
                    <div class="detail-row">
                        <label>الحجم:</label>
                        <span>${formatFileSize(file.size)}</span>
                    </div>
                    <div class="detail-row">
                        <label>النوع:</label>
                        <span>${file.type}</span>
                    </div>
                    <div class="detail-row">
                        <label>تاريخ الرفع:</label>
                        <span>${new Date(file.uploadDate).toLocaleString('ar-SA')}</span>
                    </div>
                    <div class="detail-row">
                        <label>رفع بواسطة:</label>
                        <span>${file.uploadedBy}</span>
                    </div>
                    <div class="detail-row">
                        <label>عدد التحميلات:</label>
                        <span>${file.downloads}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// Download file
function downloadFile(fileId) {
    const file = mediaFiles.find(f => f.id === fileId);
    if (!file) return;

    // Increment download count
    file.downloads++;
    saveMediaFiles();
    updateMediaDisplay();

    // Simulate download (in real app, this would trigger actual download)
    console.log(`Downloading file: ${file.name}`);
    alert(`تم بدء تحميل الملف: ${file.name}`);
}

// Edit file
function editFile(fileId) {
    const file = mediaFiles.find(f => f.id === fileId);
    if (!file) return;

    const modal = document.createElement('div');
    modal.className = 'file-edit-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3><i class="fas fa-edit"></i> تعديل الملف</h3>
                <button class="close-btn" onclick="this.closest('.file-edit-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="file-edit-form" onsubmit="saveFileChanges('${fileId}', event)">
                    <div class="form-group">
                        <label>اسم الملف:</label>
                        <input type="text" id="edit-file-name" value="${file.name}" required>
                    </div>
                    <div class="form-group">
                        <label>المجلد:</label>
                        <select id="edit-file-path">
                            ${[...new Set(mediaFiles.map(f => f.path))].map(path =>
                                `<option value="${path}" ${path === file.path ? 'selected' : ''}>${path}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="edit-file-public" ${file.isPublic ? 'checked' : ''}>
                            ملف عام (مرئي للجميع)
                        </label>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="primary-btn">حفظ التغييرات</button>
                        <button type="button" class="secondary-btn" onclick="this.closest('.file-edit-modal').remove()">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// Save file changes
function saveFileChanges(fileId, event) {
    event.preventDefault();

    const file = mediaFiles.find(f => f.id === fileId);
    if (!file) return;

    const newName = document.getElementById('edit-file-name').value;
    const newPath = document.getElementById('edit-file-path').value;
    const isPublic = document.getElementById('edit-file-public').checked;

    file.name = newName;
    file.path = newPath;
    file.isPublic = isPublic;

    saveMediaFiles();
    updateMediaDisplay();

    // Close modal
    document.querySelector('.file-edit-modal').remove();

    console.log(`File updated: ${fileId}`);
}

// Delete file
function deleteFile(fileId) {
    const file = mediaFiles.find(f => f.id === fileId);
    if (!file) return;

    if (confirm(`هل أنت متأكد من حذف الملف "${file.name}"؟`)) {
        mediaFiles = mediaFiles.filter(f => f.id !== fileId);
        selectedFiles = selectedFiles.filter(id => id !== fileId);

        saveMediaFiles();
        updateMediaDisplay();

        console.log(`File deleted: ${fileId}`);
    }
}

// Toggle file selection
function toggleFileSelection(fileId, selected) {
    if (selected) {
        if (!selectedFiles.includes(fileId)) {
            selectedFiles.push(fileId);
        }
    } else {
        selectedFiles = selectedFiles.filter(id => id !== fileId);
    }

    updateSelectionUI();
}

// Select all files
function selectAllFiles() {
    const currentFiles = mediaFiles.filter(file => file.path === currentFolder);
    selectedFiles = currentFiles.map(file => file.id);

    // Update checkboxes
    document.querySelectorAll('.file-checkbox input').forEach(checkbox => {
        checkbox.checked = true;
    });

    updateSelectionUI();
}

// Delete selected files
function deleteSelectedFiles() {
    if (selectedFiles.length === 0) {
        alert('لم يتم اختيار أي ملفات');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${selectedFiles.length} ملف؟`)) {
        mediaFiles = mediaFiles.filter(file => !selectedFiles.includes(file.id));
        selectedFiles = [];

        saveMediaFiles();
        updateMediaDisplay();
        updateSelectionUI();

        console.log('Selected files deleted');
    }
}

// Create new folder
function createNewFolder() {
    const folderName = prompt('اسم المجلد الجديد:');
    if (!folderName) return;

    const newFolderPath = currentFolder === '/' ? `/${folderName}/` : `${currentFolder}${folderName}/`;

    // Check if folder already exists
    const existingFolders = [...new Set(mediaFiles.map(f => f.path))];
    if (existingFolders.includes(newFolderPath)) {
        alert('المجلد موجود بالفعل');
        return;
    }

    // Create a placeholder file to represent the folder
    const placeholderFile = {
        id: `folder_${Date.now()}`,
        name: '.folder',
        path: newFolderPath,
        type: 'folder',
        size: 0,
        uploadDate: new Date().toISOString(),
        uploadedBy: getCurrentUserId(),
        thumbnail: null,
        downloads: 0,
        isPublic: true
    };

    mediaFiles.push(placeholderFile);
    saveMediaFiles();
    setupFolderNavigation();

    console.log(`Folder created: ${newFolderPath}`);
}

// Update selection UI
function updateSelectionUI() {
    const selectedCount = selectedFiles.length;
    const selectionInfo = document.getElementById('selection-info');
    const deleteSelectedBtn = document.getElementById('delete-selected-files');

    if (selectionInfo) {
        selectionInfo.textContent = selectedCount > 0 ? `تم اختيار ${selectedCount} ملف` : '';
    }

    if (deleteSelectedBtn) {
        deleteSelectedBtn.disabled = selectedCount === 0;
    }

    // Update file cards
    document.querySelectorAll('.media-file-card').forEach(card => {
        const fileId = card.getAttribute('data-file-id');
        if (selectedFiles.includes(fileId)) {
            card.classList.add('selected');
        } else {
            card.classList.remove('selected');
        }
    });
}

// Filter media files
function filterMediaFiles(searchTerm) {
    const filteredFiles = mediaFiles.filter(file =>
        file.path === currentFolder &&
        file.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    displayFilteredFiles(filteredFiles);
}

// Filter media by type
function filterMediaByType(type) {
    let filteredFiles = mediaFiles.filter(file => file.path === currentFolder);

    if (type !== 'all') {
        filteredFiles = filteredFiles.filter(file => file.type.startsWith(type));
    }

    displayFilteredFiles(filteredFiles);
}

// Sort media files
function sortMediaFiles(sortBy) {
    let sortedFiles = [...mediaFiles.filter(file => file.path === currentFolder)];

    switch (sortBy) {
        case 'name':
            sortedFiles.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'date':
            sortedFiles.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));
            break;
        case 'size':
            sortedFiles.sort((a, b) => b.size - a.size);
            break;
        case 'downloads':
            sortedFiles.sort((a, b) => b.downloads - a.downloads);
            break;
    }

    displayFilteredFiles(sortedFiles);
}

// Display filtered files
function displayFilteredFiles(files) {
    const mediaContainer = document.getElementById('media-files-grid');
    if (!mediaContainer) return;

    mediaContainer.innerHTML = files.map(file => `
        <div class="media-file-card ${selectedFiles.includes(file.id) ? 'selected' : ''}"
             data-file-id="${file.id}">
            <div class="file-thumbnail">
                ${file.thumbnail ?
                    `<img src="${file.thumbnail}" alt="${file.name}">` :
                    `<i class="fas fa-${getFileIcon(file.type)}"></i>`
                }
            </div>
            <div class="file-info">
                <div class="file-name" title="${file.name}">${file.name}</div>
                <div class="file-meta">
                    <span class="file-size">${formatFileSize(file.size)}</span>
                    <span class="file-date">${new Date(file.uploadDate).toLocaleDateString('ar-SA')}</span>
                </div>
                <div class="file-stats">
                    <span class="file-downloads">
                        <i class="fas fa-download"></i> ${file.downloads}
                    </span>
                    <span class="file-visibility">
                        <i class="fas fa-${file.isPublic ? 'eye' : 'eye-slash'}"></i>
                    </span>
                </div>
            </div>
            <div class="file-actions">
                <button class="file-action-btn" onclick="previewFile('${file.id}')" title="معاينة">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="file-action-btn" onclick="downloadFile('${file.id}')" title="تحميل">
                    <i class="fas fa-download"></i>
                </button>
                <button class="file-action-btn" onclick="editFile('${file.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="file-action-btn danger" onclick="deleteFile('${file.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="file-checkbox">
                <input type="checkbox" onchange="toggleFileSelection('${file.id}', this.checked)">
            </div>
        </div>
    `).join('');

    updateFileCount(files.length);
}

// Export media list
function exportMediaList() {
    const exportData = {
        timestamp: new Date().toISOString(),
        totalFiles: mediaFiles.length,
        totalSize: mediaFiles.reduce((sum, file) => sum + file.size, 0),
        files: mediaFiles.map(file => ({
            name: file.name,
            path: file.path,
            type: file.type,
            size: file.size,
            uploadDate: file.uploadDate,
            downloads: file.downloads,
            isPublic: file.isPublic
        }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `media-list-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
}

// Get media statistics
function getMediaStatistics() {
    const stats = {
        totalFiles: mediaFiles.length,
        totalSize: mediaFiles.reduce((sum, file) => sum + file.size, 0),
        totalDownloads: mediaFiles.reduce((sum, file) => sum + file.downloads, 0),
        fileTypes: {},
        folders: [...new Set(mediaFiles.map(f => f.path))].length
    };

    // Count file types
    mediaFiles.forEach(file => {
        const type = file.type.split('/')[0];
        stats.fileTypes[type] = (stats.fileTypes[type] || 0) + 1;
    });

    return stats;
}

// Make functions globally available
window.navigateToFolder = navigateToFolder;
window.previewFile = previewFile;
window.downloadFile = downloadFile;
window.editFile = editFile;
window.deleteFile = deleteFile;
window.toggleFileSelection = toggleFileSelection;
window.selectAllFiles = selectAllFiles;
window.deleteSelectedFiles = deleteSelectedFiles;
window.createNewFolder = createNewFolder;
window.saveFileChanges = saveFileChanges;
window.exportMediaList = exportMediaList;
window.getMediaStatistics = getMediaStatistics;
