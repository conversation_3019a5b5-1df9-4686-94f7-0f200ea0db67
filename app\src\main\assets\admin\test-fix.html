<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار إصلاح supabaseClient</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار إصلاح مشكلة supabaseClient</h1>
        
        <div id="test-results"></div>
        
        <button onclick="runTests()">🚀 تشغيل الاختبارات</button>
        <button onclick="clearResults()">🧹 مسح النتائج</button>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function runTests() {
            clearResults();
            addResult('🚀 بدء الاختبارات...', 'info');

            // Test 1: Check if Supabase library is loaded
            try {
                if (typeof supabase !== 'undefined') {
                    addResult('✅ تم تحميل مكتبة Supabase بنجاح', 'success');
                } else {
                    addResult('❌ مكتبة Supabase غير محملة', 'error');
                    return;
                }
            } catch (error) {
                addResult('❌ خطأ في فحص مكتبة Supabase: ' + error.message, 'error');
                return;
            }

            // Test 2: Check if config.js variables are loaded
            try {
                if (typeof supabaseClient !== 'undefined') {
                    addResult('✅ متغير supabaseClient محمل من config.js', 'success');
                } else {
                    addResult('❌ متغير supabaseClient غير محمل', 'error');
                    return;
                }

                if (typeof SUPABASE_URL !== 'undefined') {
                    addResult('✅ متغير SUPABASE_URL محمل', 'success');
                } else {
                    addResult('❌ متغير SUPABASE_URL غير محمل', 'error');
                }

                if (typeof SUPABASE_ANON_KEY !== 'undefined') {
                    addResult('✅ متغير SUPABASE_ANON_KEY محمل', 'success');
                } else {
                    addResult('❌ متغير SUPABASE_ANON_KEY غير محمل', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في فحص متغيرات config.js: ' + error.message, 'error');
                return;
            }

            // Test 3: Test Supabase connection
            try {
                addResult('🔄 اختبار الاتصال بـ Supabase...', 'info');
                const { data, error } = await supabaseClient.from('mods').select('count', { count: 'exact', head: true });
                
                if (error) {
                    addResult('❌ خطأ في الاتصال بـ Supabase: ' + error.message, 'error');
                } else {
                    addResult('✅ تم الاتصال بـ Supabase بنجاح', 'success');
                    addResult(`📊 عدد المودات في قاعدة البيانات: ${data || 0}`, 'info');
                }
            } catch (error) {
                addResult('❌ خطأ في اختبار الاتصال: ' + error.message, 'error');
            }

            // Test 4: Test custom_sections table
            try {
                addResult('🔄 اختبار جدول custom_sections...', 'info');
                const { data, error } = await supabaseClient.from('custom_sections').select('count', { count: 'exact', head: true });
                
                if (error) {
                    if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
                        addResult('⚠️ جدول custom_sections غير موجود - يحتاج إلى إنشاء', 'error');
                    } else {
                        addResult('❌ خطأ في الوصول لجدول custom_sections: ' + error.message, 'error');
                    }
                } else {
                    addResult('✅ جدول custom_sections موجود ويعمل بشكل صحيح', 'success');
                    addResult(`📊 عدد الأقسام المخصصة: ${data || 0}`, 'info');
                }
            } catch (error) {
                addResult('❌ خطأ في اختبار جدول custom_sections: ' + error.message, 'error');
            }

            addResult('🏁 انتهت الاختبارات', 'info');
        }

        // Run tests automatically when page loads
        window.addEventListener('load', function() {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
