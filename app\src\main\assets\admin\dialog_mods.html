<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة مودات المربع</title>
    <link rel="stylesheet" href="admin_style.css">
    <style>
        .dialog-info {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #ffcc00;
        }

        .dialog-info h2 {
            color: #ffcc00;
            margin-bottom: 10px;
        }

        .search-section {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #ffcc00;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #444;
            border-radius: 8px;
            background: #1a1a2e;
            color: white;
            font-size: 1rem;
            margin-bottom: 15px;
        }

        .search-box:focus {
            outline: none;
            border-color: #ffcc00;
        }

        .mods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .mod-item {
            background: #1a1a2e;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #444;
            cursor: pointer;
            transition: all 0.3s;
        }

        .mod-item:hover {
            border-color: #ffcc00;
            transform: translateY(-2px);
        }

        .mod-item.selected {
            border-color: #22c55e;
            background: rgba(34, 197, 94, 0.1);
        }

        .mod-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .mod-name {
            color: #ffcc00;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .mod-stats {
            color: #888;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .mod-category {
            display: inline-block;
            background: #ffcc00;
            color: #000;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .selected-mods {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #22c55e;
        }

        .selected-mod-item {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid #22c55e;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .selected-mod-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
        }

        .selected-mod-info {
            flex: 1;
        }

        .selected-mod-name {
            color: #ffcc00;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .remove-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .remove-btn:hover {
            background: #dc2626;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffcc00, #ff9800);
            color: #000;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #666, #888);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .loading {
            text-align: center;
            color: #ffcc00;
            font-size: 1.2rem;
            padding: 20px;
        }

        .error-message {
            background: #ef4444;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success-message {
            background: #22c55e;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #444;
            background: #1a1a2e;
            color: white;
            border-radius: 6px;
            cursor: pointer;
        }

        .pagination button:hover {
            background: #ffcc00;
            color: #000;
        }

        .pagination button.active {
            background: #ffcc00;
            color: #000;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>إدارة مودات المربع</h1>
            <p>تحديد المودات التي سيظهر لها المربع المخصص</p>
        </div>

        <div id="messageContainer"></div>

        <!-- معلومات المربع -->
        <div class="dialog-info">
            <h2 id="dialogTitle">جاري التحميل...</h2>
            <p id="dialogDescription"></p>
            <div style="margin-top: 15px;">
                <button class="btn btn-secondary" onclick="window.close()">العودة</button>
                <button class="btn btn-primary" onclick="saveSelectedMods()">حفظ التغييرات</button>
            </div>
        </div>

        <!-- المودات المحددة -->
        <div class="selected-mods">
            <h3>المودات المحددة (<span id="selectedCount">0</span>)</h3>
            <div id="selectedModsList">
                <p style="color: #888; text-align: center;">لم يتم تحديد أي مودات بعد</p>
            </div>
        </div>

        <!-- البحث والتصفية -->
        <div class="search-section">
            <h3>البحث عن المودات</h3>
            <input type="text" id="searchBox" class="search-box" placeholder="ابحث عن المودات بالاسم...">
            <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                <select id="categoryFilter" style="padding: 8px; border-radius: 6px; background: #1a1a2e; color: white; border: 1px solid #444;">
                    <option value="">جميع الفئات</option>
                    <option value="Addons">Addons</option>
                    <option value="Shaders">Shaders</option>
                    <option value="Texture">Texture Pack</option>
                    <option value="Maps">Maps</option>
                    <option value="Seeds">Seeds</option>
                </select>
                <button class="btn btn-secondary" onclick="loadMods()">تحديث</button>
            </div>
        </div>

        <!-- قائمة المودات -->
        <div class="mods-grid" id="modsGrid">
            <div class="loading">جاري تحميل المودات...</div>
        </div>

        <!-- ترقيم الصفحات -->
        <div class="pagination" id="pagination"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // تحقق من تحميل Supabase
        if (typeof supabase === 'undefined') {
            console.error('❌ Supabase library not loaded');
            document.getElementById('modsGrid').innerHTML = '<div class="error-message">خطأ: مكتبة Supabase غير محملة</div>';
        } else {
            console.log('✅ Supabase library loaded successfully');
        }
    </script>
    <script src="../supabase-manager.js" onerror="console.error('❌ Failed to load supabase-manager.js')"></script>
    <script>
        // تحقق من تحميل Supabase Manager
        setTimeout(() => {
            if (typeof window.supabaseManager === 'undefined') {
                console.error('❌ Supabase Manager not loaded after 1 second');
                document.getElementById('modsGrid').innerHTML = '<div class="error-message">خطأ: Supabase Manager غير محمل. تحقق من مسار الملف.</div>';
            } else {
                console.log('✅ Supabase Manager loaded successfully');
            }
        }, 1000);
    </script>
    <script src="dialog_mods.js"></script>
</body>
</html>
