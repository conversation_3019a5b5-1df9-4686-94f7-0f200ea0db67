<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإعلانات الاحتياطية</title>
    <link rel="stylesheet" href="unified-admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="backup-ads-config.js"></script>
    <style>
        .backup-ad-card {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #3a5998;
            transition: all 0.3s ease;
        }

        .backup-ad-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .ad-preview {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
        }

        .ad-media {
            width: 120px;
            height: 80px;
            border-radius: 10px;
            overflow: hidden;
            background: #000;
        }

        .ad-media img, .ad-media video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .ad-info h4 {
            color: #ffd700;
            margin: 0 0 5px 0;
            font-size: 1.2rem;
        }

        .ad-info p {
            color: #e0e0e0;
            margin: 0;
            font-size: 0.9rem;
        }

        .ad-meta {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .ad-meta span {
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8rem;
            color: #fff;
        }

        .ad-type-image {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }

        .ad-type-video {
            background: linear-gradient(45deg, #f44336, #da190b);
        }

        .form-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .media-upload-area {
            border: 2px dashed #4CAF50;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: rgba(76, 175, 80, 0.1);
            transition: all 0.3s ease;
        }

        .media-upload-area:hover {
            border-color: #45a049;
            background: rgba(76, 175, 80, 0.2);
        }

        .media-upload-area.dragover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: #4CAF50;
            margin-bottom: 15px;
        }

        .media-preview {
            max-width: 300px;
            max-height: 200px;
            border-radius: 10px;
            margin: 15px auto;
            display: none;
        }

        .priority-slider {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .priority-slider:hover {
            opacity: 1;
        }

        .priority-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ffd700;
            cursor: pointer;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <h1><i class="fas fa-shield-alt"></i> إدارة الإعلانات الاحتياطية</h1>
                <div class="header-actions">
                    <button onclick="window.location.href='index.html'" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة الرئيسية
                    </button>
                </div>
            </div>
        </header>

        <!-- Stats Section -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalBackupAds">0</div>
                    <div class="stat-label">إجمالي الإعلانات الاحتياطية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeBackupAds">0</div>
                    <div class="stat-label">الإعلانات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="imageAds">0</div>
                    <div class="stat-label">إعلانات الصور</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="videoAds">0</div>
                    <div class="stat-label">إعلانات الفيديو</div>
                </div>
            </div>
        </section>

        <!-- Create New Backup Ad -->
        <section class="form-section">
            <h2><i class="fas fa-plus"></i> إنشاء إعلان احتياطي جديد</h2>
            
            <form id="backupAdForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="adTitle">عنوان الإعلان</label>
                        <input type="text" id="adTitle" required placeholder="أدخل عنوان الإعلان">
                    </div>

                    <div class="form-group">
                        <label for="adType">نوع الإعلان</label>
                        <select id="adType" required>
                            <option value="">اختر نوع الإعلان</option>
                            <option value="image">إعلان صورة</option>
                            <option value="video">إعلان فيديو</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="adPriority">أولوية العرض (1-10)</label>
                        <input type="range" id="adPriority" min="1" max="10" value="5" class="priority-slider">
                        <div class="priority-display">الأولوية: <span id="priorityValue">5</span></div>
                    </div>

                    <div class="form-group">
                        <label for="adDuration">مدة العرض (ثانية)</label>
                        <input type="number" id="adDuration" min="3" max="30" value="5" placeholder="مدة عرض الإعلان">
                    </div>
                </div>

                <div class="form-group">
                    <label for="adDescription">وصف الإعلان</label>
                    <textarea id="adDescription" rows="3" placeholder="وصف مختصر للإعلان"></textarea>
                </div>

                <!-- Media Upload Area -->
                <div class="media-upload-area" id="mediaUploadArea">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <h3>اسحب وأفلت الملف هنا أو انقر للاختيار</h3>
                    <p>يدعم: JPG, PNG, GIF, MP4, WebM (حد أقصى 10 ميجابايت)</p>
                    <input type="file" id="mediaFile" accept="image/*,video/*" style="display: none;">
                </div>

                <!-- Media Preview -->
                <div id="mediaPreview" style="display: none;">
                    <img id="imagePreview" class="media-preview" style="display: none;">
                    <video id="videoPreview" class="media-preview" controls style="display: none;"></video>
                    <div class="media-info">
                        <span id="mediaSize"></span> | <span id="mediaDimensions"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="clickAction">إجراء النقر</label>
                    <select id="clickAction">
                        <option value="none">لا يوجد إجراء</option>
                        <option value="url">فتح رابط</option>
                        <option value="close">إغلاق الإعلان</option>
                    </select>
                </div>

                <div class="form-group" id="clickUrlGroup" style="display: none;">
                    <label for="clickUrl">رابط الوجهة</label>
                    <input type="url" id="clickUrl" placeholder="https://example.com">
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الإعلان الاحتياطي
                    </button>
                    <button type="button" onclick="resetForm()" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </section>

        <!-- Existing Backup Ads -->
        <section class="ads-section">
            <h2><i class="fas fa-list"></i> الإعلانات الاحتياطية الموجودة</h2>
            <div id="backupAdsList" class="ads-list">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل الإعلانات...
                </div>
            </div>
        </section>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <i class="fas fa-spinner fa-spin"></i>
            <p id="loadingText">جاري المعالجة...</p>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification" style="display: none;">
        <span id="notificationText"></span>
        <button onclick="hideNotification()"><i class="fas fa-times"></i></button>
    </div>

    <script src="backup-ads-manager.js"></script>
</body>
</html>
