/**
 * محسن الأداء البسيط والسريع
 * Simple and Fast Performance Optimizer
 * 
 * يحل مشاكل البطء والتعليق بطريقة بسيطة وفعالة
 * Solves slowness and freezing issues in a simple and effective way
 */

class SimplePerformanceOptimizer {
    constructor() {
        this.isLowEndDevice = this.detectLowEndDevice();
        this.optimizationsApplied = [];
        this.isInitialized = false;
        
        console.log(`🚀 محسن الأداء البسيط: ${this.isLowEndDevice ? 'جهاز ضعيف' : 'جهاز قوي'} تم اكتشافه`);
        this.init();
    }

    // كشف الأجهزة الضعيفة بطريقة بسيطة
    detectLowEndDevice() {
        const checks = {
            cores: navigator.hardwareConcurrency <= 2,
            memory: navigator.deviceMemory && navigator.deviceMemory <= 2,
            connection: navigator.connection && (
                navigator.connection.effectiveType === 'slow-2g' ||
                navigator.connection.effectiveType === '2g'
            )
        };

        return Object.values(checks).filter(Boolean).length >= 1;
    }

    // تهيئة بسيطة وسريعة
    init() {
        // تطبيق التحسينات الأساسية فوراً
        this.applyBasicOptimizations();
        
        // تحسينات حسب نوع الجهاز
        if (this.isLowEndDevice) {
            this.applyLowEndOptimizations();
        }
        
        // إصلاح الصور البسيط
        this.setupSimpleImageFix();
        
        this.isInitialized = true;
        console.log('✅ تم تطبيق محسن الأداء البسيط بنجاح!');
    }

    // تحسينات أساسية
    applyBasicOptimizations() {
        // تحسين CSS بسيط
        const style = document.createElement('style');
        style.textContent = `
            /* تحسينات الأداء البسيطة */
            * {
                box-sizing: border-box;
            }
            
            img {
                image-rendering: optimizeSpeed;
                will-change: auto;
            }
            
            .items-container {
                scroll-behavior: auto;
                -webkit-overflow-scrolling: touch;
            }
            
            /* تحسين للأجهزة الضعيفة */
            .simple-low-end * {
                animation-duration: 0.1s !important;
                transition-duration: 0.1s !important;
            }
        `;
        document.head.appendChild(style);
        
        // تحسين معالجة الأحداث
        this.optimizeEventHandling();
        
        this.optimizationsApplied.push('Basic optimizations');
    }

    // تحسينات للأجهزة الضعيفة
    applyLowEndOptimizations() {
        console.log('🔧 تطبيق تحسينات الأجهزة الضعيفة...');
        
        // إضافة كلاس للأجهزة الضعيفة
        document.body.classList.add('simple-low-end');
        
        // تقليل عدد العناصر المعروضة
        setTimeout(() => {
            this.reduceVisibleItems();
        }, 1000);
        
        this.optimizationsApplied.push('Low-end optimizations');
    }

    // تحسين معالجة الأحداث
    optimizeEventHandling() {
        // استخدام passive listeners للتمرير
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (['scroll', 'touchstart', 'touchmove', 'wheel'].includes(type)) {
                options = options || {};
                if (typeof options === 'object') {
                    options.passive = true;
                }
            }
            return originalAddEventListener.call(this, type, listener, options);
        };
    }

    // تقليل العناصر المرئية للأجهزة الضعيفة
    reduceVisibleItems() {
        if (!this.isLowEndDevice) return;
        
        const containers = document.querySelectorAll('.items-container');
        containers.forEach(container => {
            const items = container.querySelectorAll('.item, .mod-card');
            if (items.length > 5) {
                for (let i = 5; i < items.length; i++) {
                    items[i].style.display = 'none';
                    items[i].dataset.hiddenForPerformance = 'true';
                }
                console.log(`📱 تم إخفاء ${items.length - 5} عنصر لتحسين الأداء`);
            }
        });
    }

    // إصلاح الصور البسيط
    setupSimpleImageFix() {
        // إصلاح الصور الموجودة
        const images = document.querySelectorAll('img');
        images.forEach(img => this.fixImage(img));
        
        // مراقبة الصور الجديدة
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        const newImages = node.tagName === 'IMG' ? [node] : node.querySelectorAll('img');
                        newImages.forEach(img => this.fixImage(img));
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // معالجة أخطاء الصور
        document.addEventListener('error', (e) => {
            if (e.target.tagName === 'IMG') {
                this.handleImageError(e.target);
            }
        }, true);
    }

    // إصلاح صورة واحدة
    fixImage(img) {
        if (!img || img.tagName !== 'IMG') return;

        // تحسين الصورة
        img.style.imageRendering = 'optimizeSpeed';
        img.loading = 'lazy';
        
        // التحقق من حالة الصورة
        if (!img.src && img.dataset.src) {
            img.src = img.dataset.src;
        }
        
        // إضافة placeholder للصور الفارغة
        if (!img.src && !img.dataset.src) {
            img.src = this.createSimplePlaceholder();
        }
    }

    // معالجة أخطاء الصور
    handleImageError(img) {
        try {
            console.log('🖼️ خطأ في تحميل الصورة، استخدام بديل');
            img.src = this.createErrorPlaceholder();
            img.classList.add('image-error');
        } catch (error) {
            console.warn('خطأ في معالجة خطأ الصورة:', error);
            // استخدام صورة بديلة بسيطة
            img.src = 'data:image/svg+xml;charset=utf-8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="200" height="150"%3E%3Crect width="200" height="150" fill="%23f0f0f0"/%3E%3C/svg%3E';
        }
    }

    // إنشاء placeholder بسيط
    createSimplePlaceholder() {
        const svg = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="150" fill="#f0f0f0"/>
            <text x="100" y="75" text-anchor="middle" fill="#999" font-size="14">Loading...</text>
        </svg>`;
        return 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg);
    }

    // إنشاء placeholder للأخطاء
    createErrorPlaceholder() {
        const svg = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="150" fill="#ffe6e6"/>
            <text x="100" y="75" text-anchor="middle" fill="#cc0000" font-size="14">Failed</text>
        </svg>`;
        return 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg);
    }

    // عرض إحصائيات بسيطة
    getStats() {
        return {
            deviceType: this.isLowEndDevice ? 'Low-end' : 'High-end',
            optimizationsApplied: this.optimizationsApplied,
            isInitialized: this.isInitialized
        };
    }

    // تنظيف بسيط
    cleanup() {
        // تنظيف الذاكرة بطريقة بسيطة
        if (window.gc) {
            window.gc();
        }
        
        // إزالة الصور غير المرئية من الذاكرة
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!this.isElementVisible(img) && img.src && img.src.startsWith('data:')) {
                // لا نحذف الصور الحقيقية، فقط placeholders
                return;
            }
        });
    }

    // فحص رؤية العنصر
    isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top < window.innerHeight &&
            rect.bottom > 0 &&
            rect.left < window.innerWidth &&
            rect.right > 0
        );
    }
}

// تهيئة محسن الأداء البسيط
document.addEventListener('DOMContentLoaded', function() {
    try {
        // التأكد من عدم وجود محسنات أخرى تعمل
        if (window.mobileOptimizer || window.fastImageLoader || window.ultimatePerformanceFix) {
            console.log('⚠️ تم اكتشاف محسنات أخرى، سيتم استخدام المحسن البسيط فقط');
        }

        window.simpleOptimizer = new SimplePerformanceOptimizer();

        // تنظيف دوري بسيط
        setInterval(() => {
            try {
                window.simpleOptimizer.cleanup();
            } catch (error) {
                console.warn('خطأ في تنظيف الذاكرة:', error);
            }
        }, 60000); // كل دقيقة

        // إضافة أوامر المطور
        window.showSimpleStats = () => {
            try {
                console.table(window.simpleOptimizer.getStats());
            } catch (error) {
                console.error('خطأ في عرض الإحصائيات:', error);
            }
        };

        console.log('🚀 محسن الأداء البسيط جاهز!');
        console.log('💡 استخدم showSimpleStats() لعرض الإحصائيات');

    } catch (error) {
        console.error('❌ خطأ في تهيئة محسن الأداء البسيط:', error);
        // إنشاء نسخة احتياطية بسيطة
        window.simpleOptimizer = {
            getStats: () => ({ error: 'Failed to initialize' }),
            cleanup: () => {}
        };
        window.showSimpleStats = () => console.log('محسن الأداء غير متاح');
    }
});

// تصدير للاستخدام العام
window.SimplePerformanceOptimizer = SimplePerformanceOptimizer;
