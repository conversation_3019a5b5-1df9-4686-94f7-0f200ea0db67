# 🔧 إصلاح سريع لمشاكل Supabase - Quick Fix for Supabase Issues

## المشكلة - The Problem
```
Could not find the function public.execute_sql(sql_query) in the schema cache
```

## الحل السريع - Quick Solution

### الخطوة 1: افتح Supabase Dashboard
### Step 1: Open Supabase Dashboard

1. اذهب إلى [Supabase Dashboard](https://app.supabase.com)
2. اختر مشروعك
3. اذهب إلى **SQL Editor**

### الخطوة 2: نفذ SQL للجداول المفقودة
### Step 2: Execute SQL for Missing Tables

انسخ والصق المحتوى التالي في SQL Editor:

```sql
-- إنشاء الجداول المفقودة
-- Create missing tables

-- 1. custom_mod_dialogs
CREATE TABLE IF NOT EXISTS custom_mod_dialogs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100) DEFAULT 'تم',
    show_dont_show_again BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. custom_dialog_mods
CREATE TABLE IF NOT EXISTS custom_dialog_mods (
    id SERIAL PRIMARY KEY,
    dialog_id INTEGER NOT NULL,
    mod_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_dialog_mods_dialog_id FOREIGN KEY (dialog_id) REFERENCES custom_mod_dialogs(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_dialog_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_dialog_mod UNIQUE (dialog_id, mod_id)
);

-- 3. custom_copyright_mods
CREATE TABLE IF NOT EXISTS custom_copyright_mods (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_copyright_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_copyright_mod UNIQUE (mod_id)
);

-- 4. دالة execute_sql (اختيارية)
-- execute_sql function (optional)
CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    query_lower TEXT;
BEGIN
    query_lower := LOWER(TRIM(sql_query));
    
    -- فحص الأمان
    IF query_lower LIKE '%drop %' OR 
       query_lower LIKE '%delete %' OR 
       query_lower LIKE '%truncate %' OR
       query_lower LIKE '%alter %' OR
       query_lower LIKE '%grant %' OR
       query_lower LIKE '%revoke %' OR
       query_lower LIKE '%insert %' OR
       query_lower LIKE '%update %' THEN
        
        IF NOT (query_lower LIKE '%create table if not exists%') THEN
            RAISE EXCEPTION 'Only CREATE TABLE IF NOT EXISTS operations are allowed';
        END IF;
    END IF;
    
    IF NOT query_lower LIKE 'create%' THEN
        RAISE EXCEPTION 'Only CREATE statements are allowed';
    END IF;
    
    EXECUTE sql_query;
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error executing SQL: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- منح الصلاحيات (قم بإلغاء التعليق إذا لزم الأمر)
-- Grant permissions (uncomment if needed)
-- GRANT EXECUTE ON FUNCTION execute_sql(TEXT) TO anon;
-- GRANT EXECUTE ON FUNCTION execute_sql(TEXT) TO authenticated;
```

### الخطوة 3: تشغيل الكود
### Step 3: Run the Code

1. اضغط **Run** أو **Ctrl+Enter**
2. تأكد من عدم وجود أخطاء
3. أعد تحميل التطبيق

## التحقق من النجاح - Verify Success

بعد تنفيذ SQL، يجب أن تختفي رسائل الخطأ وتظهر:

```
Table custom_mod_dialogs exists and is accessible
Table custom_dialog_mods exists and is accessible  
Table custom_copyright_mods exists and is accessible
```

## أدوات إضافية - Additional Tools

### فحص حالة الجداول - Check Table Status
افتح: `app/src/main/assets/admin/table-checker.html`

### ملفات SQL الكاملة - Complete SQL Files
- `database/missing_tables.sql` - جميع الجداول المفقودة
- `database/create_execute_sql_function.sql` - دالة execute_sql
- `database/README_SUPABASE_SETUP.md` - دليل مفصل

## ملاحظات مهمة - Important Notes

1. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية قبل التنفيذ
2. **الصلاحيات**: تأكد من صلاحيات المستخدم
3. **الجداول المرجعية**: تأكد من وجود جدول `mods`

## إذا استمرت المشاكل - If Problems Persist

1. تحقق من سجلات الأخطاء في Supabase
2. راجع إعدادات RLS (Row Level Security)
3. تأكد من صحة متغيرات البيئة
4. استخدم أداة فحص الجداول المرفقة
