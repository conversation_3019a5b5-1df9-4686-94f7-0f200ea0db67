# زر تعديل اللغة في القائمة الجانبية - Language Change Button Documentation

## نظرة عامة / Overview

تم إضافة زر "تعديل اللغة" في القائمة الجانبية للتطبيق يسمح للمستخدمين بتغيير لغة التطبيق بسهولة بين العربية والإنجليزية.

A "Change Language" button has been added to the app's sidebar menu allowing users to easily switch the app language between Arabic and English.

## المميزات / Features

### 🌍 زر تعديل اللغة / Language Change Button
- **الموقع**: القائمة الجانبية (Drawer Menu)
- **الترتيب**: الثاني بعد رابط لوحة الإدارة
- **اللون**: أخضر (#4ade80) للتمييز
- **الأيقونة**: 🌍 (أيقونة الكرة الأرضية)

### 📱 نافذة اختيار اللغة / Language Selection Modal
- **التصميم**: نافذة منبثقة جميلة بتصميم التطبيق
- **الخيارات**: العربية 🇸🇦 والإنجليزية 🇺🇸
- **التأثيرات**: رسوم متحركة ناعمة
- **الإغلاق**: زر إغلاق أو النقر خارج النافذة

### 🔄 تغيير اللغة / Language Switching
- **الحفظ**: في localStorage وقاعدة البيانات
- **التطبيق**: فوري مع إعادة تحميل الصفحة
- **الرسالة**: رسالة نجاح مترجمة
- **التحديث**: تحديث نص الزر تلقائياً

## التنفيذ التقني / Technical Implementation

### 1. إضافة الزر في القائمة الجانبية
```javascript
// في دالة loadDrawerLinks()
const languageLink = document.createElement('a');
languageLink.href = '#';
languageLink.onclick = (e) => {
    e.preventDefault();
    showLanguageSelectionModal();
    // إغلاق القائمة الجانبية
    if (drawer) drawer.classList.remove("active");
    if (overlay) overlay.classList.remove("active");
};
```

### 2. نافذة اختيار اللغة
```javascript
function showLanguageSelectionModal() {
    // إنشاء نافذة منبثقة مع خيارات اللغة
    // تصميم جميل مع رسوم متحركة
    // أزرار للعربية والإنجليزية
}
```

### 3. تغيير اللغة
```javascript
async function changeLanguage(language) {
    // تحديث نظام الترجمة
    window.translationManager.setLanguage(language);
    
    // حفظ في localStorage
    localStorage.setItem('selectedLanguage', language);
    
    // حفظ في قاعدة البيانات
    await saveUserLanguagePreference(language);
    
    // إعادة تحميل الصفحة
    location.reload();
}
```

## الملفات المعدلة / Modified Files

### 1. script.js
- **إضافة زر اللغة** في دالة `loadDrawerLinks()`
- **دالة `showLanguageSelectionModal()`** لعرض نافذة اختيار اللغة
- **دالة `changeLanguage()`** لتغيير اللغة
- **دالة `updateLanguageLinkText()`** لتحديث نص الزر
- **دالة `showLanguageChangeSuccess()`** لعرض رسالة النجاح

### 2. translations.js
- **إضافة ترجمات جديدة**:
  - `'change_language'`: 'تعديل اللغة' / 'Change Language'
  - `'language_changed_success'`: رسالة نجاح التغيير

### 3. style.css
- **إضافة رسوم متحركة**:
  - `@keyframes fadeIn`
  - `@keyframes fadeInScaleUp`

## التصميم والألوان / Design and Colors

### زر تعديل اللغة / Language Button
```css
color: #4ade80; /* أخضر مميز */
background: transparent;
border-bottom: 1px solid #444;
padding: 12px 15px;
font-weight: bold;
```

### نافذة اختيار اللغة / Language Modal
```css
background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
border: 3px solid #ffcc00;
border-radius: 20px;
box-shadow: 0 0 30px rgba(255, 204, 0, 0.5);
```

### أزرار اللغة / Language Option Buttons
```css
background: linear-gradient(45deg, #ffcc00, #ff9800);
color: #000;
border-radius: 15px;
padding: 15px 30px;
font-size: 1.3rem;
font-weight: bold;
```

## سلوك الزر / Button Behavior

### النص الديناميكي / Dynamic Text
- **العربية**: "تعديل اللغة" مع أيقونة 🌍
- **الإنجليزية**: "Change Language" مع أيقونة 🌍
- **التحديث**: تلقائي عند تغيير اللغة

### التفاعل / Interaction
1. **النقر على الزر** → فتح نافذة اختيار اللغة
2. **اختيار لغة** → حفظ الاختيار وإعادة التحميل
3. **إغلاق النافذة** → زر الإغلاق أو النقر خارجها

## الاختبار / Testing

### 1. اختبار الزر
```javascript
// التحقق من وجود الزر
const languageButton = document.getElementById('language-link');
console.log('Language button exists:', !!languageButton);
```

### 2. اختبار تغيير اللغة
```javascript
// اختبار تغيير اللغة برمجياً
changeLanguage('ar'); // للعربية
changeLanguage('en'); // للإنجليزية
```

### 3. اختبار النص
```javascript
// اختبار تحديث النص
updateLanguageLinkText();
```

## استكشاف الأخطاء / Troubleshooting

### مشكلة: الزر لا يظهر
**الحل**:
```javascript
// التحقق من تحميل القائمة الجانبية
setTimeout(() => {
    updateLanguageLinkText();
}, 500);
```

### مشكلة: النافذة لا تفتح
**الحل**:
```javascript
// التحقق من دالة showLanguageSelectionModal
if (typeof showLanguageSelectionModal === 'function') {
    showLanguageSelectionModal();
} else {
    console.error('showLanguageSelectionModal function not found');
}
```

### مشكلة: اللغة لا تتغير
**الحل**:
```javascript
// التحقق من نظام الترجمة
if (window.translationManager) {
    window.translationManager.setLanguage('ar');
} else {
    console.error('Translation manager not found');
}
```

## الاستخدام / Usage

### للمستخدمين / For Users
1. افتح القائمة الجانبية (☰)
2. انقر على "تعديل اللغة" / "Change Language"
3. اختر اللغة المفضلة
4. انتظر إعادة تحميل الصفحة

### للمطورين / For Developers
```javascript
// إضافة مستمع للأحداث
window.addEventListener('languageChanged', (event) => {
    console.log('Language changed to:', event.detail.language);
});

// تغيير اللغة برمجياً
changeLanguage('ar');

// تحديث نص الزر
updateLanguageLinkText();
```

## التحسينات المستقبلية / Future Improvements

### 1. إضافة لغات جديدة
- فرنسية، إسبانية، إلخ
- تحديث قائمة الخيارات

### 2. تحسين التصميم
- رسوم متحركة أكثر
- تأثيرات بصرية محسنة

### 3. إعدادات متقدمة
- حفظ تفضيلات إضافية
- خيارات تخصيص أكثر

## الأمان / Security

### حماية البيانات / Data Protection
- لا يتم حفظ معلومات شخصية حساسة
- معرف المستخدم مجهول
- البيانات قابلة للحذف

### التحقق من الصحة / Validation
```javascript
// التحقق من صحة اللغة
if (language !== 'ar' && language !== 'en') {
    console.error('Invalid language code');
    return;
}
```

## الخلاصة / Summary

تم إضافة زر "تعديل اللغة" بنجاح في القائمة الجانبية مع:
- ✅ تصميم جميل ومتناسق
- ✅ وظائف كاملة لتغيير اللغة
- ✅ حفظ الاختيار محلياً وفي قاعدة البيانات
- ✅ رسوم متحركة ناعمة
- ✅ دعم كامل للترجمة
- ✅ سهولة الاستخدام

The "Change Language" button has been successfully added to the sidebar with:
- ✅ Beautiful and consistent design
- ✅ Complete language switching functionality
- ✅ Local and database storage
- ✅ Smooth animations
- ✅ Full translation support
- ✅ Easy to use interface
