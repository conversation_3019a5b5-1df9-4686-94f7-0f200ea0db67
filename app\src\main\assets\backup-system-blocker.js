// Backup System Blocker - حاجب دائم لنظام النسخ الاحتياطي
// يمنع تحميل وتشغيل DatabaseBackupSystem نهائياً

(function() {
    'use strict';

    console.log('🛡️ Backup System Blocker activated');

    // Check if backup system should be blocked
    const isBlocked = localStorage.getItem('backupSystemBlocked') === 'true';
    
    if (isBlocked) {
        console.log('🚫 نظام النسخ الاحتياطي محجوب بواسطة المستخدم');
    }

    // Block DatabaseBackupSystem class
    if (typeof window.DatabaseBackupSystem === 'undefined' || isBlocked) {
        Object.defineProperty(window, 'DatabaseBackupSystem', {
            get: function() {
                console.warn('⚠️ DatabaseBackupSystem محجوب - تم منع التحميل');
                return function() {
                    console.warn('⚠️ محاولة إنشاء DatabaseBackupSystem تم منعها');
                    return {
                        init: function() {
                            console.log('ℹ️ نظام النسخ الاحتياطي معطل');
                            return Promise.resolve();
                        },
                        initializeDatabaseConnections: () => Promise.resolve(),
                        initializeFirebaseBackup: () => Promise.resolve(),
                        checkDatabaseHealth: () => Promise.resolve(),
                        createBackup: () => Promise.resolve(),
                        restoreBackup: () => Promise.resolve(),
                        _blocked: true,
                        _blockedAt: new Date().toISOString()
                    };
                };
            },
            set: function(value) {
                console.warn('⚠️ محاولة تعيين DatabaseBackupSystem تم منعها');
            },
            configurable: false,
            enumerable: false
        });
    }

    // Block databaseBackupSystem instance
    if (typeof window.databaseBackupSystem === 'undefined' || isBlocked) {
        Object.defineProperty(window, 'databaseBackupSystem', {
            get: function() {
                console.warn('⚠️ databaseBackupSystem محجوب');
                return {
                    init: function() {
                        console.log('ℹ️ نظام النسخ الاحتياطي معطل');
                        return Promise.resolve();
                    },
                    initializeDatabaseConnections: () => Promise.resolve(),
                    initializeFirebaseBackup: () => Promise.resolve(),
                    checkDatabaseHealth: () => Promise.resolve(),
                    createBackup: () => Promise.resolve(),
                    restoreBackup: () => Promise.resolve(),
                    _blocked: true,
                    _blockedAt: new Date().toISOString()
                };
            },
            set: function(value) {
                console.warn('⚠️ محاولة تعيين databaseBackupSystem تم منعها');
            },
            configurable: false,
            enumerable: false
        });
    }

    // Block script loading
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(this, tagName);
        
        if (tagName.toLowerCase() === 'script') {
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
                if (name === 'src' && value && value.includes('database-backup-system')) {
                    console.warn('🚫 منع تحميل database-backup-system script:', value);
                    return; // Don't set the src attribute
                }
                return originalSetAttribute.call(this, name, value);
            };

            // Also block direct src assignment
            Object.defineProperty(element, 'src', {
                get: function() {
                    return this._src || '';
                },
                set: function(value) {
                    if (value && value.includes('database-backup-system')) {
                        console.warn('🚫 منع تحميل database-backup-system script via src:', value);
                        return;
                    }
                    this._src = value;
                    originalSetAttribute.call(this, 'src', value);
                }
            });
        }
        
        return element;
    };

    // Block dynamic imports
    if (window.import) {
        const originalImport = window.import;
        window.import = function(specifier) {
            if (specifier && specifier.includes('database-backup-system')) {
                console.warn('🚫 منع استيراد database-backup-system module:', specifier);
                return Promise.reject(new Error('Module blocked by backup system blocker'));
            }
            return originalImport.call(this, specifier);
        };
    }

    // Override fetch for backup system files
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
        if (typeof url === 'string' && url.includes('database-backup-system')) {
            console.warn('🚫 منع تحميل database-backup-system via fetch:', url);
            return Promise.reject(new Error('File blocked by backup system blocker'));
        }
        return originalFetch.call(this, url, options);
    };

    // Remove existing script tags
    function removeExistingScripts() {
        const scripts = document.querySelectorAll('script[src*="database-backup-system"]');
        scripts.forEach(script => {
            console.log('🗑️ إزالة سكريبت database-backup-system موجود:', script.src);
            script.remove();
        });
    }

    // Clean up on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', removeExistingScripts);
    } else {
        removeExistingScripts();
    }

    // Monitor for new script additions
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1 && node.tagName === 'SCRIPT') {
                    if (node.src && node.src.includes('database-backup-system')) {
                        console.warn('🚫 منع إضافة database-backup-system script:', node.src);
                        node.remove();
                    }
                }
            });
        });
    });

    observer.observe(document, {
        childList: true,
        subtree: true
    });

    // Provide utility functions
    window.backupSystemBlocker = {
        isBlocked: function() {
            return localStorage.getItem('backupSystemBlocked') === 'true';
        },
        
        block: function() {
            localStorage.setItem('backupSystemBlocked', 'true');
            localStorage.setItem('backupSystemBlockedAt', new Date().toISOString());
            console.log('🚫 تم حجب نظام النسخ الاحتياطي');
            location.reload(); // Reload to apply blocking
        },
        
        unblock: function() {
            localStorage.removeItem('backupSystemBlocked');
            localStorage.removeItem('backupSystemBlockedAt');
            console.log('✅ تم إلغاء حجب نظام النسخ الاحتياطي');
            location.reload(); // Reload to remove blocking
        },
        
        status: function() {
            const blocked = this.isBlocked();
            const blockedAt = localStorage.getItem('backupSystemBlockedAt');
            
            return {
                blocked: blocked,
                blockedAt: blockedAt,
                classBlocked: typeof window.DatabaseBackupSystem !== 'undefined' && window.DatabaseBackupSystem._blocked,
                instanceBlocked: typeof window.databaseBackupSystem !== 'undefined' && window.databaseBackupSystem._blocked
            };
        }
    };

    // Log status
    const status = window.backupSystemBlocker.status();
    if (status.blocked) {
        console.log('🛡️ نظام النسخ الاحتياطي محجوب منذ:', status.blockedAt);
    } else {
        console.log('ℹ️ نظام النسخ الاحتياطي غير محجوب');
    }

    console.log('🛡️ Backup System Blocker ready');

})();
