/**
 * اختبار إصلاح الأداء
 * Performance Fix Test
 * 
 * ملف اختبار للتأكد من أن جميع الإصلاحات تعمل بشكل صحيح
 */

// اختبار محسن الأداء البسيط
function testSimpleOptimizer() {
    console.log('🧪 اختبار محسن الأداء البسيط...');
    
    const tests = {
        optimizerExists: false,
        statsFunction: false,
        cleanupFunction: false,
        deviceDetection: false,
        imageHandling: false
    };
    
    try {
        // اختبار وجود المحسن
        if (window.simpleOptimizer) {
            tests.optimizerExists = true;
            console.log('✅ محسن الأداء موجود');
            
            // اختبار دالة الإحصائيات
            if (typeof window.simpleOptimizer.getStats === 'function') {
                tests.statsFunction = true;
                console.log('✅ دالة الإحصائيات تعمل');
                
                const stats = window.simpleOptimizer.getStats();
                console.log('📊 الإحصائيات:', stats);
            }
            
            // اختبار دالة التنظيف
            if (typeof window.simpleOptimizer.cleanup === 'function') {
                tests.cleanupFunction = true;
                console.log('✅ دالة التنظيف تعمل');
            }
            
            // اختبار كشف نوع الجهاز
            if (window.simpleOptimizer.isLowEndDevice !== undefined) {
                tests.deviceDetection = true;
                console.log(`✅ كشف الجهاز: ${window.simpleOptimizer.isLowEndDevice ? 'ضعيف' : 'قوي'}`);
            }
            
            // اختبار معالجة الصور
            if (typeof window.simpleOptimizer.fixImage === 'function') {
                tests.imageHandling = true;
                console.log('✅ معالجة الصور تعمل');
            }
        } else {
            console.log('❌ محسن الأداء غير موجود');
        }
        
        // اختبار دالة الإحصائيات العامة
        if (typeof window.showSimpleStats === 'function') {
            console.log('✅ دالة عرض الإحصائيات العامة تعمل');
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار محسن الأداء:', error);
    }
    
    return tests;
}

// اختبار نظام التخزين المؤقت البسيط
function testSimpleCache() {
    console.log('🧪 اختبار نظام التخزين المؤقت...');
    
    const tests = {
        cacheExists: false,
        setFunction: false,
        getFunction: false,
        cleanupFunction: false
    };
    
    try {
        // البحث عن نظام التخزين المؤقت في script.js
        if (typeof simpleCache !== 'undefined') {
            tests.cacheExists = true;
            console.log('✅ نظام التخزين المؤقت موجود');
            
            // اختبار دالة الحفظ
            if (typeof simpleCache.set === 'function') {
                tests.setFunction = true;
                simpleCache.set('test', 'value');
                console.log('✅ دالة الحفظ تعمل');
            }
            
            // اختبار دالة الاسترجاع
            if (typeof simpleCache.get === 'function') {
                tests.getFunction = true;
                const value = simpleCache.get('test');
                if (value === 'value') {
                    console.log('✅ دالة الاسترجاع تعمل');
                }
            }
            
            // اختبار دالة التنظيف
            if (typeof simpleCache.cleanup === 'function') {
                tests.cleanupFunction = true;
                console.log('✅ دالة تنظيف التخزين المؤقت تعمل');
            }
        } else {
            console.log('❌ نظام التخزين المؤقت غير موجود');
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار التخزين المؤقت:', error);
    }
    
    return tests;
}

// اختبار دوال التحميل البسيطة
function testSimpleFetchFunctions() {
    console.log('🧪 اختبار دوال التحميل البسيطة...');
    
    const functions = [
        'fetchNewsItems',
        'fetchAddonsItems',
        'fetchSuggestedItems',
        'fetchShadersItems',
        'fetchTextureItems',
        'fetchSeedsItems',
        'fetchMapsItems',
        'clearCache'
    ];
    
    const tests = {};
    
    functions.forEach(funcName => {
        try {
            if (typeof window[funcName] === 'function') {
                tests[funcName] = true;
                console.log(`✅ ${funcName} موجودة`);
            } else {
                tests[funcName] = false;
                console.log(`❌ ${funcName} غير موجودة`);
            }
        } catch (error) {
            tests[funcName] = false;
            console.error(`❌ خطأ في فحص ${funcName}:`, error);
        }
    });
    
    return tests;
}

// اختبار الأداء العام
function testPerformance() {
    console.log('🧪 اختبار الأداء العام...');
    
    const tests = {
        memoryUsage: 0,
        domElements: 0,
        loadTime: 0,
        responsiveness: false
    };
    
    try {
        // اختبار استخدام الذاكرة
        if (performance.memory) {
            tests.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            console.log(`📊 استخدام الذاكرة: ${tests.memoryUsage} MB`);
        }
        
        // عدد عناصر DOM
        tests.domElements = document.querySelectorAll('*').length;
        console.log(`📊 عناصر DOM: ${tests.domElements}`);
        
        // وقت التحميل
        if (performance.timing) {
            tests.loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log(`📊 وقت التحميل: ${tests.loadTime}ms`);
        }
        
        // اختبار الاستجابة
        const startTime = performance.now();
        setTimeout(() => {
            const responseTime = performance.now() - startTime;
            tests.responsiveness = responseTime < 100;
            console.log(`📊 زمن الاستجابة: ${responseTime.toFixed(2)}ms`);
        }, 0);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الأداء:', error);
    }
    
    return tests;
}

// تشغيل جميع الاختبارات
function runAllTests() {
    console.log('🚀 بدء اختبارات إصلاح الأداء...');
    console.log('=====================================');
    
    const results = {
        optimizer: testSimpleOptimizer(),
        cache: testSimpleCache(),
        fetchFunctions: testSimpleFetchFunctions(),
        performance: testPerformance()
    };
    
    console.log('=====================================');
    console.log('📋 ملخص النتائج:');
    console.table(results);
    
    // حساب النجاح العام
    let totalTests = 0;
    let passedTests = 0;
    
    Object.values(results).forEach(category => {
        Object.values(category).forEach(test => {
            totalTests++;
            if (test === true || (typeof test === 'number' && test > 0)) {
                passedTests++;
            }
        });
    });
    
    const successRate = Math.round((passedTests / totalTests) * 100);
    console.log(`🎯 معدل النجاح: ${successRate}% (${passedTests}/${totalTests})`);
    
    if (successRate >= 80) {
        console.log('🎉 إصلاح الأداء يعمل بشكل ممتاز!');
    } else if (successRate >= 60) {
        console.log('⚠️ إصلاح الأداء يعمل بشكل جيد مع بعض المشاكل');
    } else {
        console.log('❌ إصلاح الأداء يحتاج إلى مراجعة');
    }
    
    return results;
}

// تصدير الدوال للاستخدام العام
window.testPerformanceFix = {
    runAllTests,
    testSimpleOptimizer,
    testSimpleCache,
    testSimpleFetchFunctions,
    testPerformance
};

// تشغيل الاختبارات تلقائياً بعد تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('💡 لتشغيل اختبارات الأداء، استخدم: testPerformanceFix.runAllTests()');
    }, 3000);
});

console.log('🧪 ملف اختبار إصلاح الأداء جاهز!');
console.log('💡 استخدم testPerformanceFix.runAllTests() لتشغيل جميع الاختبارات');
