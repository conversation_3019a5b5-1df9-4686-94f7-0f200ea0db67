<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة الإعدادات النهائية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #ffd700;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }
        
        .test-section h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .status {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .status.success {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
        }
        
        .status.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }
        
        .test-button {
            background: linear-gradient(135deg, #ffd700, #ffb300);
            color: #000;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        
        .iframe-container {
            margin-top: 30px;
            border: 2px solid #ffd700;
            border-radius: 10px;
            overflow: hidden;
            height: 80vh;
            min-height: 600px;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .mobile-test {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        
        .mobile-preview {
            width: 375px;
            height: 667px;
            border: 3px solid #333;
            border-radius: 25px;
            overflow: hidden;
            background: #000;
            position: relative;
        }
        
        .mobile-preview iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        @media (max-width: 768px) {
            .mobile-test {
                flex-direction: column;
            }
            
            .mobile-preview {
                width: 100%;
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 اختبار صفحة الإعدادات النهائية</h1>
            <p>فحص شامل للتحسينات الجديدة: الترجمة، الجوال، زر الرجوع</p>
        </div>
        
        <div class="test-section">
            <h3>✅ التحسينات المطبقة</h3>
            <div class="test-item">
                <span>إزالة صور المستخدم والعلامة التجارية</span>
                <span class="status success">✅ تم</span>
            </div>
            <div class="test-item">
                <span>تحسين التخطيط للجوال</span>
                <span class="status success">✅ تم</span>
            </div>
            <div class="test-item">
                <span>إضافة زر الرجوع الوظيفي</span>
                <span class="status success">✅ تم</span>
            </div>
            <div class="test-item">
                <span>دعم الترجمة الكاملة (عربي/إنجليزي)</span>
                <span class="status success">✅ تم</span>
            </div>
            <div class="test-item">
                <span>تحسين الرسائل والتفاعلات</span>
                <span class="status success">✅ تم</span>
            </div>
            <div class="test-item">
                <span>إضافة CSS متجاوب للجوال</span>
                <span class="status success">✅ تم</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبارات التفاعل</h3>
            <div style="text-align: center;">
                <button class="test-button" onclick="testLanguageSwitch()">🌐 اختبار تغيير اللغة</button>
                <button class="test-button" onclick="testMobileResponsive()">📱 اختبار الاستجابة للجوال</button>
                <button class="test-button" onclick="testBackButton()">⬅️ اختبار زر الرجوع</button>
                <button class="test-button" onclick="testSettingsSave()">💾 اختبار حفظ الإعدادات</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📱 معاينة الجوال والديسكتوب</h3>
            <div style="text-align: center; margin-bottom: 15px;">
                <button class="test-button" onclick="loadDesktopView()">🖥️ عرض الديسكتوب</button>
                <button class="test-button" onclick="loadMobileView()">📱 عرض الجوال</button>
                <button class="test-button" onclick="toggleLanguage()">🌐 تبديل اللغة</button>
            </div>
            
            <div class="iframe-container">
                <iframe id="settingsPreview" src="user-settings.html"></iframe>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'ar';
        
        function testLanguageSwitch() {
            const iframe = document.getElementById('settingsPreview');
            iframe.contentWindow.postMessage({
                type: 'TEST_LANGUAGE_SWITCH',
                language: currentLanguage === 'ar' ? 'en' : 'ar'
            }, '*');
            
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            alert(`تم تبديل اللغة إلى: ${currentLanguage === 'ar' ? 'العربية' : 'English'}`);
        }
        
        function testMobileResponsive() {
            const iframe = document.getElementById('settingsPreview');
            const container = iframe.parentElement;
            
            if (container.style.maxWidth === '375px') {
                container.style.maxWidth = '100%';
                container.style.height = '80vh';
                alert('تم التبديل إلى عرض الديسكتوب');
            } else {
                container.style.maxWidth = '375px';
                container.style.height = '667px';
                container.style.margin = '0 auto';
                alert('تم التبديل إلى عرض الجوال');
            }
        }
        
        function testBackButton() {
            const iframe = document.getElementById('settingsPreview');
            iframe.contentWindow.postMessage({
                type: 'TEST_BACK_BUTTON'
            }, '*');
            
            alert('تم اختبار زر الرجوع - تحقق من وحدة التحكم');
        }
        
        function testSettingsSave() {
            const iframe = document.getElementById('settingsPreview');
            iframe.contentWindow.postMessage({
                type: 'TEST_SETTINGS_SAVE'
            }, '*');
            
            alert('تم اختبار حفظ الإعدادات');
        }
        
        function loadDesktopView() {
            const container = document.querySelector('.iframe-container');
            container.style.maxWidth = '100%';
            container.style.height = '80vh';
        }
        
        function loadMobileView() {
            const container = document.querySelector('.iframe-container');
            container.style.maxWidth = '375px';
            container.style.height = '667px';
            container.style.margin = '0 auto';
        }
        
        function toggleLanguage() {
            const iframe = document.getElementById('settingsPreview');
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            
            // Reload iframe with language parameter
            const currentSrc = iframe.src;
            iframe.src = currentSrc + (currentSrc.includes('?') ? '&' : '?') + 'lang=' + currentLanguage;
        }
        
        // Listen for messages from iframe
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type) {
                console.log('Received message from settings page:', event.data);
            }
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Settings test page loaded');
        });
    </script>
</body>
</html>
