<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الصور - Mod Etaris</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="image-display-styles.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            color: #333;
            border-bottom: 2px solid #ff6b35;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-success { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-error { background-color: #ef4444; }
        
        .diagnostic-output {
            background: #1f2937;
            color: #10b981;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .test-button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #e55a2b;
        }
        
        .sample-images {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
        }
        
        .sample-image {
            width: 150px;
            height: 150px;
            border: 2px solid #ff6b35;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: white; margin-bottom: 30px;">
            🧪 اختبار إصلاح عرض الصور
        </h1>
        
        <!-- قسم حالة النظام -->
        <div class="test-section">
            <h2 class="test-title">📊 حالة النظام</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h3>ملفات الإصلاح</h3>
                    <div id="filesStatus">جاري التحقق...</div>
                </div>
                <div class="test-card">
                    <h3>دوال JavaScript</h3>
                    <div id="functionsStatus">جاري التحقق...</div>
                </div>
                <div class="test-card">
                    <h3>ملفات CSS</h3>
                    <div id="cssStatus">جاري التحقق...</div>
                </div>
            </div>
        </div>
        
        <!-- قسم اختبار الصور -->
        <div class="test-section">
            <h2 class="test-title">🖼️ اختبار عرض الصور</h2>
            <div class="sample-images">
                <img class="mod-image sample-image" data-src="https://example.com/valid-image.jpg" alt="صورة صحيحة">
                <img class="mod-image sample-image" data-src="https://invalid-url.com/broken.jpg" alt="صورة معطلة">
                <img class="mod-image sample-image" data-src="image/placeholder.svg" alt="صورة احتياطية">
                <img class="mod-image sample-image" data-src="" alt="صورة فارغة">
            </div>
            <button class="test-button" onclick="testImageLoading()">اختبار تحميل الصور</button>
            <button class="test-button" onclick="fixBrokenImages()">إصلاح الصور المعطلة</button>
        </div>
        
        <!-- قسم التشخيص -->
        <div class="test-section">
            <h2 class="test-title">🔍 تشخيص الصور</h2>
            <div>
                <button class="test-button" onclick="runDiagnostics()">تشغيل التشخيص</button>
                <button class="test-button" onclick="clearOutput()">مسح النتائج</button>
            </div>
            <div id="diagnosticOutput" class="diagnostic-output">
                انقر على "تشغيل التشخيص" لبدء الفحص...
            </div>
        </div>
        
        <!-- قسم الإحصائيات -->
        <div class="test-section">
            <h2 class="test-title">📈 إحصائيات الأداء</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h3>إجمالي الصور</h3>
                    <div id="totalImages">0</div>
                </div>
                <div class="test-card">
                    <h3>تم تحميلها</h3>
                    <div id="loadedImages">0</div>
                </div>
                <div class="test-card">
                    <h3>فشل التحميل</h3>
                    <div id="failedImages">0</div>
                </div>
                <div class="test-card">
                    <h3>معدل النجاح</h3>
                    <div id="successRate">0%</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات والملفات -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="image-display-fix.js"></script>
    <script src="image-diagnostics.js"></script>
    
    <script>
        // فحص حالة النظام
        function checkSystemStatus() {
            // فحص الملفات
            const filesChecks = [
                { name: 'image-display-fix.js', check: () => typeof window.getMainImageSafe === 'function' },
                { name: 'image-diagnostics.js', check: () => typeof window.imageDiagnostics === 'object' },
                { name: 'image-display-styles.css', check: () => true }
            ];
            
            let filesHtml = '';
            filesChecks.forEach(file => {
                const status = file.check() ? 'success' : 'error';
                const icon = status === 'success' ? '✅' : '❌';
                filesHtml += `<div><span class="status-indicator status-${status}"></span>${icon} ${file.name}</div>`;
            });
            document.getElementById('filesStatus').innerHTML = filesHtml;
            
            // فحص الدوال
            const functions = [
                'getMainImageSafe',
                'handleImageError', 
                'enhanceLazyLoading',
                'imageDiagnostics'
            ];
            
            let functionsHtml = '';
            functions.forEach(func => {
                const exists = typeof window[func] !== 'undefined';
                const status = exists ? 'success' : 'error';
                const icon = exists ? '✅' : '❌';
                functionsHtml += `<div><span class="status-indicator status-${status}"></span>${icon} ${func}</div>`;
            });
            document.getElementById('functionsStatus').innerHTML = functionsHtml;
            
            // فحص CSS
            document.getElementById('cssStatus').innerHTML = '<div><span class="status-indicator status-success"></span>✅ تم تحميل ملفات CSS</div>';
        }
        
        // اختبار تحميل الصور
        function testImageLoading() {
            const images = document.querySelectorAll('.sample-image');
            images.forEach((img, index) => {
                setTimeout(() => {
                    if (typeof window.enhanceLazyLoading === 'function') {
                        window.enhanceLazyLoading();
                    }
                    img.src = img.getAttribute('data-src');
                }, index * 500);
            });
            
            appendToOutput('🔄 بدء اختبار تحميل الصور...');
        }
        
        // إصلاح الصور المعطلة
        function fixBrokenImages() {
            if (typeof window.imageDiagnostics === 'object') {
                const fixed = window.imageDiagnostics.fix();
                appendToOutput(`🔧 تم إصلاح ${fixed} صورة معطلة`);
            } else {
                appendToOutput('❌ دالة الإصلاح غير متاحة');
            }
        }
        
        // تشغيل التشخيص
        function runDiagnostics() {
            if (typeof window.imageDiagnostics === 'object') {
                const result = window.imageDiagnostics.runFull();
                updateStats(result.stats);
                appendToOutput('✅ تم تشغيل التشخيص الشامل - راجع وحدة التحكم للتفاصيل');
            } else {
                appendToOutput('❌ أداة التشخيص غير متاحة');
            }
        }
        
        // تحديث الإحصائيات
        function updateStats(stats) {
            document.getElementById('totalImages').textContent = stats.total;
            document.getElementById('loadedImages').textContent = stats.loaded;
            document.getElementById('failedImages').textContent = stats.failed;
            
            const successRate = stats.total > 0 ? ((stats.loaded / stats.total) * 100).toFixed(1) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }
        
        // إضافة نص إلى المخرجات
        function appendToOutput(text) {
            const output = document.getElementById('diagnosticOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<div>[${timestamp}] ${text}</div>`;
            output.scrollTop = output.scrollHeight;
        }
        
        // مسح المخرجات
        function clearOutput() {
            document.getElementById('diagnosticOutput').innerHTML = 'تم مسح النتائج...';
        }
        
        // تشغيل الفحص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            checkSystemStatus();
            appendToOutput('🚀 تم تحميل صفحة الاختبار بنجاح');
            
            // تشغيل تشخيص أولي
            setTimeout(() => {
                if (typeof window.imageDiagnostics === 'object') {
                    const stats = window.imageDiagnostics.diagnose();
                    updateStats(stats);
                }
            }, 1000);
        });
    </script>
</body>
</html>
