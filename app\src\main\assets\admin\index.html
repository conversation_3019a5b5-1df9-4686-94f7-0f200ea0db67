<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ لوحة الإدارة الموحدة - Mod Etaris</title>
    <link rel="stylesheet" href="unified-admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <div class="header-content">
                <h1><i class="fas fa-cogs"></i> لوحة الإدارة الموحدة</h1>
                <p class="header-subtitle">إدارة شاملة وسهلة لجميع جوانب تطبيق Mod Etaris</p>
            </div>
            <div class="header-stats">
                <div class="stat-item">
                    <i class="fas fa-cube"></i>
                    <span id="total-mods">-</span>
                    <small>إجمالي المودات</small>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span id="total-users">-</span>
                    <small>المستخدمين النشطين</small>
                </div>
                <div class="stat-item">
                    <i class="fas fa-download"></i>
                    <span id="total-downloads">-</span>
                    <small>إجمالي التحميلات</small>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="admin-tabs">
            <button class="tab-button active" data-tab="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                لوحة المعلومات
            </button>
            <button class="tab-button" data-tab="mods">
                <i class="fas fa-cube"></i>
                إدارة المودات
            </button>
            <button class="tab-button" data-tab="banners">
                <i class="fas fa-image"></i>
                البانرات الإعلانية
            </button>
            <button class="tab-button" data-tab="subscriptions">
                <i class="fas fa-crown"></i>
                الاشتراكات المجانية
            </button>
            <button class="tab-button" data-tab="dialogs">
                <i class="fas fa-comment-dots"></i>
                المربعات المخصصة
            </button>
            <button class="tab-button" data-tab="users">
                <i class="fas fa-users"></i>
                إدارة المستخدمين
            </button>
            <button class="tab-button" data-tab="analytics">
                <i class="fas fa-chart-bar"></i>
                التحليلات والتقارير
            </button>
            <button class="tab-button" data-tab="content">
                <i class="fas fa-folder-open"></i>
                إدارة المحتوى
            </button>
            <button class="tab-button" data-tab="notifications">
                <i class="fas fa-bell"></i>
                الإشعارات
            </button>
            <button class="tab-button" data-tab="maintenance">
                <i class="fas fa-tools"></i>
                الصيانة والتحسين
            </button>
            <button class="tab-button" data-tab="interactive-dashboard">
                <i class="fas fa-chart-pie"></i>
                لوحة المعلومات التفاعلية
            </button>
            <button class="tab-button" data-tab="permissions">
                <i class="fas fa-shield-alt"></i>
                الأذونات والأدوار
            </button>
            <button class="tab-button" data-tab="backup-system">
                <i class="fas fa-save"></i>
                النسخ الاحتياطي
            </button>
            <button class="tab-button" data-tab="performance-monitor">
                <i class="fas fa-tachometer-alt"></i>
                مراقب الأداء
            </button>
            <button class="tab-button" data-tab="audit-system">
                <i class="fas fa-clipboard-list"></i>
                السجلات والتدقيق
            </button>
            <button class="tab-button" data-tab="advanced-settings">
                <i class="fas fa-cogs"></i>
                الإعدادات المتقدمة
            </button>
            <button class="tab-button" data-tab="media-manager">
                <i class="fas fa-images"></i>
                إدارة الوسائط
            </button>
            <button class="tab-button" data-tab="settings">
                <i class="fas fa-cog"></i>
                الإعدادات العامة
            </button>
        </div>

        <!-- Tab Contents -->
        <div class="tab-content active" id="dashboard">
            <div class="dashboard-grid">
                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
                    <div class="quick-actions">
                        <button class="quick-btn" onclick="openQuickModAdd()">
                            <i class="fas fa-plus"></i>
                            إضافة مود جديد
                        </button>
                        <button class="quick-btn" onclick="openQuickBannerAdd()">
                            <i class="fas fa-image"></i>
                            إنشاء بانر إعلاني
                        </button>
                        <button class="quick-btn" onclick="openQuickCampaignAdd()">
                            <i class="fas fa-crown"></i>
                            حملة اشتراك سريعة
                        </button>
                        <button class="quick-btn" onclick="openSystemStatus()">
                            <i class="fas fa-heartbeat"></i>
                            حالة النظام
                        </button>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <h3><i class="fas fa-clock"></i> النشاط الأخير</h3>
                    <div id="recent-activity" class="activity-list">
                        <div class="loading">جاري التحميل...</div>
                    </div>
                </div>

                <!-- System Health -->
                <div class="dashboard-card">
                    <h3><i class="fas fa-heartbeat"></i> صحة النظام</h3>
                    <div class="system-health">
                        <div class="health-item">
                            <span class="health-label">قاعدة البيانات</span>
                            <span class="health-status" id="db-status">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </div>
                        <div class="health-item">
                            <span class="health-label">تخزين الصور</span>
                            <span class="health-status" id="storage-status">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </div>
                        <div class="health-item">
                            <span class="health-label">الاتصال</span>
                            <span class="health-status" id="connection-status">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mods Management Tab -->
        <div class="tab-content" id="mods">
            <div class="section-header">
                <h2><i class="fas fa-cube"></i> إدارة المودات</h2>
                <button class="primary-btn" onclick="openModManager()">
                    <i class="fas fa-plus"></i>
                    إضافة مود جديد
                </button>
            </div>

            <div class="management-grid">
                <div class="management-card" onclick="openFeaturedAddons()">
                    <div class="card-icon">⭐</div>
                    <h3>المودات المميزة</h3>
                    <p>إدارة المودات التي تظهر بتأثيرات خاصة</p>
                    <span class="card-count" id="featured-count">-</span>
                </div>

                <div class="management-card" onclick="openFreeAddons()">
                    <div class="card-icon">🎁</div>
                    <h3>الإضافات المجانية</h3>
                    <p>إدارة قسم الإضافات المجانية</p>
                    <span class="card-count" id="free-addons-count">-</span>
                </div>

                <div class="management-card" onclick="openSuggestedMods()">
                    <div class="card-icon">💡</div>
                    <h3>المودات المقترحة</h3>
                    <p>إدارة المودات المقترحة للمستخدمين</p>
                    <span class="card-count" id="suggested-count">-</span>
                </div>

                <div class="management-card" onclick="openCustomSections()">
                    <div class="card-icon">🎯</div>
                    <h3>الأقسام المخصصة</h3>
                    <p>إنشاء وإدارة أقسام مخصصة</p>
                    <span class="card-count" id="sections-count">-</span>
                </div>
            </div>
        </div>

        <!-- Banners Management Tab -->
        <div class="tab-content" id="banners">
            <div class="section-header">
                <h2><i class="fas fa-image"></i> إدارة البانرات الإعلانية</h2>
                <button class="primary-btn" onclick="openBannerCreator()">
                    <i class="fas fa-plus"></i>
                    إنشاء بانر جديد
                </button>
            </div>

            <div class="banner-types">
                <div class="banner-type-card" onclick="createRegularBanner()">
                    <div class="type-icon">🖼️</div>
                    <h3>بانر عادي</h3>
                    <p>بانر يوجه لرابط خارجي</p>
                </div>

                <div class="banner-type-card" onclick="createModBanner()">
                    <div class="type-icon">🎮</div>
                    <h3>بانر مود</h3>
                    <p>بانر يعرض تفاصيل مود محدد</p>
                </div>

                <div class="banner-type-card" onclick="createSubscriptionBanner()">
                    <div class="type-icon">👑</div>
                    <h3>منشئ الاشتراك الموحد</h3>
                    <p>إنشاء حملة اشتراك مجاني بطرق متعددة</p>
                </div>

                <div class="banner-type-card" onclick="openUnifiedCreator()">
                    <div class="type-icon">🚀</div>
                    <h3>منشئ موحد</h3>
                    <p>إنشاء حملة اشتراك مع بانر في خطوة واحدة</p>
                </div>

                <div class="banner-type-card" onclick="openBackupAdsManager()">
                    <div class="type-icon">🛡️</div>
                    <h3>الإعلانات الاحتياطية</h3>
                    <p>إدارة الإعلانات التي تظهر عند فشل AdMob</p>
                </div>

                <div class="banner-type-card" onclick="openBackupAdsTest()">
                    <div class="type-icon">🧪</div>
                    <h3>اختبار الإعلانات</h3>
                    <p>اختبار وفحص نظام الإعلانات الاحتياطية</p>
                </div>
            </div>

            <div class="banners-list" id="banners-list">
                <div class="loading">جاري تحميل البانرات...</div>
            </div>
        </div>

        <!-- Subscriptions Management Tab -->
        <div class="tab-content" id="subscriptions">
            <div class="section-header">
                <h2><i class="fas fa-crown"></i> إدارة الاشتراكات المجانية</h2>
                <button class="primary-btn" onclick="showUnifiedSubscriptionCreator()">
                    <i class="fas fa-magic"></i>
                    منشئ الاشتراك الموحد
                </button>
            </div>

            <!-- طرق إنشاء الاشتراك -->
            <div class="subscription-methods">
                <h3>طرق إنشاء الاشتراك المجاني:</h3>
                <div class="methods-grid">
                    <div class="method-card quick-card" onclick="window.open('easy_campaign_creator.html', '_blank')">
                        <div class="method-icon">⚡</div>
                        <h4>إنشاء سريع</h4>
                        <p>قوالب جاهزة وإعداد سريع</p>
                    </div>

                    <div class="method-card advanced-card" onclick="window.open('subscription_admin.html', '_blank')">
                        <div class="method-icon">🎯</div>
                        <h4>إنشاء متقدم</h4>
                        <p>تحكم كامل في الإعدادات</p>
                    </div>

                    <div class="method-card complete-card" onclick="window.open('unified-subscription-banner.html', '_blank')">
                        <div class="method-icon">🚀</div>
                        <h4>إنشاء شامل</h4>
                        <p>حملة + بانر في خطوات منظمة</p>
                    </div>

                    <div class="method-card manage-card" onclick="window.open('enhanced_tasks_admin.html', '_blank')">
                        <div class="method-icon">📊</div>
                        <h4>إدارة الحملات</h4>
                        <p>عرض وإدارة الحملات الموجودة</p>
                    </div>

                    <!-- الطرق الإضافية -->
                    <div class="method-card popup-card" onclick="window.open('banner_admin.html', '_blank')">
                        <div class="method-icon">🪟</div>
                        <h4>نوافذ منبثقة</h4>
                        <p>نوافذ تفاعلية للاشتراك</p>
                    </div>

                    <div class="method-card dialog-card" onclick="window.open('custom_dialogs.html', '_blank')">
                        <div class="method-icon">💬</div>
                        <h4>مربعات مخصصة</h4>
                        <p>مربعات حوار للترويج</p>
                    </div>

                    <div class="method-card backup-card" onclick="window.open('backup-ads-manager.html', '_blank')">
                        <div class="method-icon">🛡️</div>
                        <h4>إعلانات احتياطية</h4>
                        <p>إعلانات عند فشل AdMob</p>
                    </div>

                    <div class="method-card notification-card" onclick="showUnifiedSubscriptionCreator()">
                        <div class="method-icon">🔔</div>
                        <h4>إشعارات التطبيق</h4>
                        <p>إشعارات للترويج للاشتراك</p>
                    </div>

                    <!-- المنشئ الذكي الجديد -->
                    <div class="method-card smart-card" onclick="window.open('smart-subscription-creator.html', '_blank')">
                        <div class="method-icon">🧠</div>
                        <h4>المنشئ الذكي</h4>
                        <p>منشئ احترافي بذكاء اصطناعي</p>
                    </div>
                </div>
            </div>

            <div class="subscription-stats">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-info">
                        <span class="stat-number" id="active-campaigns">-</span>
                        <span class="stat-label">حملات نشطة</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-info">
                        <span class="stat-number" id="active-subscribers">-</span>
                        <span class="stat-label">مشتركين نشطين</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-info">
                        <span class="stat-number" id="completed-tasks">-</span>
                        <span class="stat-label">مهام مكتملة</span>
                    </div>
                </div>
            </div>

            <div class="campaigns-list" id="campaigns-list">
                <div class="loading">جاري تحميل الحملات...</div>
            </div>
        </div>

        <!-- Custom Dialogs Tab -->
        <div class="tab-content" id="dialogs">
            <div class="section-header">
                <h2><i class="fas fa-comment-dots"></i> إدارة المربعات المخصصة</h2>
                <button class="primary-btn" onclick="openDialogCreator()">
                    <i class="fas fa-plus"></i>
                    إنشاء مربع جديد
                </button>
            </div>

            <div class="dialog-types">
                <div class="dialog-type-card" onclick="createCustomDialog()">
                    <div class="type-icon">💬</div>
                    <h3>مربع إشعار مخصص</h3>
                    <p>مربع يظهر قبل عرض تفاصيل المود</p>
                </div>

                <div class="dialog-type-card" onclick="createCopyrightDialog()">
                    <div class="type-icon">⚖️</div>
                    <h3>مربع حقوق الطبع</h3>
                    <p>مربع خاص بحقوق الطبع والنشر</p>
                </div>
            </div>

            <div class="dialogs-list" id="dialogs-list">
                <div class="loading">جاري تحميل المربعات...</div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-content" id="settings">
            <div class="section-header">
                <h2><i class="fas fa-cog"></i> الإعدادات العامة</h2>
            </div>

            <div class="settings-grid">
                <div class="settings-card">
                    <h3><i class="fas fa-clock"></i> إعدادات المودات الجديدة</h3>
                    <div class="setting-item">
                        <label>مدة عرض المودات الجديدة</label>
                        <span id="new-mods-duration">7 أيام</span>
                    </div>
                    <div class="setting-item">
                        <label>عدد المودات في الصفحة الرئيسية</label>
                        <span id="new-mods-limit">10 مودات</span>
                    </div>
                    <button class="primary-btn" onclick="openNewModsSettings()">
                        <i class="fas fa-cog"></i>
                        إدارة الإعدادات
                    </button>
                </div>

                <div class="settings-card">
                    <h3><i class="fas fa-database"></i> إعدادات قاعدة البيانات</h3>
                    <div class="setting-item">
                        <label>حالة الاتصال</label>
                        <span id="db-connection-status" class="status-indicator">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </div>
                    <button class="secondary-btn" onclick="testDatabaseConnection()">
                        اختبار الاتصال
                    </button>
                </div>

                <div class="settings-card">
                    <h3><i class="fas fa-cloud"></i> إعدادات التخزين</h3>
                    <div class="setting-item">
                        <label>مساحة التخزين المستخدمة</label>
                        <span id="storage-usage">-</span>
                    </div>
                    <button class="secondary-btn" onclick="checkStorageUsage()">
                        فحص المساحة
                    </button>
                </div>

                <div class="settings-card">
                    <h3><i class="fas fa-tools"></i> أدوات الصيانة</h3>
                    <button class="secondary-btn" onclick="cleanupOldData()">
                        تنظيف البيانات القديمة
                    </button>
                    <button class="secondary-btn" onclick="optimizeDatabase()">
                        تحسين قاعدة البيانات
                    </button>
                    <button class="secondary-btn" onclick="exportBackup()">
                        تصدير نسخة احتياطية
                    </button>
                </div>
            </div>
        </div>

        <!-- Users Management Tab -->
        <div class="tab-content" id="users">
            <div class="tab-header">
                <h2><i class="fas fa-users"></i> إدارة المستخدمين</h2>
                <p>إدارة شاملة للمستخدمين وتتبع أنشطتهم</p>
            </div>

            <div class="users-dashboard">
                <!-- User Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-users"></i></div>
                        <div class="stat-info">
                            <h3 id="total-users">-</h3>
                            <p>إجمالي المستخدمين</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-user-clock"></i></div>
                        <div class="stat-info">
                            <h3 id="active-users">-</h3>
                            <p>المستخدمون النشطون</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-user-plus"></i></div>
                        <div class="stat-info">
                            <h3 id="new-users-today">-</h3>
                            <p>مستخدمون جدد اليوم</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-download"></i></div>
                        <div class="stat-info">
                            <h3 id="total-downloads">-</h3>
                            <p>إجمالي التحميلات</p>
                        </div>
                    </div>
                </div>

                <!-- User Management Tools -->
                <div class="management-section">
                    <div class="section-header">
                        <h3><i class="fas fa-tools"></i> أدوات إدارة المستخدمين</h3>
                    </div>
                    <div class="tools-grid">
                        <button class="tool-btn" onclick="openUsersList()">
                            <i class="fas fa-list"></i>
                            قائمة المستخدمين
                        </button>
                        <button class="tool-btn" onclick="openUserAnalytics()">
                            <i class="fas fa-chart-line"></i>
                            تحليل سلوك المستخدمين
                        </button>
                        <button class="tool-btn" onclick="openBannedUsers()">
                            <i class="fas fa-user-slash"></i>
                            المستخدمون المحظورون
                        </button>
                        <button class="tool-btn" onclick="openUserReviews()">
                            <i class="fas fa-star"></i>
                            التقييمات والمراجعات
                        </button>
                    </div>
                </div>

                <!-- Recent User Activity -->
                <div class="activity-section">
                    <div class="section-header">
                        <h3><i class="fas fa-history"></i> النشاط الأخير للمستخدمين</h3>
                        <button class="refresh-btn" onclick="refreshUserActivity()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="activity-list" id="user-activity-list">
                        <!-- User activities will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-content" id="analytics">
            <div class="tab-header">
                <h2><i class="fas fa-chart-bar"></i> التحليلات والتقارير</h2>
                <p>تحليلات شاملة وتقارير مفصلة عن أداء التطبيق</p>
            </div>

            <div class="analytics-dashboard">
                <!-- Analytics Overview -->
                <div class="analytics-overview">
                    <div class="overview-card">
                        <h3><i class="fas fa-download"></i> تحليل التحميلات</h3>
                        <div class="chart-container">
                            <canvas id="downloads-chart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <span>اليوم: <strong id="downloads-today">-</strong></span>
                            <span>هذا الأسبوع: <strong id="downloads-week">-</strong></span>
                            <span>هذا الشهر: <strong id="downloads-month">-</strong></span>
                        </div>
                    </div>

                    <div class="overview-card">
                        <h3><i class="fas fa-eye"></i> تحليل المشاهدات</h3>
                        <div class="chart-container">
                            <canvas id="views-chart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <span>اليوم: <strong id="views-today">-</strong></span>
                            <span>هذا الأسبوع: <strong id="views-week">-</strong></span>
                            <span>هذا الشهر: <strong id="views-month">-</strong></span>
                        </div>
                    </div>
                </div>

                <!-- Report Generation -->
                <div class="reports-section">
                    <div class="section-header">
                        <h3><i class="fas fa-file-alt"></i> إنشاء التقارير</h3>
                    </div>
                    <div class="report-tools">
                        <div class="report-options">
                            <label>نوع التقرير:</label>
                            <select id="report-type">
                                <option value="downloads">تقرير التحميلات</option>
                                <option value="users">تقرير المستخدمين</option>
                                <option value="revenue">تقرير الإيرادات</option>
                                <option value="errors">تقرير الأخطاء</option>
                            </select>
                        </div>
                        <div class="report-options">
                            <label>الفترة الزمنية:</label>
                            <select id="report-period">
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>
                        <button class="primary-btn" onclick="generateReport()">
                            <i class="fas fa-chart-line"></i>
                            إنشاء التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Management Tab -->
        <div class="tab-content" id="content">
            <div class="tab-header">
                <h2><i class="fas fa-folder-open"></i> إدارة المحتوى</h2>
                <p>إدارة شاملة للمودات والوسائط والمحتوى</p>
            </div>

            <div class="content-dashboard">
                <!-- Content Statistics -->
                <div class="content-stats">
                    <div class="stat-item">
                        <i class="fas fa-cube"></i>
                        <span>إجمالي المودات: <strong id="total-mods">-</strong></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>في انتظار الموافقة: <strong id="pending-mods">-</strong></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-images"></i>
                        <span>إجمالي الصور: <strong id="total-images">-</strong></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-hdd"></i>
                        <span>استخدام التخزين: <strong id="storage-used">-</strong></span>
                    </div>
                </div>

                <!-- Content Management Tools -->
                <div class="content-tools">
                    <div class="tool-section">
                        <h3><i class="fas fa-upload"></i> رفع المحتوى</h3>
                        <div class="upload-area" id="mod-upload-area">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>اسحب وأفلت ملفات المودات هنا أو انقر للاختيار</p>
                            <input type="file" id="mod-file-input" multiple accept=".zip,.mcpack,.mcworld">
                        </div>
                        <button class="primary-btn" onclick="uploadMods()">
                            <i class="fas fa-upload"></i>
                            رفع المودات
                        </button>
                    </div>

                    <div class="tool-section">
                        <h3><i class="fas fa-check-circle"></i> الموافقة على المودات</h3>
                        <div class="pending-mods-list" id="pending-mods-list">
                            <!-- Pending mods will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        </div>

        <!-- Notifications Tab -->
        <div class="tab-content" id="notifications">
            <div class="tab-header">
                <h2><i class="fas fa-bell"></i> إدارة الإشعارات</h2>
                <p>إرسال وإدارة الإشعارات والرسائل للمستخدمين</p>
            </div>

            <div class="notifications-dashboard">
                <!-- Send Notification -->
                <div class="notification-composer">
                    <h3><i class="fas fa-paper-plane"></i> إرسال إشعار جديد</h3>
                    <div class="composer-form">
                        <div class="form-group">
                            <label>نوع الإشعار:</label>
                            <select id="notification-type">
                                <option value="general">إشعار عام</option>
                                <option value="update">تحديث التطبيق</option>
                                <option value="promotion">عرض ترويجي</option>
                                <option value="warning">تحذير</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>العنوان:</label>
                            <input type="text" id="notification-title" placeholder="عنوان الإشعار">
                        </div>
                        <div class="form-group">
                            <label>المحتوى:</label>
                            <textarea id="notification-content" placeholder="محتوى الإشعار"></textarea>
                        </div>
                        <div class="form-group">
                            <label>المستهدفون:</label>
                            <select id="notification-target">
                                <option value="all">جميع المستخدمين</option>
                                <option value="active">المستخدمون النشطون</option>
                                <option value="new">المستخدمون الجدد</option>
                                <option value="premium">المشتركون المميزون</option>
                            </select>
                        </div>
                        <button class="primary-btn" onclick="sendNotification()">
                            <i class="fas fa-send"></i>
                            إرسال الإشعار
                        </button>
                    </div>
                </div>

                <!-- Notification History -->
                <div class="notification-history">
                    <h3><i class="fas fa-history"></i> سجل الإشعارات</h3>
                    <div class="notifications-list" id="notifications-history">
                        <!-- Notification history will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Maintenance Tab -->
        <div class="tab-content" id="maintenance">
            <div class="tab-header">
                <h2><i class="fas fa-tools"></i> الصيانة والتحسين</h2>
                <p>أدوات الصيانة وتحسين أداء التطبيق</p>
            </div>

            <div class="maintenance-dashboard">
                <!-- System Health -->
                <div class="system-health">
                    <h3><i class="fas fa-heartbeat"></i> صحة النظام</h3>
                    <div class="health-indicators">
                        <div class="health-item">
                            <span class="health-label">قاعدة البيانات:</span>
                            <span class="health-status" id="db-status">-</span>
                        </div>
                        <div class="health-item">
                            <span class="health-label">التخزين:</span>
                            <span class="health-status" id="storage-status">-</span>
                        </div>
                        <div class="health-item">
                            <span class="health-label">الشبكة:</span>
                            <span class="health-status" id="network-status">-</span>
                        </div>
                        <div class="health-item">
                            <span class="health-label">الأداء:</span>
                            <span class="health-status" id="performance-status">-</span>
                        </div>
                    </div>
                    <button class="secondary-btn" onclick="checkSystemHealth()">
                        <i class="fas fa-sync-alt"></i>
                        فحص صحة النظام
                    </button>
                </div>

                <!-- Maintenance Tools -->
                <div class="maintenance-tools">
                    <h3><i class="fas fa-wrench"></i> أدوات الصيانة</h3>
                    <div class="tools-grid">
                        <div class="tool-card">
                            <h4><i class="fas fa-database"></i> تنظيف قاعدة البيانات</h4>
                            <p>حذف البيانات القديمة وغير المستخدمة</p>
                            <button class="tool-btn" onclick="cleanDatabase()">تنظيف</button>
                        </div>
                        <div class="tool-card">
                            <h4><i class="fas fa-compress"></i> ضغط الصور</h4>
                            <p>ضغط الصور لتوفير مساحة التخزين</p>
                            <button class="tool-btn" onclick="compressImages()">ضغط</button>
                        </div>
                        <div class="tool-card">
                            <h4><i class="fas fa-trash"></i> حذف الملفات المؤقتة</h4>
                            <p>حذف الملفات المؤقتة والتخزين المؤقت</p>
                            <button class="tool-btn" onclick="clearTempFiles()">حذف</button>
                        </div>
                        <div class="tool-card">
                            <h4><i class="fas fa-download"></i> نسخة احتياطية</h4>
                            <p>إنشاء نسخة احتياطية من البيانات</p>
                            <button class="tool-btn" onclick="createBackup()">إنشاء</button>
                        </div>
                        <div class="tool-card">
                            <h4><i class="fas fa-exclamation-triangle"></i> إدارة أخطاء التحميل</h4>
                            <p>مراقبة وإصلاح مشاكل تحميل المودات</p>
                            <button class="tool-btn" onclick="openDownloadErrorManager()">إدارة الأخطاء</button>
                        </div>
                        <div class="tool-card">
                            <h4><i class="fas fa-shield-alt"></i> نظام التحميل الاحتياطي</h4>
                            <p>إدارة روابط التحميل الاحتياطية</p>
                            <button class="tool-btn" onclick="openBackupSystemManager()">إدارة النظام</button>
                        </div>
                    </div>
                </div>

                <!-- Performance Monitor -->
                <div class="performance-monitor">
                    <h3><i class="fas fa-tachometer-alt"></i> مراقب الأداء</h3>
                    <div class="performance-metrics">
                        <div class="metric-item">
                            <span class="metric-label">استخدام المعالج:</span>
                            <div class="metric-bar">
                                <div class="metric-fill" id="cpu-usage" style="width: 0%"></div>
                            </div>
                            <span class="metric-value" id="cpu-value">0%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">استخدام الذاكرة:</span>
                            <div class="metric-bar">
                                <div class="metric-fill" id="memory-usage" style="width: 0%"></div>
                            </div>
                            <span class="metric-value" id="memory-value">0%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">استخدام التخزين:</span>
                            <div class="metric-bar">
                                <div class="metric-fill" id="disk-usage" style="width: 0%"></div>
                            </div>
                            <span class="metric-value" id="disk-value">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Dashboard Tab -->
        <div class="tab-content" id="interactive-dashboard">
            <div class="tab-header">
                <h2><i class="fas fa-chart-pie"></i> لوحة المعلومات التفاعلية</h2>
                <div class="dashboard-controls">
                    <button id="dashboard-refresh" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <label class="auto-refresh-toggle">
                        <input type="checkbox" id="auto-refresh-toggle" checked>
                        <span>التحديث التلقائي</span>
                    </label>
                </div>
            </div>

            <div class="dashboard-metrics">
                <div class="metrics-grid">
                    <div class="metric-card" id="total-users-metric">
                        <div class="metric-icon"><i class="fas fa-users"></i></div>
                        <div class="metric-info">
                            <div class="metric-value">-</div>
                            <div class="metric-label">إجمالي المستخدمين</div>
                        </div>
                    </div>
                    <div class="metric-card" id="active-users-metric">
                        <div class="metric-icon"><i class="fas fa-user-clock"></i></div>
                        <div class="metric-info">
                            <div class="metric-value">-</div>
                            <div class="metric-label">المستخدمون النشطون</div>
                        </div>
                    </div>
                    <div class="metric-card" id="total-mods-metric">
                        <div class="metric-icon"><i class="fas fa-cube"></i></div>
                        <div class="metric-info">
                            <div class="metric-value">-</div>
                            <div class="metric-label">إجمالي المودات</div>
                        </div>
                    </div>
                    <div class="metric-card" id="daily-revenue-metric">
                        <div class="metric-icon"><i class="fas fa-dollar-sign"></i></div>
                        <div class="metric-info">
                            <div class="metric-value">-</div>
                            <div class="metric-label">الإيرادات اليومية</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-status">
                <div class="status-info">
                    <span>آخر تحديث: <span id="last-refresh-time">-</span></span>
                    <span>التحديث التلقائي: <span id="auto-refresh-status" class="auto-refresh-status active">مفعل</span></span>
                </div>
            </div>
        </div>

        <!-- Permissions Management Tab -->
        <div class="tab-content" id="permissions">
            <div class="tab-header">
                <h2><i class="fas fa-shield-alt"></i> إدارة الأذونات والأدوار</h2>
                <p>إدارة أذونات المستخدمين والأدوار الإدارية</p>
            </div>

            <div class="permissions-dashboard">
                <div class="current-user-info">
                    <h3>المستخدم الحالي</h3>
                    <div class="user-role-display">
                        <span id="current-user-role">-</span>
                        <span id="user-role-badge" class="role-badge">-</span>
                    </div>
                </div>

                <div class="permissions-tools">
                    <button class="tool-btn" onclick="createRoleManagementInterface()">
                        <i class="fas fa-users-cog"></i>
                        إدارة الأدوار
                    </button>
                    <button class="tool-btn" onclick="exportPermissionsData()">
                        <i class="fas fa-download"></i>
                        تصدير الأذونات
                    </button>
                </div>
            </div>
        </div>

        <!-- Backup System Tab -->
        <div class="tab-content" id="backup-system">
            <div class="tab-header">
                <h2><i class="fas fa-save"></i> نظام النسخ الاحتياطي</h2>
                <p>إدارة النسخ الاحتياطية التلقائية والاستعادة</p>
            </div>

            <div class="backup-dashboard">
                <div class="backup-controls">
                    <button class="primary-btn" onclick="createManualBackup()">
                        <i class="fas fa-plus"></i>
                        إنشاء نسخة احتياطية
                    </button>
                    <button class="secondary-btn" onclick="exportBackupSettings()">
                        <i class="fas fa-cog"></i>
                        إعدادات النسخ الاحتياطي
                    </button>
                </div>

                <div class="backup-settings">
                    <h3>إعدادات النسخ الاحتياطي</h3>
                    <div class="settings-grid">
                        <label>
                            <input type="checkbox" id="auto-backup-toggle">
                            النسخ الاحتياطي التلقائي
                        </label>
                        <select id="backup-frequency">
                            <option value="daily">يومياً</option>
                            <option value="weekly">أسبوعياً</option>
                            <option value="monthly">شهرياً</option>
                        </select>
                    </div>
                </div>

                <div class="backup-history">
                    <h3>سجل النسخ الاحتياطية</h3>
                    <div id="backup-history-list">
                        <!-- Backup history will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Monitor Tab -->
        <div class="tab-content" id="performance-monitor">
            <div class="tab-header">
                <h2><i class="fas fa-tachometer-alt"></i> مراقب الأداء المباشر</h2>
                <div class="monitor-controls">
                    <button id="monitoring-toggle" class="monitoring-toggle start">بدء المراقبة</button>
                    <span id="monitoring-status" class="monitoring-status inactive">متوقف</span>
                </div>
            </div>

            <div class="performance-dashboard">
                <div class="performance-metrics">
                    <div class="metric-group">
                        <h3>استخدام النظام</h3>
                        <div class="system-metrics">
                            <div class="system-metric" id="cpu-usage-system">
                                <label>المعالج:</label>
                                <div class="system-metric-bar good"></div>
                                <span class="system-metric-value">0%</span>
                            </div>
                            <div class="system-metric" id="memory-usage-system">
                                <label>الذاكرة:</label>
                                <div class="system-metric-bar good"></div>
                                <span class="system-metric-value">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="performance-alerts" id="performance-alerts">
                    <!-- Performance alerts will appear here -->
                </div>
            </div>
        </div>

        <!-- Audit System Tab -->
        <div class="tab-content" id="audit-system">
            <div class="tab-header">
                <h2><i class="fas fa-clipboard-list"></i> نظام السجلات والتدقيق</h2>
                <p>مراقبة وتتبع جميع أنشطة النظام</p>
            </div>

            <div class="audit-dashboard">
                <div class="audit-controls">
                    <button class="primary-btn" onclick="exportAuditLogs()">
                        <i class="fas fa-download"></i>
                        تصدير السجلات
                    </button>
                    <button class="secondary-btn danger" onclick="clearAuditLogs()">
                        <i class="fas fa-trash"></i>
                        مسح السجلات
                    </button>
                </div>

                <div class="audit-filters">
                    <div class="filter-group">
                        <input type="date" id="audit-date-from" placeholder="من تاريخ">
                        <input type="date" id="audit-date-to" placeholder="إلى تاريخ">
                        <select id="audit-level-filter">
                            <option value="all">جميع المستويات</option>
                            <option value="info">معلومات</option>
                            <option value="warn">تحذير</option>
                            <option value="error">خطأ</option>
                        </select>
                    </div>
                </div>

                <div class="audit-logs">
                    <div id="audit-logs-container">
                        <!-- Audit logs will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Settings Tab -->
        <div class="tab-content" id="advanced-settings">
            <div class="tab-header">
                <h2><i class="fas fa-cogs"></i> الإعدادات المتقدمة</h2>
                <p>إدارة شاملة لجميع إعدادات النظام</p>
            </div>

            <div class="settings-dashboard">
                <div class="settings-navigation">
                    <div id="settings-tabs" class="settings-tabs">
                        <!-- Settings tabs will be generated here -->
                    </div>
                </div>

                <div class="settings-content">
                    <div id="settings-forms" class="settings-forms">
                        <!-- Settings forms will be generated here -->
                    </div>
                </div>

                <div class="settings-actions">
                    <button id="save-settings-btn" class="primary-btn" disabled>محفوظ</button>
                    <button id="reset-settings-btn" class="secondary-btn">إعادة تعيين</button>
                    <button id="export-settings-btn" class="secondary-btn">تصدير</button>
                    <button id="import-settings-btn" class="secondary-btn">استيراد</button>
                    <input type="file" id="import-settings-file" accept=".json" style="display: none;">
                </div>
            </div>
        </div>

        <!-- Media Manager Tab -->
        <div class="tab-content" id="media-manager">
            <div class="tab-header">
                <h2><i class="fas fa-images"></i> إدارة الملفات والوسائط</h2>
                <p>إدارة شاملة للملفات والصور والوسائط</p>
            </div>

            <div class="media-dashboard">
                <div class="media-toolbar">
                    <div class="media-actions">
                        <button id="upload-files-btn" class="primary-btn">
                            <i class="fas fa-upload"></i>
                            رفع ملفات
                        </button>
                        <button id="create-folder-btn" class="secondary-btn">
                            <i class="fas fa-folder-plus"></i>
                            مجلد جديد
                        </button>
                        <button id="delete-selected-files" class="secondary-btn danger" disabled>
                            <i class="fas fa-trash"></i>
                            حذف المحدد
                        </button>
                    </div>
                    <div class="media-info">
                        <span id="files-count">0 ملف</span>
                        <span id="selection-info"></span>
                    </div>
                </div>

                <div class="media-navigation">
                    <div id="folder-navigation" class="folder-navigation">
                        <!-- Folder navigation will be generated here -->
                    </div>
                </div>

                <div class="media-content">
                    <div id="media-files-grid" class="media-files-grid">
                        <!-- Media files will be displayed here -->
                    </div>
                </div>

                <div id="media-drop-zone" class="media-drop-zone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>اسحب وأفلت الملفات هنا للرفع</p>
                </div>

                <input type="file" id="media-file-input" multiple style="display: none;">
            </div>
        </div>

        </div>

        <!-- Footer -->
        <div class="admin-footer">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="../index.html" class="footer-link">
                        <i class="fas fa-home"></i>
                        العودة للتطبيق الرئيسي
                    </a>
                    <a href="#" onclick="showSystemInfo()" class="footer-link">
                        <i class="fas fa-info-circle"></i>
                        معلومات النظام
                    </a>
                    <a href="#" onclick="showHelp()" class="footer-link">
                        <i class="fas fa-question-circle"></i>
                        المساعدة
                    </a>
                </div>
                <div class="footer-info">
                    <span>لوحة الإدارة الموحدة - Mod Etaris v2.0</span>
                    <span id="last-update">آخر تحديث: -</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <span id="loading-text">جاري التحميل...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Load Supabase Manager first to avoid multiple instances -->
    <script src="../supabase-manager.js"></script>

    <!-- Admin Panel Fixes - Load before other admin scripts -->
    <script src="admin-panel-fixes.js"></script>

    <script src="unified-admin.js"></script>
    <script src="advanced-admin-features.js"></script>
    <script src="interactive-dashboard.js"></script>
    <script src="permissions-manager.js"></script>
    <script src="backup-system.js"></script>
    <script src="performance-monitor.js"></script>
    <script src="audit-system.js"></script>
    <script src="advanced-settings.js"></script>
    <script src="media-manager.js"></script>

    <!-- Advanced Admin Styles -->
    <link rel="stylesheet" href="advanced-admin-styles.css">
    <link rel="stylesheet" href="enhanced-admin-styles.css">
</body>
</html>
