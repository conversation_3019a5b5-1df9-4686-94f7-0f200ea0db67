# 🚀 إعداد سريع للمربعات المخصصة ثنائية اللغة
# Quick Setup for Bilingual Custom Dialogs

## ✅ ما تم إنجازه / What's Been Completed

تم تحديث التطبيق بنجاح لدعم المربعات المخصصة ثنائية اللغة! 🎉

### التحديثات المنجزة:
- ✅ تحديث دالة `showCustomDialog` لدعم اللغتين
- ✅ تحديث دالة `checkCustomDialog` لجلب الحقول ثنائية اللغة
- ✅ إضافة منطق اختيار اللغة التلقائي
- ✅ دعم اتجاه النص (RTL/LTR)
- ✅ إنشاء ملف اختبار شامل

## 🔧 خطوة واحدة مطلوبة / One Step Required

### تحديث قاعدة البيانات:
انسخ والصق هذا الكود في **Supabase SQL Editor**:

```sql
-- إضافة الحقول الإنجليزية لجدول custom_mod_dialogs
ALTER TABLE custom_mod_dialogs 
ADD COLUMN IF NOT EXISTS title_en VARCHAR(255),
ADD COLUMN IF NOT EXISTS description_en TEXT,
ADD COLUMN IF NOT EXISTS button_text_en VARCHAR(100) DEFAULT 'OK';
```

## 🧪 اختبار النظام / Test the System

### 1. اختبار سريع:
1. افتح التطبيق
2. اذهب إلى إعدادات اللغة واختر العربية
3. انقر على أي مود له مربع مخصص
4. يجب أن يظهر المربع بالعربية

### 2. اختبار شامل:
افتح ملف: `app/src/main/assets/test-bilingual-custom-dialogs.html`

## 🎯 كيف يعمل النظام / How It Works

### عند اختيار اللغة العربية:
```
المستخدم يختار العربية → localStorage.setItem('selectedLanguage', 'ar')
↓
عند النقر على مود → checkCustomDialog()
↓
إذا وُجد مربع مخصص → showCustomDialog()
↓
يتم عرض: title, description, button_text (العربية)
+ اتجاه RTL + زر إغلاق يسار
```

### عند اختيار اللغة الإنجليزية:
```
المستخدم يختار الإنجليزية → localStorage.setItem('selectedLanguage', 'en')
↓
عند النقر على مود → checkCustomDialog()
↓
إذا وُجد مربع مخصص → showCustomDialog()
↓
يتم عرض: title_en, description_en, button_text_en (الإنجليزية)
+ اتجاه LTR + زر إغلاق يمين
```

## 📱 إضافة مربعات جديدة / Adding New Dialogs

### من لوحة الإدارة:
1. اذهب إلى `admin/custom_dialogs.html`
2. املأ الحقول:
   - **عنوان المربع (عربي)**: النص العربي
   - **عنوان المربع (إنجليزي)**: النص الإنجليزي
   - **وصف المربع (عربي)**: الوصف العربي
   - **وصف المربع (إنجليزي)**: الوصف الإنجليزي
   - **نص الزر (عربي)**: "تم" أو نص مخصص
   - **نص الزر (إنجليزي)**: "OK" أو نص مخصص

### مثال على البيانات:
```
العنوان العربي: "تحذير مهم"
العنوان الإنجليزي: "Important Warning"
الوصف العربي: "هذا المود يتطلب إصدار معين من ماين كرافت"
الوصف الإنجليزي: "This mod requires a specific version of Minecraft"
نص الزر العربي: "فهمت"
نص الزر الإنجليزي: "Understood"
```

## 🔍 استكشاف الأخطاء / Troubleshooting

### المربع لا يظهر باللغة الصحيحة:
```javascript
// تحقق من اللغة في console
console.log(localStorage.getItem('selectedLanguage'));
// يجب أن يكون 'ar' أو 'en'
```

### المربع لا يظهر نهائياً:
1. تحقق من وجود بيانات في `custom_mod_dialogs`
2. تحقق من ربط المود في `custom_dialog_mods`
3. تأكد من `is_active = true`

### أخطاء في قاعدة البيانات:
```sql
-- تحقق من وجود الحقول الجديدة
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'custom_mod_dialogs';
```

## 📊 حالة النظام / System Status

### ✅ مكتمل / Completed:
- دعم اللغة العربية والإنجليزية
- اتجاه النص التلقائي
- ترجمة جميع عناصر المربع
- التوافق مع نظام اللغة الموجود

### 🔄 قيد التطوير / In Development:
- إضافة لغات أخرى (اختياري)
- تحسينات إضافية على التصميم

## 🎉 النتيجة النهائية / Final Result

الآن عندما يختار المستخدم:

### 🇸🇦 اللغة العربية:
- المربع يظهر من اليمين إلى اليسار
- جميع النصوص بالعربية
- زر الإغلاق في الجهة اليسرى
- "عدم الظهور مجدداً" بالعربية

### 🇺🇸 اللغة الإنجليزية:
- المربع يظهر من اليسار إلى اليمين
- جميع النصوص بالإنجليزية
- زر الإغلاق في الجهة اليمنى
- "Don't show again" بالإنجليزية

---

**🎯 النظام جاهز للاستخدام!**
**🎯 System Ready to Use!**

فقط نفذ كود SQL المذكور أعلاه وستكون جاهزاً! 🚀
