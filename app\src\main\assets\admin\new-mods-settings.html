<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات المودات الجديدة - New Mods Settings</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .setting-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .setting-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 215, 0, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
        }
        
        .admin-button {
            background: linear-gradient(135deg, #ffd700, #ffb300);
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .admin-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
        }
        
        .admin-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .success-message {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 15px 0;
            display: none;
            animation: slideIn 0.3s ease;
        }
        
        .error-message {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 15px 0;
            display: none;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .input-field {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            padding: 12px 15px;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }
        
        .range-slider {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }
        
        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ffd700;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }
        
        .range-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ffd700;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }
        
        .preview-box {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-active {
            background: #4caf50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }
        
        .status-inactive {
            background: #f44336;
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
        }
    </style>
</head>
<body class="min-h-screen text-white p-6">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="admin-container p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-yellow-400 mb-2">
                        <i class="fas fa-cog mr-3"></i>
                        إعدادات المودات الجديدة
                    </h1>
                    <p class="text-gray-300">تحكم في مدة عرض المودات الجديدة وإعداداتها</p>
                </div>
                <button onclick="goBack()" class="admin-button">
                    <i class="fas fa-arrow-left mr-2"></i>
                    رجوع
                </button>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div id="successMessage" class="success-message"></div>
        <div id="errorMessage" class="error-message"></div>

        <!-- Main Settings -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Duration Settings -->
            <div class="setting-card p-6">
                <h2 class="text-xl font-bold text-yellow-400 mb-4">
                    <i class="fas fa-clock mr-2"></i>
                    مدة عرض المودات الجديدة
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">المدة بالأيام:</label>
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <input type="range" id="durationSlider" class="range-slider" 
                                   min="1" max="30" value="7" 
                                   oninput="updateDurationDisplay(this.value)">
                            <div class="flex items-center">
                                <span id="durationValue" class="text-2xl font-bold text-yellow-400">7</span>
                                <span class="text-sm text-gray-300 mr-2">أيام</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium mb-2">أو أدخل قيمة مخصصة:</label>
                        <input type="number" id="customDuration" class="input-field" 
                               min="1" max="365" placeholder="أدخل عدد الأيام"
                               onchange="updateSliderFromInput(this.value)">
                    </div>
                </div>
            </div>

            <!-- Cache Settings -->
            <div class="setting-card p-6">
                <h2 class="text-xl font-bold text-yellow-400 mb-4">
                    <i class="fas fa-database mr-2"></i>
                    إعدادات التخزين المؤقت
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">مدة التخزين المؤقت (بالدقائق):</label>
                        <select id="cacheMinutes" class="input-field">
                            <option value="1">دقيقة واحدة</option>
                            <option value="3" selected>3 دقائق</option>
                            <option value="5">5 دقائق</option>
                            <option value="10">10 دقائق</option>
                            <option value="15">15 دقيقة</option>
                            <option value="30">30 دقيقة</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium mb-2">عدد المودات في الصفحة الرئيسية:</label>
                        <select id="homePageLimit" class="input-field">
                            <option value="5">5 مودات</option>
                            <option value="10" selected>10 مودات</option>
                            <option value="15">15 مود</option>
                            <option value="20">20 مود</option>
                            <option value="25">25 مود</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="setting-card p-6 mt-6">
            <h2 class="text-xl font-bold text-yellow-400 mb-4">
                <i class="fas fa-eye mr-2"></i>
                معاينة الإعدادات
            </h2>
            
            <div class="preview-box">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold text-yellow-400" id="previewDuration">7</div>
                        <div class="text-sm text-gray-300">أيام عرض المودات الجديدة</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-blue-400" id="previewCache">3</div>
                        <div class="text-sm text-gray-300">دقائق تخزين مؤقت</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-green-400" id="previewLimit">10</div>
                        <div class="text-sm text-gray-300">مودات في الصفحة الرئيسية</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="setting-card p-6 mt-6">
            <h2 class="text-xl font-bold text-yellow-400 mb-4">
                <i class="fas fa-info-circle mr-2"></i>
                الحالة الحالية
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center justify-between p-4 bg-black bg-opacity-30 rounded-lg">
                    <span>حالة النظام:</span>
                    <span class="flex items-center">
                        نشط
                        <span class="status-indicator status-active"></span>
                    </span>
                </div>
                <div class="flex items-center justify-between p-4 bg-black bg-opacity-30 rounded-lg">
                    <span>آخر تحديث:</span>
                    <span id="lastUpdate">لم يتم التحديث بعد</span>
                </div>
                <div class="flex items-center justify-between p-4 bg-black bg-opacity-30 rounded-lg">
                    <span>عدد المودات الجديدة الحالية:</span>
                    <span id="currentNewMods">جاري التحميل...</span>
                </div>
                <div class="flex items-center justify-between p-4 bg-black bg-opacity-30 rounded-lg">
                    <span>حالة قاعدة البيانات:</span>
                    <span class="flex items-center" id="dbStatus">
                        جاري الفحص...
                        <span class="status-indicator status-inactive"></span>
                    </span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-4 mt-6 justify-center">
            <button onclick="saveSettings()" class="admin-button" id="saveBtn">
                <i class="fas fa-save mr-2"></i>
                حفظ الإعدادات
            </button>
            <button onclick="testSettings()" class="admin-button">
                <i class="fas fa-flask mr-2"></i>
                اختبار الإعدادات
            </button>
            <button onclick="resetToDefault()" class="admin-button" style="background: linear-gradient(135deg, #f44336, #d32f2f);">
                <i class="fas fa-undo mr-2"></i>
                إعادة تعيين افتراضية
            </button>
            <button onclick="clearCache()" class="admin-button" style="background: linear-gradient(135deg, #ff9800, #f57c00);">
                <i class="fas fa-trash mr-2"></i>
                مسح التخزين المؤقت
            </button>
        </div>
    </div>

    <!-- Include Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="new-mods-settings.js"></script>
</body>
</html>
