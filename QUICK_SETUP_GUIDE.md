# دليل الإعداد السريع - Quick Setup Guide

## نظام اختيار اللغة - Language Selection System

### خطوات التثبيت السريعة / Quick Installation Steps

#### 1. قاعدة البيانات / Database Setup
```sql
-- في Supabase SQL Editor أو أي أداة إدارة قاعدة بيانات
-- In Supabase SQL Editor or any database management tool

-- إنشاء جدول إحصائيات اللغة
-- Create language statistics table
CREATE TABLE IF NOT EXISTS user_language_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    selected_language VARCHAR(10) NOT NULL CHECK (selected_language IN ('ar', 'en')),
    selection_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_agent TEXT,
    ip_address INET,
    device_info JSONB DEFAULT '{}',
    CONSTRAINT unique_user_language UNIQUE (user_id)
);

-- إضا<PERSON>ة عمود الوصف العربي لجدول المودات
-- Add Arabic description column to mods table
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;
```

#### 2. الملفات المطلوبة / Required Files
تأكد من وجود هذه الملفات / Make sure these files exist:

```
app/src/main/assets/
├── language-selection.html     ✅ (جديد/New)
├── translations.js            ✅ (جديد/New)
├── index.html                 ✅ (محدث/Updated)
├── search.html                ✅ (محدث/Updated)
├── script.js                  ✅ (محدث/Updated)
├── supabase-manager.js        ✅ (محدث/Updated)
└── style.css                  ✅ (محدث/Updated)

database/
├── user_language_stats.sql    ✅ (جديد/New)
└── add_arabic_description.sql ✅ (جديد/New)
```

#### 3. اختبار النظام / System Testing

##### أ. اختبار صفحة اختيار اللغة / Test Language Selection Page
1. احذف localStorage للتطبيق / Clear app localStorage:
   ```javascript
   localStorage.clear();
   ```
2. افتح التطبيق / Open the app
3. يجب أن تظهر صفحة اختيار اللغة / Language selection page should appear
4. اختر لغة وتأكد من الانتقال للصفحة الرئيسية / Select language and verify redirect

##### ب. اختبار الترجمة / Test Translation
1. افتح `test_language_system.html` في المتصفح / Open `test_language_system.html` in browser
2. اختبر تبديل اللغات / Test language switching
3. تحقق من عرض النصوص المترجمة / Verify translated text display

##### ج. اختبار وصف المودات / Test Mod Descriptions
1. أضف وصف عربي لبعض المودات في قاعدة البيانات / Add Arabic descriptions to some mods in database:
   ```sql
   UPDATE mods SET description_ar = 'وصف عربي للمود' WHERE id = 'mod_id_here';
   ```
2. غير اللغة للعربية وتحقق من عرض الوصف العربي / Switch to Arabic and verify Arabic description display

### استكشاف الأخطاء / Troubleshooting

#### مشكلة: صفحة اختيار اللغة لا تظهر
**الحل:**
```javascript
// في console المتصفح
localStorage.removeItem('languageSelected');
localStorage.removeItem('selectedLanguage');
location.reload();
```

#### مشكلة: الترجمات لا تعمل
**الحل:**
1. تأكد من تحميل `translations.js` قبل `script.js`
2. تحقق من console للأخطاء
3. تأكد من استخدام `t('key')` بدلاً من النص المباشر

#### مشكلة: اتجاه النص لا يتغير
**الحل:**
```javascript
// تحقق من اتجاه النص الحالي
console.log(document.documentElement.dir);
console.log(document.documentElement.lang);

// إعادة تعيين اللغة
translationManager.setLanguage('ar'); // أو 'en'
```

#### مشكلة: قاعدة البيانات
**الحل:**
1. تأكد من تشغيل ملفات SQL
2. تحقق من صلاحيات الوصول للجداول
3. تأكد من إعداد Supabase بشكل صحيح

### إضافة ترجمات جديدة / Adding New Translations

#### 1. في ملف translations.js
```javascript
// أضف في كلا القسمين ar و en
ar: {
    'new_key': 'النص العربي',
    // ...
},
en: {
    'new_key': 'English Text',
    // ...
}
```

#### 2. في الكود
```javascript
// استخدم الترجمة الجديدة
const translatedText = t('new_key');
```

### إضافة وصوفات عربية للمودات / Adding Arabic Descriptions to Mods

#### طريقة 1: SQL مباشر / Direct SQL
```sql
UPDATE mods 
SET description_ar = 'الوصف العربي هنا' 
WHERE id = 'mod_id_here';
```

#### طريقة 2: من خلال admin panel
1. أضف حقل `description_ar` في نموذج إضافة/تعديل المودات
2. استخدم الدالة المساعدة `getLocalizedDescription()` في العرض

### نصائح مهمة / Important Tips

#### 1. الأداء / Performance
- نظام الترجمة محمل مرة واحدة فقط
- localStorage يحفظ اختيار المستخدم محلياً
- قاعدة البيانات تحفظ الإحصائيات فقط

#### 2. الأمان / Security
- لا يتم حفظ معلومات شخصية حساسة
- معرف المستخدم مجهول ومولد محلياً
- جميع البيانات قابلة للحذف

#### 3. التوافق / Compatibility
- يعمل مع جميع المتصفحات الحديثة
- دعم كامل للـ RTL والـ LTR
- متوافق مع النظام الحالي

### الخطوات التالية / Next Steps

#### 1. تحسينات مقترحة / Suggested Improvements
- إضافة المزيد من اللغات
- تحسين واجهة اختيار اللغة
- إضافة ترجمات للرسائل الأخرى

#### 2. مراقبة الإحصائيات / Monitor Statistics
```sql
-- عرض إحصائيات اللغات
SELECT * FROM language_usage_stats;

-- عرض الاختيارات اليومية
SELECT * FROM daily_language_selections;
```

#### 3. صيانة دورية / Regular Maintenance
- تحديث الترجمات حسب الحاجة
- إضافة وصوفات عربية للمودات الجديدة
- مراجعة الإحصائيات شهرياً

---

## اتصل بنا / Contact

للدعم الفني أو الاستفسارات:
- راجع ملف `LANGUAGE_SYSTEM_README.md` للتفاصيل الكاملة
- استخدم `test_language_system.html` للاختبار
- تحقق من console المتصفح للأخطاء

For technical support or questions:
- Check `LANGUAGE_SYSTEM_README.md` for complete details
- Use `test_language_system.html` for testing
- Check browser console for errors
