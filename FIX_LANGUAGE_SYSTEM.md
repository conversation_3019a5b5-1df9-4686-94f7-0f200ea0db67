# إصلاح نظام اختيار اللغة - Fix Language System

## المشكلة الحالية / Current Issue
```
GET https://ytqxxodyecdeosnqoure.supabase.co/rest/v1/user_language_stats?select=id&limit=1 404 (Not Found)
Table user_language_stats does not exist, creating...
Could not create table user_language_stats via RPC: Could not find the function public.execute_sql(sql_query) in the schema cache
```

## الحل السريع / Quick Solution

### 1. تشغيل SQL في Supabase Dashboard
اذهب إلى **Supabase Dashboard** → **SQL Editor** وقم بتشغيل الملف:
```
EXECUTE_THIS_SQL.sql
```

أو انسخ والصق هذا الكود:

```sql
-- إنشاء جدول لغات المستخدمين
CREATE TABLE IF NOT EXISTS user_languages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL UNIQUE,
    selected_language VARCHAR(10) NOT NULL DEFAULT 'en' CHECK (selected_language IN ('ar', 'en')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    device_info JSONB DEFAULT '{}',
    user_agent TEXT
);

-- إضافة عمود الوصف العربي
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;

-- إنشاء فهارس
CREATE INDEX IF NOT EXISTS idx_user_languages_language ON user_languages(selected_language);
CREATE INDEX IF NOT EXISTS idx_mods_description_ar ON mods USING gin(to_tsvector('arabic', description_ar));
```

### 2. التحقق من إنشاء الجداول
بعد تشغيل SQL، تحقق من وجود الجداول:
```sql
-- التحقق من جدول اللغات
SELECT COUNT(*) FROM user_languages;

-- التحقق من عمود الوصف العربي
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'mods' AND column_name = 'description_ar';
```

### 3. اختبار النظام
1. **امسح localStorage**:
   ```javascript
   localStorage.clear();
   ```

2. **أعد تحميل التطبيق** - يجب أن تظهر صفحة اختيار اللغة

3. **اختر لغة** وتحقق من حفظ البيانات:
   ```sql
   SELECT * FROM user_languages ORDER BY created_at DESC LIMIT 5;
   ```

## الملفات المحدثة / Updated Files

### ✅ تم التحديث / Updated:
- `app/src/main/assets/supabase-manager.js` - اسم الجدول الجديد
- `app/src/main/assets/language-selection.html` - استخدام الجدول الجديد
- `app/src/main/assets/script.js` - دوال حفظ اللغة
- `database/user_language_stats.sql` - هيكل الجدول المحدث

### 📁 ملفات جديدة / New Files:
- `EXECUTE_THIS_SQL.sql` - للتنفيذ المباشر
- `FIX_LANGUAGE_SYSTEM.md` - هذا الملف

## اختبار الوظائف / Test Functions

### 1. اختبار حفظ اللغة
```javascript
// في console المتصفح
saveUserLanguagePreference('ar').then(result => {
    console.log('Language saved:', result);
});
```

### 2. اختبار الوصف المترجم
```javascript
// اختبار دالة الوصف المترجم
const testMod = {
    description: 'English description',
    description_ar: 'الوصف العربي'
};
console.log(getLocalizedDescription(testMod));
```

### 3. اختبار نظام الترجمة
```javascript
// اختبار الترجمات
console.log(t('shader_warning_title'));
console.log(t('description'));
```

## استعلامات مفيدة / Useful Queries

### إحصائيات اللغات
```sql
SELECT 
    selected_language,
    COUNT(*) as users,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 1) as percentage
FROM user_languages 
GROUP BY selected_language;
```

### المودات التي تحتاج وصف عربي
```sql
SELECT 
    name,
    category,
    CASE 
        WHEN description_ar IS NULL THEN '❌ لا يوجد'
        WHEN description_ar = '' THEN '⚠️ فارغ'
        ELSE '✅ موجود'
    END as arabic_status
FROM mods 
ORDER BY category, name
LIMIT 20;
```

### آخر المستخدمين
```sql
SELECT 
    user_id,
    selected_language,
    created_at,
    device_info->>'device' as device,
    device_info->>'browser' as browser
FROM user_languages 
ORDER BY created_at DESC 
LIMIT 10;
```

## استكشاف الأخطاء / Troubleshooting

### مشكلة: الجدول غير موجود
**الحل**: تشغيل `EXECUTE_THIS_SQL.sql` في Supabase

### مشكلة: صفحة اختيار اللغة لا تظهر
**الحل**:
```javascript
localStorage.removeItem('languageSelected');
localStorage.removeItem('selectedLanguage');
location.reload();
```

### مشكلة: الترجمات لا تعمل
**الحل**: تحقق من تحميل `translations.js` قبل `script.js`

### مشكلة: الوصف العربي لا يظهر
**الحل**: 
1. تحقق من وجود عمود `description_ar` في جدول `mods`
2. أضف وصوفات عربية للمودات:
```sql
UPDATE mods 
SET description_ar = 'وصف عربي للمود' 
WHERE id = 'mod_id_here';
```

## الخطوات التالية / Next Steps

### 1. إضافة وصوفات عربية
```sql
-- تحديث المودات بوصوفات عربية
UPDATE mods 
SET description_ar = 'الوصف العربي المناسب'
WHERE name = 'اسم المود' AND description_ar IS NULL;
```

### 2. مراقبة الإحصائيات
```sql
-- مراقبة يومية للإحصائيات
SELECT * FROM language_usage_stats;
```

### 3. تحسينات مستقبلية
- إضافة المزيد من اللغات
- تحسين واجهة اختيار اللغة
- إضافة ترجمات للرسائل الأخرى

## ملاحظات مهمة / Important Notes

- ✅ النظام يعمل الآن مع جدول `user_languages` بدلاً من `user_language_stats`
- ✅ تم إصلاح جميع المراجع في الكود
- ✅ تم إضافة عمود `description_ar` لجدول `mods`
- ✅ تم إنشاء دوال مساعدة للتعامل مع اللغات
- ⚠️ تأكد من تشغيل SQL قبل اختبار التطبيق

## الدعم / Support

إذا واجهت أي مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تحقق من Supabase Dashboard للجداول
3. استخدم `test_language_system.html` للاختبار
4. راجع هذا الملف للحلول

---

**تم إصلاح النظام! 🎉**
**System Fixed! 🎉**
