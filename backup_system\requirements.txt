# متطلبات نظام النسخ الاحتياطي الاحترافي
# Professional Backup System Requirements

# Firebase Admin SDK
firebase-admin>=6.2.0

# Supabase Client
supabase>=1.0.0

# Database Connectivity
asyncpg>=0.28.0
psycopg2-binary>=2.9.0

# HTTP Requests
requests>=2.31.0
aiohttp>=3.8.0

# File Handling
zipfile36>=0.1.3

# Configuration
python-dotenv>=1.0.0

# Logging and Monitoring
colorlog>=6.7.0

# Email Notifications
secure-smtplib>=0.1.1

# Async Support
asyncio-mqtt>=0.13.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# Encryption (Optional)
cryptography>=41.0.0

# Progress Bars
tqdm>=4.65.0

# JSON Handling
ujson>=5.8.0

# Date/Time Handling
python-dateutil>=2.8.0

# System Monitoring
psutil>=5.9.0

# Web Framework (for API endpoints)
fastapi>=0.100.0
uvicorn>=0.23.0

# Validation
pydantic>=2.0.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
