# Script to set <PERSON><PERSON><PERSON><PERSON>HOME and build the Android project
# This script sets JAV<PERSON><PERSON>HOME to the JDK-24 path and runs the Gradle build

# Set JAVA_HOME for the current session
$env:JAVA_HOME = "C:\Program Files\Java\jdk-24"

# Display the current JAVA_HOME
Write-Host "JAVA_HOME is set to: $env:JAVA_HOME"

# Run the Gradle build
Write-Host "Running Gradle build..."
.\gradlew assembleDebug

# Check if build was successful
if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful! APK should be available in app\build\outputs\apk\debug\"
} else {
    Write-Host "Build failed with exit code $LASTEXITCODE"
}
