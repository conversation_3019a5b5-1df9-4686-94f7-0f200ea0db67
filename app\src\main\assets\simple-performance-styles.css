/**
 * أنماط الأداء البسيط
 * Simple Performance Styles
 * 
 * يحل مشاكل البطء والتعليق بإزالة التأثيرات المعقدة
 * Solves slowness and freezing by removing complex effects
 */

/* إزالة التأثيرات المعقدة للأجهزة الضعيفة */
.simple-low-end * {
    animation-duration: 0.1s !important;
    transition-duration: 0.1s !important;
    transform: none !important;
    filter: none !important;
    box-shadow: none !important;
}

/* تحسين الصور */
img {
    image-rendering: optimizeSpeed !important;
    will-change: auto !important;
    transform: none !important;
}

/* تحسين التمرير */
.items-container {
    scroll-behavior: auto !important;
    -webkit-overflow-scrolling: touch !important;
}

/* إزالة التأثيرات المعقدة من الكاردات */
.mod-card,
.item {
    animation: none !important;
    transition: opacity 0.2s ease !important;
    transform: none !important;
    filter: none !important;
    box-shadow: none !important;
}

.mod-card:hover,
.item:hover {
    transform: translateY(-2px) !important;
    box-shadow: none !important;
}

/* إزالة جميع التأثيرات المتحركة المعقدة */
.orb,
.floating-orb,
.sparkle,
.pixel-particle,
.free-addon-pixel-particle {
    display: none !important;
}

/* تبسيط مؤشرات التحميل */
.loading-spinner {
    width: 40px !important;
    height: 40px !important;
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #ffcc00 !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تبسيط الأزرار */
button {
    transition: background-color 0.2s ease !important;
    transform: none !important;
    filter: none !important;
}

button:hover {
    opacity: 0.9 !important;
    transform: none !important;
}

/* إزالة التأثيرات من الأقسام */
.section {
    animation: none !important;
    transition: none !important;
    transform: none !important;
}

/* تبسيط النوافذ المنبثقة */
.modal,
.overlay {
    animation: none !important;
    transition: none !important;
    transform: none !important;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* إزالة التأثيرات من النصوص */
h1, h2, h3, h4, h5, h6, p, span {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    filter: none !important;
}

/* تحسين الشبكة */
.grid,
.flex {
    will-change: auto !important;
    transform: none !important;
}

/* إزالة التأثيرات من الأيقونات */
.icon,
.social-icon {
    animation: none !important;
    transition: opacity 0.2s ease !important;
    transform: none !important;
    filter: none !important;
}

/* تبسيط شريط التنقل */
.navbar,
.navigation {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    backdrop-filter: none !important;
}

/* إزالة التأثيرات من القوائم */
.menu,
.dropdown {
    animation: none !important;
    transition: opacity 0.2s ease !important;
    transform: none !important;
}

/* تحسين النماذج */
input,
textarea,
select {
    transition: border-color 0.2s ease !important;
    transform: none !important;
    filter: none !important;
}

/* إزالة التأثيرات من الخلفيات */
.background,
.bg {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    filter: none !important;
}

/* تبسيط الحدود والظلال */
.border,
.shadow {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    animation: none !important;
    transition: none !important;
}

/* إزالة التأثيرات من الجداول */
table,
tr,
td,
th {
    animation: none !important;
    transition: none !important;
    transform: none !important;
}

/* تحسين الفيديو والصوت */
video,
audio {
    will-change: auto !important;
    transform: none !important;
    filter: none !important;
}

/* إزالة التأثيرات من العناصر المخصصة */
.custom-element,
.widget {
    animation: none !important;
    transition: opacity 0.2s ease !important;
    transform: none !important;
    filter: none !important;
}

/* تحسين الطباعة */
@media print {
    * {
        animation: none !important;
        transition: none !important;
        transform: none !important;
        filter: none !important;
        box-shadow: none !important;
    }
}

/* تحسين الشاشات الصغيرة */
@media (max-width: 768px) {
    * {
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
    }
    
    .mod-card,
    .item {
        margin-bottom: 10px !important;
    }
}

/* تحسين الأجهزة الضعيفة */
@media (max-width: 480px) {
    * {
        animation: none !important;
        transition: none !important;
        transform: none !important;
        filter: none !important;
    }
    
    .loading-spinner {
        width: 30px !important;
        height: 30px !important;
    }
}

/* إزالة التأثيرات من الرسوم المتحركة المخصصة */
.animate-visible,
.initial-hidden-animate,
.fade-in,
.slide-in,
.zoom-in {
    animation: none !important;
    transition: opacity 0.2s ease !important;
    transform: none !important;
    opacity: 1 !important;
}

/* تحسين الأداء العام */
* {
    box-sizing: border-box !important;
    will-change: auto !important;
}

/* إزالة التأثيرات من الكاردات المميزة */
.featured-addon,
.popular-mod,
.free-addon-mod {
    animation: none !important;
    transition: opacity 0.2s ease !important;
    transform: none !important;
    filter: none !important;
}

/* تبسيط الأيقونات الشعبية */
.popular-icon {
    animation: none !important;
    transition: none !important;
    transform: none !important;
}

/* إزالة تأثيرات التوهج */
.all-category-glow,
.glow-effect {
    box-shadow: none !important;
    animation: none !important;
    transition: none !important;
}

/* تحسين الأداء للعناصر الكبيرة */
.large-content,
.heavy-element {
    will-change: auto !important;
    transform: none !important;
    filter: none !important;
    animation: none !important;
}
