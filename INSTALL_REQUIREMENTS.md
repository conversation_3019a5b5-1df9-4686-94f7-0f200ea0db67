# تثبيت المتطلبات - Install Requirements

## 🚀 تثبيت سريع / Quick Installation

### تشغيل الأمر التالي في PowerShell:
```powershell
# الانتقال إلى مجلد الأداة
cd "C:\Users\<USER>\modetaris\app\src\main\assets\send addons"

# تثبيت جميع المكتبات المطلوبة
pip install supabase google-generativeai pyperclip cloudscraper requests beautifulsoup4 pillow
```

## 📦 المكتبات المطلوبة / Required Libraries

### 1. **Supabase** - قاعدة البيانات والتخزين
```bash
pip install supabase
```
**الاستخدام:** الاتصال بقاعدة البيانات ورفع الملفات

### 2. **Google Generative AI** - الذكاء الاصطناعي
```bash
pip install google-generativeai
```
**الاستخدام:** إنشاء الوصوفات العربية والإنجليزية

### 3. **PyPerClip** - النسخ واللصق
```bash
pip install pyperclip
```
**الاستخدام:** أزرار النسخ واللصق في الواجهة

### 4. **CloudScraper** - تجاوز حماية Cloudflare
```bash
pip install cloudscraper
```
**الاستخدام:** تحميل المودات من المواقع المحمية

### 5. **Requests** - طلبات HTTP
```bash
pip install requests
```
**الاستخدام:** تحميل الملفات والصور من الإنترنت

### 6. **BeautifulSoup4** - تحليل HTML
```bash
pip install beautifulsoup4
```
**الاستخدام:** استخراج البيانات من صفحات الويب

### 7. **Pillow** - معالجة الصور
```bash
pip install pillow
```
**الاستخدام:** ضغط وتحسين الصور

## 🔧 إعداد قاعدة البيانات / Database Setup

### إضافة عمود الوصف العربي:
```sql
-- تشغيل في Supabase SQL Editor
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;

-- التحقق من إضافة العمود
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'mods' AND column_name = 'description_ar';
```

## 🔑 إعداد مفاتيح API / API Keys Setup

### ملف `config.json` موجود بالفعل مع المفاتيح:
```json
{
  "gemini_api_keys": [
    "AIzaSyCi7DaxX-N17Fkd5ehjMfj5-F7qbKyhv1Y",
    "AIzaSyANSq4W3sbXYOxuwx13hXfXTaY-uPqrUxE",
    "AIzaSyBs3sWzuIqwHfMg4tttQGBu9K9w0ETR58I",
    "AIzaSyC1RYK4MXuEKKmi0VVsctiaDfRNOWQtTlg",
    "AIzaSyClKt_GqxphM-5aFYUMLzT925OgTtxxIhY"
  ]
}
```

## ✅ التحقق من التثبيت / Installation Verification

### 1. فحص المكتبات المثبتة:
```bash
pip list | findstr "supabase google pyperclip cloudscraper requests beautifulsoup4 pillow"
```

### 2. اختبار الاستيراد:
```python
# تشغيل في Python Console
import supabase
import google.generativeai
import pyperclip
import cloudscraper
import requests
import bs4
from PIL import Image

print("جميع المكتبات مثبتة بنجاح!")
```

### 3. اختبار الأداة:
```bash
python mod_processor.py
```

**يجب أن ترى:**
```
Supabase storage client initialized and tested successfully.
Supabase App DB client initialized and tested successfully.
```

## 🔄 إعادة التثبيت / Reinstallation

### إذا واجهت مشاكل:
```bash
# إزالة المكتبات القديمة
pip uninstall supabase google-generativeai pyperclip cloudscraper requests beautifulsoup4 pillow -y

# تنظيف الكاش
pip cache purge

# إعادة التثبيت
pip install supabase google-generativeai pyperclip cloudscraper requests beautifulsoup4 pillow
```

## 🌐 متطلبات النظام / System Requirements

### Python:
- **الإصدار:** Python 3.8 أو أحدث
- **التحقق:** `python --version`

### نظام التشغيل:
- **Windows 10/11** ✅
- **macOS** ✅  
- **Linux** ✅

### الذاكرة:
- **الحد الأدنى:** 4GB RAM
- **المستحسن:** 8GB RAM أو أكثر

### التخزين:
- **المساحة المطلوبة:** 500MB للمكتبات
- **مساحة إضافية:** 2GB للملفات المؤقتة

### الإنترنت:
- **اتصال مستقر** لـ Supabase و Gemini API
- **سرعة مستحسنة:** 10 Mbps أو أكثر

## 🛠️ استكشاف أخطاء التثبيت / Installation Troubleshooting

### مشكلة: "pip is not recognized"
```bash
# إضافة Python إلى PATH
# أو استخدام المسار الكامل:
"C:\Program Files\Python313\Scripts\pip.exe" install supabase
```

### مشكلة: "Permission denied"
```bash
# تشغيل كمدير:
pip install --user supabase google-generativeai pyperclip cloudscraper
```

### مشكلة: "Failed building wheel"
```bash
# تحديث pip أولاً:
python -m pip install --upgrade pip
pip install setuptools wheel
```

### مشكلة: "SSL Certificate error"
```bash
# تجاهل شهادات SSL (مؤقتاً):
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org supabase
```

## 📋 قائمة التحقق النهائية / Final Checklist

### قبل تشغيل الأداة:
- [ ] Python 3.8+ مثبت
- [ ] جميع المكتبات مثبتة بنجاح
- [ ] ملف `config.json` موجود
- [ ] عمود `description_ar` مضاف لقاعدة البيانات
- [ ] اتصال إنترنت مستقر

### اختبار سريع:
```bash
# 1. الانتقال للمجلد
cd "C:\Users\<USER>\modetaris\app\src\main\assets\send addons"

# 2. اختبار الاستيراد
python -c "import supabase, google.generativeai; print('OK')"

# 3. تشغيل الأداة
python mod_processor.py
```

## 🎉 النتيجة المتوقعة / Expected Result

عند تشغيل الأداة بنجاح، ستحصل على:

### ✅ واجهة مستخدم كاملة مع:
- قسم استخراج المقالات
- قسم الذكاء الاصطناعي مع أزرار الوصف العربي
- قسم النشر مع حقل الوصف العربي
- جميع الأزرار تعمل بشكل طبيعي

### ✅ رسائل نجاح:
```
Supabase storage client initialized and tested successfully.
Supabase App DB client initialized and tested successfully.
```

### ✅ ميزات جاهزة:
- إنشاء وصوفات عربية بالذكاء الاصطناعي
- نشر المودات مع الوصف العربي
- معالجة الصور والملفات
- استخراج البيانات من المواقع

---

**الآن أنت جاهز لاستخدام أداة نشر مودات ماين كرافت مع ميزة الوصف العربي! 🚀**
