-- ========================================
-- إنشاء دالة execute_sql في Supabase
-- Create execute_sql function in Supabase
-- ========================================

-- هذه الدالة تسمح بتنفيذ استعلامات SQL من التطبيق
-- This function allows executing SQL queries from the application

CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    query_lower TEXT;
BEGIN
    -- تحويل الاستعلام إلى أحرف صغيرة للفحص
    -- Convert query to lowercase for checking
    query_lower := LOWER(TRIM(sql_query));
    
    -- فحص الأمان: منع العمليات الخطيرة
    -- Security check: prevent dangerous operations
    IF query_lower LIKE '%drop %' OR 
       query_lower LIKE '%delete %' OR 
       query_lower LIKE '%truncate %' OR
       query_lower LIKE '%alter %' OR
       query_lower LIKE '%grant %' OR
       query_lower LIKE '%revoke %' OR
       query_lower LIKE '%insert %' OR
       query_lower LIKE '%update %' THEN
        
        -- السماح فقط بـ CREATE TABLE IF NOT EXISTS
        -- Allow only CREATE TABLE IF NOT EXISTS
        IF NOT (query_lower LIKE '%create table if not exists%') THEN
            RAISE EXCEPTION 'Only CREATE TABLE IF NOT EXISTS operations are allowed for security reasons';
        END IF;
    END IF;
    
    -- التحقق من أن الاستعلام يبدأ بـ CREATE
    -- Check that query starts with CREATE
    IF NOT query_lower LIKE 'create%' THEN
        RAISE EXCEPTION 'Only CREATE statements are allowed';
    END IF;
    
    -- تنفيذ الاستعلام
    -- Execute the query
    EXECUTE sql_query;
    
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ وإرجاع false
        -- Log error and return false
        RAISE EXCEPTION 'Error executing SQL: %', SQLERRM;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إضافة تعليق توضيحي
-- Add documentation comment
COMMENT ON FUNCTION execute_sql(TEXT) IS 'Safely executes CREATE TABLE statements from the application';

-- منح الصلاحيات للمستخدمين المناسبين
-- Grant permissions to appropriate users
-- GRANT EXECUTE ON FUNCTION execute_sql(TEXT) TO anon;
-- GRANT EXECUTE ON FUNCTION execute_sql(TEXT) TO authenticated;

-- ملاحظة: قم بإلغاء التعليق عن الأسطر أعلاه إذا كنت تريد السماح للتطبيق بتنفيذ هذه الدالة
-- Note: Uncomment the lines above if you want to allow the application to execute this function
