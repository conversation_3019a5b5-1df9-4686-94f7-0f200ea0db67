<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإعلانات الاحتياطية</title>
    <link rel="stylesheet" href="unified-admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="backup-ads-config.js"></script>
    <script src="../backup-ads-integration.js"></script>
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            border-radius: 20px;
            color: white;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .test-button.secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .test-button.secondary:hover {
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .test-results {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }

        .log-error {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }

        .log-info {
            background: rgba(33, 150, 243, 0.2);
            color: #2196F3;
        }

        .log-warning {
            background: rgba(255, 193, 7, 0.2);
            color: #FFC107;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ffd700;
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <header class="admin-header">
            <div class="header-content">
                <h1><i class="fas fa-vial"></i> اختبار الإعلانات الاحتياطية</h1>
                <div class="header-actions">
                    <button onclick="window.location.href='backup-ads-manager.html'" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> إدارة الإعلانات
                    </button>
                    <button onclick="window.location.href='index.html'" class="btn btn-secondary">
                        <i class="fas fa-home"></i> الرئيسية
                    </button>
                </div>
            </div>
        </header>

        <div class="test-container">
            <!-- System Status -->
            <div class="test-section">
                <h2><i class="fas fa-heartbeat"></i> حالة النظام</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="systemStatus">❓</div>
                        <div class="stat-label">حالة النظام</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalAds">0</div>
                        <div class="stat-label">إجمالي الإعلانات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeAds">0</div>
                        <div class="stat-label">الإعلانات النشطة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="lastTest">-</div>
                        <div class="stat-label">آخر اختبار</div>
                    </div>
                </div>
                <button onclick="checkSystemStatus()" class="test-button">
                    <i class="fas fa-sync"></i> فحص حالة النظام
                </button>
            </div>

            <!-- Basic Tests -->
            <div class="test-section">
                <h2><i class="fas fa-play"></i> الاختبارات الأساسية</h2>
                <button onclick="testInitialization()" class="test-button">
                    <i class="fas fa-power-off"></i> اختبار التهيئة
                </button>
                <button onclick="testDatabaseConnection()" class="test-button">
                    <i class="fas fa-database"></i> اختبار قاعدة البيانات
                </button>
                <button onclick="testAdRetrieval()" class="test-button">
                    <i class="fas fa-download"></i> اختبار جلب الإعلانات
                </button>
                <button onclick="testAdDisplay()" class="test-button">
                    <i class="fas fa-eye"></i> اختبار عرض الإعلان
                </button>
            </div>

            <!-- Advanced Tests -->
            <div class="test-section">
                <h2><i class="fas fa-cogs"></i> الاختبارات المتقدمة</h2>
                
                <div class="form-group">
                    <label for="testCategory">فئة الاختبار:</label>
                    <select id="testCategory">
                        <option value="">جميع الفئات</option>
                        <option value="Addons">Addons</option>
                        <option value="Shaders">Shaders</option>
                        <option value="Maps">Maps</option>
                        <option value="Seeds">Seeds</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="testAdType">نوع الإعلان:</label>
                    <select id="testAdType">
                        <option value="">جميع الأنواع</option>
                        <option value="image">صورة</option>
                        <option value="video">فيديو</option>
                    </select>
                </div>

                <button onclick="testCategoryTargeting()" class="test-button">
                    <i class="fas fa-bullseye"></i> اختبار الاستهداف
                </button>
                <button onclick="testPrioritySystem()" class="test-button">
                    <i class="fas fa-sort-amount-down"></i> اختبار الأولوية
                </button>
                <button onclick="testAnalytics()" class="test-button">
                    <i class="fas fa-chart-line"></i> اختبار الإحصائيات
                </button>
                <button onclick="testErrorHandling()" class="test-button secondary">
                    <i class="fas fa-exclamation-triangle"></i> اختبار معالجة الأخطاء
                </button>
            </div>

            <!-- Performance Tests -->
            <div class="test-section">
                <h2><i class="fas fa-tachometer-alt"></i> اختبارات الأداء</h2>
                <button onclick="testLoadTime()" class="test-button">
                    <i class="fas fa-stopwatch"></i> اختبار سرعة التحميل
                </button>
                <button onclick="testMemoryUsage()" class="test-button">
                    <i class="fas fa-memory"></i> اختبار استخدام الذاكرة
                </button>
                <button onclick="testConcurrentAds()" class="test-button">
                    <i class="fas fa-layer-group"></i> اختبار الإعلانات المتزامنة
                </button>
                <button onclick="runFullTestSuite()" class="test-button secondary">
                    <i class="fas fa-rocket"></i> تشغيل جميع الاختبارات
                </button>
            </div>

            <!-- Test Results -->
            <div class="test-section">
                <h2><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h2>
                <button onclick="clearResults()" class="test-button secondary">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
                <button onclick="exportResults()" class="test-button">
                    <i class="fas fa-download"></i> تصدير النتائج
                </button>
                <div class="test-results" id="testResults">
                    <div class="log-info">جاهز لبدء الاختبارات...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test results storage
        let testResults = [];
        let testStartTime = null;

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة الاختبار', 'info');
            checkSystemStatus();
        });

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = {
                timestamp: timestamp,
                message: message,
                type: type
            };
            
            testResults.push(entry);
            
            const resultsDiv = document.getElementById('testResults');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // Check system status
        async function checkSystemStatus() {
            log('فحص حالة النظام...', 'info');
            
            try {
                // Check if backup ads system is available
                if (typeof window.backupAds === 'undefined') {
                    log('❌ نظام الإعلانات الاحتياطية غير متاح', 'error');
                    document.getElementById('systemStatus').textContent = '❌';
                    return;
                }
                
                // Check if system is enabled
                if (!window.backupAds.enabled()) {
                    log('⚠️ نظام الإعلانات الاحتياطية معطل', 'warning');
                    document.getElementById('systemStatus').textContent = '⚠️';
                    return;
                }
                
                // Initialize system
                const initialized = await window.backupAds.initialize();
                if (initialized) {
                    log('✅ النظام يعمل بشكل صحيح', 'success');
                    document.getElementById('systemStatus').textContent = '✅';
                } else {
                    log('❌ فشل في تهيئة النظام', 'error');
                    document.getElementById('systemStatus').textContent = '❌';
                }
                
                // Update last test time
                document.getElementById('lastTest').textContent = new Date().toLocaleTimeString('ar-SA');
                
            } catch (error) {
                log(`❌ خطأ في فحص النظام: ${error.message}`, 'error');
                document.getElementById('systemStatus').textContent = '❌';
            }
        }

        // Test initialization
        async function testInitialization() {
            log('بدء اختبار التهيئة...', 'info');
            testStartTime = performance.now();
            
            try {
                const result = await window.backupAds.initialize();
                const duration = performance.now() - testStartTime;
                
                if (result) {
                    log(`✅ تم اختبار التهيئة بنجاح (${duration.toFixed(2)}ms)`, 'success');
                } else {
                    log('❌ فشل اختبار التهيئة', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التهيئة: ${error.message}`, 'error');
            }
        }

        // Test database connection
        async function testDatabaseConnection() {
            log('بدء اختبار قاعدة البيانات...', 'info');
            
            try {
                // This would need to be implemented in the backup ads system
                log('⚠️ اختبار قاعدة البيانات غير مُنفذ بعد', 'warning');
            } catch (error) {
                log(`❌ خطأ في اختبار قاعدة البيانات: ${error.message}`, 'error');
            }
        }

        // Test ad retrieval
        async function testAdRetrieval() {
            log('بدء اختبار جلب الإعلانات...', 'info');
            
            try {
                // This would need to be implemented in the backup ads system
                log('⚠️ اختبار جلب الإعلانات غير مُنفذ بعد', 'warning');
            } catch (error) {
                log(`❌ خطأ في اختبار جلب الإعلانات: ${error.message}`, 'error');
            }
        }

        // Test ad display
        async function testAdDisplay() {
            log('بدء اختبار عرض الإعلان...', 'info');
            
            try {
                const result = await window.backupAds.show('test_mod_id', 'Addons');
                
                if (result) {
                    log('✅ تم عرض الإعلان الاحتياطي بنجاح', 'success');
                } else {
                    log('⚠️ لم يتم العثور على إعلانات احتياطية للعرض', 'warning');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار عرض الإعلان: ${error.message}`, 'error');
            }
        }

        // Test category targeting
        async function testCategoryTargeting() {
            const category = document.getElementById('testCategory').value;
            log(`بدء اختبار الاستهداف للفئة: ${category || 'جميع الفئات'}`, 'info');
            
            try {
                const result = await window.backupAds.show('test_mod_id', category);
                
                if (result) {
                    log(`✅ تم اختبار الاستهداف بنجاح للفئة: ${category}`, 'success');
                } else {
                    log(`⚠️ لا توجد إعلانات متاحة للفئة: ${category}`, 'warning');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار الاستهداف: ${error.message}`, 'error');
            }
        }

        // Test priority system
        async function testPrioritySystem() {
            log('بدء اختبار نظام الأولوية...', 'info');
            log('⚠️ اختبار نظام الأولوية غير مُنفذ بعد', 'warning');
        }

        // Test analytics
        async function testAnalytics() {
            log('بدء اختبار الإحصائيات...', 'info');
            log('⚠️ اختبار الإحصائيات غير مُنفذ بعد', 'warning');
        }

        // Test error handling
        async function testErrorHandling() {
            log('بدء اختبار معالجة الأخطاء...', 'info');
            
            try {
                // Test with invalid parameters
                await window.backupAds.show(null, null);
                log('⚠️ لم يتم رفع خطأ كما هو متوقع', 'warning');
            } catch (error) {
                log(`✅ تم اختبار معالجة الأخطاء بنجاح: ${error.message}`, 'success');
            }
        }

        // Test load time
        async function testLoadTime() {
            log('بدء اختبار سرعة التحميل...', 'info');
            
            const startTime = performance.now();
            try {
                await window.backupAds.show('test_mod_id', 'Addons');
                const loadTime = performance.now() - startTime;
                log(`✅ وقت التحميل: ${loadTime.toFixed(2)}ms`, 'success');
            } catch (error) {
                log(`❌ خطأ في اختبار سرعة التحميل: ${error.message}`, 'error');
            }
        }

        // Test memory usage
        function testMemoryUsage() {
            log('بدء اختبار استخدام الذاكرة...', 'info');
            
            if (performance.memory) {
                const memory = performance.memory;
                log(`📊 الذاكرة المستخدمة: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log(`📊 إجمالي الذاكرة: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log(`📊 حد الذاكرة: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`, 'info');
            } else {
                log('⚠️ معلومات الذاكرة غير متاحة في هذا المتصفح', 'warning');
            }
        }

        // Test concurrent ads
        async function testConcurrentAds() {
            log('بدء اختبار الإعلانات المتزامنة...', 'info');
            log('⚠️ اختبار الإعلانات المتزامنة غير مُنفذ بعد', 'warning');
        }

        // Run full test suite
        async function runFullTestSuite() {
            log('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            const tests = [
                testInitialization,
                testDatabaseConnection,
                testAdRetrieval,
                testAdDisplay,
                testCategoryTargeting,
                testPrioritySystem,
                testAnalytics,
                testErrorHandling,
                testLoadTime,
                testMemoryUsage,
                testConcurrentAds
            ];
            
            let passed = 0;
            let failed = 0;
            
            for (const test of tests) {
                try {
                    await test();
                    passed++;
                } catch (error) {
                    failed++;
                    log(`❌ فشل الاختبار: ${error.message}`, 'error');
                }
                
                // Wait a bit between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log(`🏁 انتهت جميع الاختبارات - نجح: ${passed}, فشل: ${failed}`, 'info');
        }

        // Clear results
        function clearResults() {
            testResults = [];
            document.getElementById('testResults').innerHTML = '<div class="log-info">تم مسح النتائج...</div>';
        }

        // Export results
        function exportResults() {
            const data = {
                timestamp: new Date().toISOString(),
                results: testResults
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `backup-ads-test-results-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            URL.revokeObjectURL(url);
            log('✅ تم تصدير النتائج', 'success');
        }
    </script>
</body>
</html>
