# Script to permanently set <PERSON><PERSON><PERSON>_HOME in system environment variables
# This script requires administrator privileges to run

# Set JAVA_HOME system environment variable
[System.Environment]::SetEnvironmentVariable('JAVA_HOME', 'C:\Program Files\Java\jdk-24', [System.EnvironmentVariableTarget]::Machine)

# Verify the change
$newJavaHome = [System.Environment]::GetEnvironmentVariable('JAVA_HOME', [System.EnvironmentVariableTarget]::Machine)
Write-Host "JAVA_HOME is now set to: $newJavaHome"

Write-Host "You may need to restart your terminal or computer for changes to take effect."
