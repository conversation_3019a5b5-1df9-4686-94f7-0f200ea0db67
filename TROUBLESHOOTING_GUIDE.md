# دليل استكشاف الأخطاء وإصلاحها
# Troubleshooting Guide

## 🔧 حل المشاكل الشائعة / Common Issues Solutions

### 1. مشكلة Supabase - خطأ `proxy`

#### المشكلة:
```
Error initializing Supabase storage client: Client.__init__() got an unexpected keyword argument 'proxy'
```

#### السبب:
إصدار مكتبة Supabase الحالي لا يدعم معامل `proxy`.

#### الحل:
✅ **تم إصلاحه في الكود!** تم إزالة معامل `proxy` من إنشاء عميل Supabase.

#### إذا استمرت المشكلة:
```bash
# تحديث مكتبة Supabase
pip install --upgrade supabase

# أو إعادة تثبيتها
pip uninstall supabase
pip install supabase
```

### 2. مشكلة Gemini API - "No Gemini API keys"

#### المشكلة:
```
Warning: No Gemini API keys. AI features disabled.
```

#### السبب:
- ملف `config.json` غير موجود أو فارغ
- مفاتيح API غير صالحة
- مكتبة `google-generativeai` غير مثبتة

#### الحل:

##### أ) تثبيت مكتبة Gemini:
```bash
pip install google-generativeai
```

##### ب) التحقق من ملف التكوين:
```json
{
  "gemini_api_keys": [
    "AIzaSyCi7DaxX-N17Fkd5ehjMfj5-F7qbKyhv1Y",
    "AIzaSyANSq4W3sbXYOxuwx13hXfXTaY-uPqrUxE",
    "AIzaSyBs3sWzuIqwHfMg4tttQGBu9K9w0ETR58I",
    "AIzaSyC1RYK4MXuEKKmi0VVsctiaDfRNOWQtTlg",
    "AIzaSyClKt_GqxphM-5aFYUMLzT925OgTtxxIhY"
  ]
}
```

##### ج) الحصول على مفاتيح جديدة:
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ مفتاح API جديد
3. أضفه إلى ملف `config.json`

### 3. مشكلة قاعدة البيانات - عمود `description_ar` مفقود

#### المشكلة:
```
Error: column "description_ar" does not exist
```

#### الحل:
```sql
-- تشغيل هذا الأمر في Supabase SQL Editor
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;

-- التحقق من إضافة العمود
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'mods' AND column_name = 'description_ar';
```

### 4. مشكلة المكتبات المفقودة

#### أ) مكتبة `pyperclip`:
```bash
pip install pyperclip
```

#### ب) مكتبة `cloudscraper`:
```bash
pip install cloudscraper
```

#### ج) جميع المكتبات المطلوبة:
```bash
pip install supabase google-generativeai pyperclip cloudscraper requests beautifulsoup4 pillow
```

### 5. مشكلة الخطوط العربية

#### المشكلة:
النص العربي لا يظهر بشكل صحيح في الواجهة.

#### الحل:
```python
# في Windows، تأكد من وجود خطوط عربية
# الخطوط المدعومة: Arial, Tahoma, Segoe UI
default_font = ("Arial", 10)
```

### 6. مشكلة الاتصال بالإنترنت

#### المشكلة:
```
Error: Failed to connect to Gemini API
```

#### الحل:
1. **تحقق من الاتصال:**
```bash
ping google.com
```

2. **تحقق من البروكسي:**
```python
# إذا كنت تستخدم بروكسي، أضف:
import os
os.environ['HTTP_PROXY'] = 'your_proxy_here'
os.environ['HTTPS_PROXY'] = 'your_proxy_here'
```

3. **تحقق من الجدار الناري:**
- تأكد من أن Python مسموح في الجدار الناري
- تأكد من أن المنافذ 80 و 443 مفتوحة

## 🚀 خطوات التشغيل الصحيحة / Correct Startup Steps

### 1. التحضير الأولي:
```bash
# 1. الانتقال إلى المجلد الصحيح
cd "C:\Users\<USER>\modetaris\app\src\main\assets\send addons"

# 2. التحقق من وجود الملفات المطلوبة
dir config.json
dir mod_processor.py

# 3. تثبيت المكتبات المطلوبة
pip install supabase google-generativeai pyperclip cloudscraper requests beautifulsoup4 pillow
```

### 2. تحديث قاعدة البيانات:
```sql
-- في Supabase SQL Editor
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;
```

### 3. تشغيل الأداة:
```bash
python mod_processor.py
```

### 4. التحقق من الحالة:
عند تشغيل الأداة، يجب أن ترى:
```
Supabase storage client initialized and tested successfully.
Supabase App DB client initialized and tested successfully.
```

## 🔍 تشخيص المشاكل / Diagnostics

### 1. فحص حالة Supabase:
```python
# في Python Console
from supabase import create_client

# اختبار Storage
storage_client = create_client("https://mwxzwfeqsashcwvqthmd.supabase.co", "your_key")
print(storage_client.storage.list_buckets())

# اختبار Database
app_client = create_client("https://ytqxxodyecdeosnqoure.supabase.co", "your_key")
print(app_client.table('mods').select("id").limit(1).execute())
```

### 2. فحص حالة Gemini:
```python
import google.generativeai as genai
genai.configure(api_key="your_api_key")
model = genai.GenerativeModel('gemini-pro')
response = model.generate_content("Hello")
print(response.text)
```

### 3. فحص الملفات:
```bash
# التحقق من وجود الملفات
ls -la config.json
ls -la mod_processor.py

# التحقق من محتوى config.json
cat config.json
```

## 📋 قائمة التحقق / Checklist

### قبل التشغيل:
- [ ] Python 3.8+ مثبت
- [ ] جميع المكتبات مثبتة
- [ ] ملف `config.json` موجود ويحتوي على مفاتيح صالحة
- [ ] عمود `description_ar` موجود في قاعدة البيانات
- [ ] اتصال إنترنت مستقر

### أثناء التشغيل:
- [ ] لا توجد رسائل خطأ عند بدء التشغيل
- [ ] أزرار الواجهة تعمل بشكل طبيعي
- [ ] يمكن إنشاء الوصوفات العربية
- [ ] يمكن النشر في قاعدة البيانات

### بعد التشغيل:
- [ ] البيانات محفوظة في قاعدة البيانات
- [ ] الوصف العربي ظاهر في الحقل المخصص
- [ ] لا توجد رسائل خطأ في السجل

## 🆘 طلب المساعدة / Getting Help

### إذا استمرت المشاكل:

#### 1. جمع معلومات التشخيص:
```bash
# إصدار Python
python --version

# المكتبات المثبتة
pip list | grep -E "(supabase|google|pyperclip|requests|beautifulsoup4|pillow)"

# رسائل الخطأ الكاملة
python mod_processor.py > output.log 2>&1
```

#### 2. التحقق من السجلات:
- راقب رسائل الحالة في أسفل الأداة
- ابحث عن رسائل الخطأ باللون الأحمر
- احفظ رسائل الخطأ الكاملة

#### 3. خطوات إعادة التثبيت:
```bash
# إزالة المكتبات القديمة
pip uninstall supabase google-generativeai pyperclip cloudscraper

# إعادة التثبيت
pip install supabase google-generativeai pyperclip cloudscraper requests beautifulsoup4 pillow

# إعادة تشغيل الأداة
python mod_processor.py
```

## ✅ الحلول المطبقة / Applied Solutions

### تم إصلاح المشاكل التالية:

#### 1. ✅ مشكلة Supabase `proxy`:
- تم إزالة معامل `proxy` من إنشاء العميل
- تم تحديث كود إنشاء عميل Storage
- تم تحديث كود إنشاء عميل Database

#### 2. ✅ إضافة ميزة الوصف العربي:
- تم إضافة واجهة مستخدم كاملة
- تم إضافة دوال الذكاء الاصطناعي
- تم تحديث منطق النشر
- تم إضافة دعم قاعدة البيانات

#### 3. ✅ تحسين معالجة الأخطاء:
- تم إضافة رسائل خطأ واضحة
- تم إضافة إعادة المحاولة للذكاء الاصطناعي
- تم تحسين رسائل الحالة

### المطلوب من المستخدم:

#### 1. 🔧 تحديث قاعدة البيانات:
```sql
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;
```

#### 2. 📦 تثبيت المكتبات:
```bash
pip install google-generativeai pyperclip cloudscraper
```

#### 3. 🚀 تشغيل الأداة:
```bash
cd "C:\Users\<USER>\modetaris\app\src\main\assets\send addons"
python mod_processor.py
```

---

**الآن الأداة جاهزة للاستخدام مع ميزة إنشاء الوصف العربي! 🎉**
