-- إضافة دعم ثنائي اللغة للمربعات المخصصة
-- Add bilingual support for custom dialogs

-- إضافة الحقول الإنجليزية لجدول custom_mod_dialogs
ALTER TABLE custom_mod_dialogs 
ADD COLUMN IF NOT EXISTS title_en VARCHAR(255),
ADD COLUMN IF NOT EXISTS description_en TEXT,
ADD COLUMN IF NOT EXISTS button_text_en VARCHAR(100) DEFAULT 'OK';

-- تحديث الحقول الموجودة لتكون واضحة أنها عربية
COMMENT ON COLUMN custom_mod_dialogs.title IS 'Arabic title';
COMMENT ON COLUMN custom_mod_dialogs.description IS 'Arabic description';
COMMENT ON COLUMN custom_mod_dialogs.button_text IS 'Arabic button text';
COMMENT ON COLUMN custom_mod_dialogs.title_en IS 'English title';
COMMENT ON COLUMN custom_mod_dialogs.description_en IS 'English description';
COMMENT ON COLUMN custom_mod_dialogs.button_text_en IS 'English button text';

-- دالة للحصول على محتوى المربع حسب لغة المستخدم
CREATE OR REPLACE FUNCTION get_dialog_content_for_user(
    p_dialog_id INTEGER,
    p_user_id VARCHAR(255)
)
RETURNS TABLE (
    id INTEGER,
    title TEXT,
    description TEXT,
    image_url TEXT,
    button_text TEXT,
    show_dont_show_again BOOLEAN,
    is_active BOOLEAN
) AS $$
DECLARE
    user_lang VARCHAR(10);
BEGIN
    -- الحصول على لغة المستخدم
    user_lang := get_user_language(p_user_id);
    
    RETURN QUERY
    SELECT 
        cmd.id,
        CASE 
            WHEN user_lang = 'ar' THEN COALESCE(cmd.title, cmd.title_en, 'No title')
            ELSE COALESCE(cmd.title_en, cmd.title, 'No title')
        END as title,
        CASE 
            WHEN user_lang = 'ar' THEN COALESCE(cmd.description, cmd.description_en)
            ELSE COALESCE(cmd.description_en, cmd.description)
        END as description,
        cmd.image_url,
        CASE 
            WHEN user_lang = 'ar' THEN COALESCE(cmd.button_text, cmd.button_text_en, 'تم')
            ELSE COALESCE(cmd.button_text_en, cmd.button_text, 'OK')
        END as button_text,
        cmd.show_dont_show_again,
        cmd.is_active
    FROM custom_mod_dialogs cmd
    WHERE cmd.id = p_dialog_id AND cmd.is_active = true;
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على جميع المربعات المرتبطة بمود معين حسب لغة المستخدم
CREATE OR REPLACE FUNCTION get_mod_dialogs_for_user(
    p_mod_id UUID,
    p_user_id VARCHAR(255)
)
RETURNS TABLE (
    dialog_id INTEGER,
    title TEXT,
    description TEXT,
    image_url TEXT,
    button_text TEXT,
    show_dont_show_again BOOLEAN
) AS $$
DECLARE
    user_lang VARCHAR(10);
BEGIN
    -- الحصول على لغة المستخدم
    user_lang := get_user_language(p_user_id);
    
    RETURN QUERY
    SELECT 
        cmd.id,
        CASE 
            WHEN user_lang = 'ar' THEN COALESCE(cmd.title, cmd.title_en, 'No title')
            ELSE COALESCE(cmd.title_en, cmd.title, 'No title')
        END as title,
        CASE 
            WHEN user_lang = 'ar' THEN COALESCE(cmd.description, cmd.description_en)
            ELSE COALESCE(cmd.description_en, cmd.description)
        END as description,
        cmd.image_url,
        CASE 
            WHEN user_lang = 'ar' THEN COALESCE(cmd.button_text, cmd.button_text_en, 'تم')
            ELSE COALESCE(cmd.button_text_en, cmd.button_text, 'OK')
        END as button_text,
        cmd.show_dont_show_again
    FROM custom_mod_dialogs cmd
    INNER JOIN custom_dialog_mods cdm ON cmd.id = cdm.dialog_id
    WHERE cdm.mod_id = p_mod_id 
    AND cmd.is_active = true
    ORDER BY cmd.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- إنشاء فهارس للحقول الجديدة
CREATE INDEX IF NOT EXISTS idx_custom_mod_dialogs_title_en ON custom_mod_dialogs USING gin(to_tsvector('english', title_en));
CREATE INDEX IF NOT EXISTS idx_custom_mod_dialogs_description_en ON custom_mod_dialogs USING gin(to_tsvector('english', description_en));
