# ميزة إنشاء الوصف العربي - Arabic Description Generation Feature

## نظرة عامة / Overview

تم إضافة ميزة جديدة لأداة نشر مودات ماين كرافت تسمح بإنشاء وصوفات عربية تلقائياً باستخدام الذكاء الاصطناعي Gemini AI.

A new feature has been added to the Minecraft mod publishing tool that allows automatic generation of Arabic descriptions using Gemini AI.

## المميزات الجديدة / New Features

### 🤖 إنشاء الوصف العربي بالذكاء الاصطناعي
- **زر "إنشاء وصف عربي"** في قسم الذكاء الاصطناعي
- **استخدام Gemini AI** لإنشاء وصوفات عربية طبيعية وجذابة
- **دعم السياق** من النص المستخرج والميزات المدخلة يدوياً
- **تنسيق احترافي** مع تعليمات مفصلة للذكاء الاصطناعي

### 📝 حقل الوصف العربي
- **حقل نص منفصل** للوصف العربي في قسم النشر
- **أزرار التحكم** (لصق، نسخ، مسح) لسهولة الاستخدام
- **تكامل مع النشر** - يتم حفظ الوصف العربي في قاعدة البيانات
- **تفريغ تلقائي** عند تفريغ الحقول

### 🔄 أزرار إضافية
- **زر "نسخ الوصف العربي"** في قسم الذكاء الاصطناعي
- **تحديث تلقائي** للحقل عند إنشاء الوصف
- **رسائل حالة** باللغة العربية

## التنفيذ التقني / Technical Implementation

### 1. واجهة المستخدم / User Interface

#### إضافة الأزرار في قسم الذكاء الاصطناعي:
```python
# زر إنشاء الوصف العربي
generate_arabic_desc_button = ttk.Button(
    ai_buttons_frame, 
    text="إنشاء وصف عربي", 
    command=handle_generate_arabic_description
)

# زر نسخ الوصف العربي المنشأ
copy_generated_arabic_desc_button = ttk.Button(
    ai_buttons_frame,
    text="نسخ الوصف العربي",
    command=lambda: copy_from_widget(publish_arabic_desc_text)
)
```

#### إضافة حقل الوصف العربي في قسم النشر:
```python
# Row 2: Arabic Description
ttk.Label(publish_frame, text="الوصف العربي:").grid(row=2, column=0)
publish_arabic_desc_text = scrolledtext.ScrolledText(
    publish_frame, 
    height=8, 
    width=45, 
    font=default_font
)
publish_arabic_desc_text.grid(row=2, column=1, columnspan=col_count-2)

# أزرار التحكم (لصق، نسخ، مسح)
arabic_desc_buttons_frame = ttk.Frame(publish_frame)
paste_arabic_desc_button = ttk.Button(arabic_desc_buttons_frame, text="لصق")
copy_arabic_desc_button = ttk.Button(arabic_desc_buttons_frame, text="نسخ")
clear_arabic_desc_button = ttk.Button(arabic_desc_buttons_frame, text="مسح")
```

### 2. منطق الذكاء الاصطناعي / AI Logic

#### دالة معالجة إنشاء الوصف العربي:
```python
def handle_generate_arabic_description():
    """Handles the button click for generating Arabic AI description."""
    global last_scraped_data, gemini_extraction_cache
    
    # التحقق من توفر Gemini
    if not GEMINI_CLIENT_OK:
        messagebox.showerror("Gemini Error", "Gemini is not configured correctly")
        return
    
    # جمع البيانات من الواجهة
    manual_features_text = mod_features_text.get("1.0", tk.END).strip()
    mod_name = publish_name_entry.get().strip()
    mod_category = category_combobox.get()
    
    # استخدام النص المستخرج كسياق إضافي
    scraped_context = None
    if last_scraped_data and last_scraped_data.get("full_scraped_text"):
        scraped_context = last_scraped_data["full_scraped_text"]
    
    # تشغيل المهمة في خيط منفصل
    run_in_thread(generate_arabic_description_task, mod_name, mod_category, scraped_context, manual_features_text)
```

#### دالة إنشاء الوصف العربي:
```python
def generate_arabic_description_task(mod_name, mod_category, scraped_text, manual_features):
    """Calls Gemini API to generate Arabic description using available context."""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK
    
    # تحديد السياق الأساسي
    primary_context_source = ""
    if manual_features:
        primary_context_source = f"الميزات المقدمة من المستخدم:\n{manual_features}\n"
        if scraped_text:
            primary_context_source += f"\nوصف إضافي مستخرج:\n{scraped_text[:2000]}\n"
    elif scraped_text:
        primary_context_source = f"الوصف الكامل المستخرج:\n{scraped_text[:2500]}\n"
    
    # إنشاء الطلب للذكاء الاصطناعي
    prompt = f"""
    أنت كاتب محتوى ماين كرافت خبير باللغة العربية. مهمتك هي كتابة وصف جذاب وفريد ومفصل 
    (حوالي 100-200 كلمة) لمحتوى ماين كرافت باللغة العربية.

    **اسم المحتوى:** {mod_name if mod_name else "غير محدد"}
    **نوع المحتوى:** {mod_category}

    **معلومات المصدر:**
    {primary_context_source}
    
    **التعليمات:**
    - استخدم المعلومات المقدمة كأساس رئيسي لكتابتك
    - أنشئ وصفاً جديداً وفريداً وجذاباً
    - اذكر الميزات الرئيسية للمود صراحة
    - سلط الضوء على قيمة المحتوى للاعبين
    - استخدم اللغة العربية الطبيعية والسلسة
    - تجنب التكرار والنسخ المباشر
    
    قدم إجابتك بهذا التنسيق:
    [ARABIC_DESCRIPTION]
    وصفك الجذاب باللغة العربية هنا...
    [/ARABIC_DESCRIPTION]
    """
    
    # إرسال الطلب إلى Gemini مع معالجة الأخطاء
    retries = 0
    success = False
    generated_arabic_desc = ""
    
    while retries < MAX_GEMINI_RETRIES and not success:
        try:
            response = gemini_model.generate_content(prompt)
            full_response = response.text.strip()
            
            # استخراج الوصف من الرد
            desc_match = re.search(r'\[ARABIC_DESCRIPTION\](.*?)\[/ARABIC_DESCRIPTION\]', full_response, re.DOTALL)
            if desc_match:
                generated_arabic_desc = desc_match.group(1).strip()
            else:
                generated_arabic_desc = full_response
            
            success = True
            
        except Exception as e:
            # معالجة أخطاء حد المعدل والأخطاء الأخرى
            is_rate_limit = "rate limit" in str(e).lower() or "quota" in str(e).lower()
            if is_rate_limit:
                retries += 1
                configure_gemini_client(current_gemini_key_index + 1)
            else:
                break
    
    # تحديث الواجهة بالوصف المُنشأ
    if success:
        window.after(0, auto_populate_text_widget, publish_arabic_desc_text, generated_arabic_desc)
        update_status("--> تم تحديث حقل الوصف العربي بالوصف المُنشأ بواسطة AI.")
    
    # إعادة تفعيل الزر
    window.after(0, lambda: generate_arabic_desc_button.config(state=tk.NORMAL))
```

### 3. تكامل النشر / Publishing Integration

#### تحديث دالة النشر لتشمل الوصف العربي:
```python
def handle_publish_mod():
    # جمع البيانات من الحقول
    mod_name = publish_name_entry.get().strip()
    mod_desc = publish_desc_text.get("1.0", tk.END).strip()
    mod_desc_ar = publish_arabic_desc_text.get("1.0", tk.END).strip()  # الوصف العربي
    
    # إضافة الوصف العربي إلى بيانات النشر
    publish_data = {
        "name": mod_name,
        "description": mod_desc if mod_desc else None,
        "description_ar": mod_desc_ar if mod_desc_ar else None,  # الحقل الجديد
        "category": mod_category,
        # ... باقي الحقول
    }
    
    # نشر البيانات
    run_in_thread(publish_mod_task, publish_data, clear_after_publish)
```

#### تحديث دالة تفريغ الحقول:
```python
def clear_all_app_fields():
    text_widgets_to_clear = [
        mod_features_text,
        publish_desc_text,
        publish_arabic_desc_text,  # إضافة الوصف العربي
        publish_other_images_text,
        mod_result_text,
    ]
    
    for widget in text_widgets_to_clear:
        if widget: clear_widget_content(widget)
```

## الاستخدام / Usage

### للمستخدمين / For Users

#### 1. إنشاء وصف عربي:
1. **أدخل ميزات المود** في قسم "ميزات المود" أو **استخرج مقال** أولاً
2. **انقر على "إنشاء وصف عربي"** في قسم الذكاء الاصطناعي
3. **انتظر** حتى يتم إنشاء الوصف (قد يستغرق بضع ثوانٍ)
4. **راجع الوصف** في حقل "الوصف العربي"
5. **عدّل حسب الحاجة** أو **انسخ** لاستخدامه في مكان آخر

#### 2. نشر المود مع الوصف العربي:
1. **املأ جميع الحقول المطلوبة** (الاسم، الفئة، الصور، إلخ)
2. **تأكد من وجود وصف عربي** في الحقل المخصص
3. **انقر على "نشر المود الآن"**
4. **سيتم حفظ الوصف العربي** في قاعدة البيانات تلقائياً

### للمطورين / For Developers

#### إضافة دعم الوصف العربي في التطبيق:
```javascript
// في التطبيق، استخدم الوصف العربي حسب لغة المستخدم
function getLocalizedDescription(mod) {
    const userLanguage = localStorage.getItem('selectedLanguage') || 'en';
    
    if (userLanguage === 'ar' && mod.description_ar) {
        return mod.description_ar;
    }
    
    return mod.description || 'No description available.';
}
```

## المتطلبات / Requirements

### 1. قاعدة البيانات / Database
- **عمود `description_ar`** في جدول `mods`
- **نوع البيانات**: TEXT
- **قابل للقيم الفارغة**: نعم

```sql
ALTER TABLE mods ADD COLUMN description_ar TEXT;
```

### 2. Gemini AI
- **مفاتيح API صالحة** في ملف التكوين
- **اتصال إنترنت** لاستدعاء API
- **حصة كافية** لطلبات الذكاء الاصطناعي

### 3. واجهة المستخدم
- **Python tkinter** مع scrolledtext
- **خطوط تدعم العربية** للعرض الصحيح

## الفوائد / Benefits

### 🌍 دعم متعدد اللغات
- **توسيع الجمهور** للمحتوى العربي
- **تحسين تجربة المستخدم** للمتحدثين بالعربية
- **زيادة الوصول** للمحتوى في المنطقة العربية

### ⚡ توفير الوقت
- **إنشاء تلقائي** للوصوفات العربية
- **جودة عالية** مع الذكاء الاصطناعي
- **تقليل الجهد اليدوي** في الترجمة

### 📈 تحسين الجودة
- **وصوفات طبيعية** وليست مترجمة آلياً
- **سياق مناسب** للمحتوى
- **لغة جذابة** ومناسبة للجمهور

## استكشاف الأخطاء / Troubleshooting

### مشكلة: الزر لا يعمل
**الأسباب المحتملة:**
- Gemini غير مكوّن بشكل صحيح
- لا توجد ميزات مدخلة أو نص مستخرج
- مشكلة في الاتصال بالإنترنت

**الحلول:**
```python
# التحقق من حالة Gemini
if not GEMINI_CLIENT_OK:
    print("Gemini client not configured")

# التحقق من وجود محتوى
manual_features = mod_features_text.get("1.0", tk.END).strip()
if not manual_features:
    print("No features entered")
```

### مشكلة: الوصف لا يظهر
**الأسباب المحتملة:**
- خطأ في تحليل رد Gemini
- مشكلة في تحديث الواجهة
- خطأ في التنسيق

**الحلول:**
```python
# التحقق من رد Gemini
print(f"Gemini response: {full_response}")

# التحقق من استخراج الوصف
if desc_match:
    print(f"Extracted description: {desc_match.group(1)}")
else:
    print("Could not extract description from response")
```

### مشكلة: خطأ في النشر
**الأسباب المحتملة:**
- عمود `description_ar` غير موجود في قاعدة البيانات
- مشكلة في الاتصال بـ Supabase
- خطأ في تنسيق البيانات

**الحلول:**
```sql
-- إضافة العمود إذا لم يكن موجوداً
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;

-- التحقق من وجود العمود
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'mods' AND column_name = 'description_ar';
```

## الخلاصة / Summary

تم إضافة ميزة إنشاء الوصف العربي بنجاح إلى أداة نشر مودات ماين كرافت مع:

- ✅ **واجهة مستخدم كاملة** مع أزرار وحقول مخصصة
- ✅ **تكامل مع Gemini AI** لإنشاء وصوفات عالية الجودة
- ✅ **دعم السياق** من النصوص المستخرجة والميزات المدخلة
- ✅ **تكامل مع النشر** وحفظ البيانات في قاعدة البيانات
- ✅ **معالجة شاملة للأخطاء** مع إعادة المحاولة
- ✅ **تفريغ تلقائي** للحقول عند الحاجة

الميزة جاهزة للاستخدام وستساعد في توسيع نطاق المحتوى ليشمل الجمهور العربي! 🎉
