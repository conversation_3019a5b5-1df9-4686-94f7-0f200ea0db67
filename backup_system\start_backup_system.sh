#!/bin/bash

# نظام النسخ الاحتياطي الاحترافي
# Professional Backup System

clear

echo "==============================================================================="
echo "🚀 نظام النسخ الاحتياطي الاحترافي للمودات"
echo "   Professional Backup System for Mods Database"
echo "==============================================================================="
echo

cd "$(dirname "$0")"

show_menu() {
    echo "📋 اختر العملية المطلوبة:"
    echo
    echo "1. إنشاء نسخة احتياطية سريعة (Quick Backup)"
    echo "2. نقل البيانات بين قواعد البيانات (Transfer Data)"
    echo "3. تبديل قاعدة البيانات النشطة (Switch Database)"
    echo "4. مقارنة قواعد البيانات (Compare Databases)"
    echo "5. عرض حالة النظام (System Status)"
    echo "6. بدء المراقبة التلقائية (Start Monitoring)"
    echo "7. بدء النسخ الاحتياطي التلقائي (Auto Backup)"
    echo "8. إعداد النظام (Setup System)"
    echo "9. خروج (Exit)"
    echo
}

backup_operation() {
    echo
    echo "💾 إنشاء نسخة احتياطية..."
    python3 database_backup_manager.py backup
    echo
    read -p "اضغط Enter للمتابعة..."
}

transfer_operation() {
    echo
    echo "📊 نقل البيانات بين قواعد البيانات"
    echo
    echo "قواعد البيانات المتاحة:"
    echo "- main (الرئيسية)"
    echo "- backup1 (الاحتياطية 1)"
    echo "- backup2 (الاحتياطية 2)"
    echo
    read -p "قاعدة البيانات المصدر: " source
    read -p "قاعدة البيانات المستهدفة: " target
    read -p "تفعيل قاعدة البيانات الجديدة؟ (y/n): " activate
    
    if [[ "$activate" == "y" || "$activate" == "Y" ]]; then
        python3 database_backup_manager.py transfer "$source" "$target" --activate
    else
        python3 database_backup_manager.py transfer "$source" "$target"
    fi
    echo
    read -p "اضغط Enter للمتابعة..."
}

switch_operation() {
    echo
    echo "🔄 تبديل قاعدة البيانات النشطة"
    echo
    echo "قواعد البيانات المتاحة:"
    echo "- main (الرئيسية)"
    echo "- backup1 (الاحتياطية 1)"
    echo "- backup2 (الاحتياطية 2)"
    echo
    read -p "قاعدة البيانات المستهدفة: " target
    read -p "إنشاء نسخة احتياطية قبل التبديل؟ (y/n): " backup
    
    if [[ "$backup" == "n" || "$backup" == "N" ]]; then
        python3 database_backup_manager.py switch "$target" --no-backup
    else
        python3 database_backup_manager.py switch "$target"
    fi
    echo
    read -p "اضغط Enter للمتابعة..."
}

compare_operation() {
    echo
    echo "📊 مقارنة قواعد البيانات"
    echo
    read -p "قاعدة البيانات الأولى: " db1
    read -p "قاعدة البيانات الثانية: " db2
    python3 database_backup_manager.py compare "$db1" "$db2"
    echo
    read -p "اضغط Enter للمتابعة..."
}

status_operation() {
    echo
    echo "📈 حالة النظام"
    python3 database_backup_manager.py status
    echo
    read -p "اضغط Enter للمتابعة..."
}

monitor_operation() {
    echo
    echo "🔍 بدء المراقبة التلقائية..."
    echo "اضغط Ctrl+C لإيقاف المراقبة"
    python3 database_monitor.py start
    echo
    read -p "اضغط Enter للمتابعة..."
}

auto_backup_operation() {
    echo
    echo "🕐 بدء النسخ الاحتياطي التلقائي..."
    echo "اضغط Ctrl+C لإيقاف النسخ التلقائي"
    python3 database_backup_manager.py auto
    echo
    read -p "اضغط Enter للمتابعة..."
}

setup_operation() {
    echo
    echo "⚙️ إعداد النظام..."
    python3 quick_start.py
    echo
    read -p "اضغط Enter للمتابعة..."
}

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير مثبت"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

# التحقق من وجود الملفات المطلوبة
if [[ ! -f "database_backup_manager.py" ]]; then
    echo "❌ ملف database_backup_manager.py غير موجود"
    exit 1
fi

# الحلقة الرئيسية
while true; do
    clear
    echo "==============================================================================="
    echo "🚀 نظام النسخ الاحتياطي الاحترافي"
    echo "==============================================================================="
    echo
    
    show_menu
    
    read -p "أدخل رقم الخيار (1-9): " choice
    
    case $choice in
        1)
            backup_operation
            ;;
        2)
            transfer_operation
            ;;
        3)
            switch_operation
            ;;
        4)
            compare_operation
            ;;
        5)
            status_operation
            ;;
        6)
            monitor_operation
            ;;
        7)
            auto_backup_operation
            ;;
        8)
            setup_operation
            ;;
        9)
            echo
            echo "👋 شكراً لاستخدام نظام النسخ الاحتياطي الاحترافي"
            echo "   Thank you for using Professional Backup System"
            echo
            exit 0
            ;;
        *)
            echo "❌ خيار غير صحيح، حاول مرة أخرى"
            read -p "اضغط Enter للمتابعة..."
            ;;
    esac
done
