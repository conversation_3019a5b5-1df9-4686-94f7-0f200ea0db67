// إدارة واجهة النسخ الاحتياطي لقاعدة البيانات
// Database Backup Manager UI

class DatabaseBackupManagerUI {
    constructor() {
        this.backupSystem = null;
        this.refreshInterval = null;
        this.selectedBackupId = null;
        
        this.init();
    }
    
    async init() {
        try {
            console.log('🔄 تهيئة واجهة إدارة النسخ الاحتياطي...');
            
            // انتظار تحميل نظام النسخ الاحتياطي
            await this.waitForBackupSystem();
            
            // تحديث الواجهة الأولي
            await this.updateUI();
            
            // بدء التحديث الدوري
            this.startPeriodicRefresh();
            
            // إضافة مستمعي الأحداث
            this.setupEventListeners();
            
            console.log('✅ تم تهيئة واجهة إدارة النسخ الاحتياطي');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة واجهة إدارة النسخ الاحتياطي:', error);
            this.showAlert('خطأ في تهيئة النظام', 'danger');
        }
    }
    
    async waitForBackupSystem() {
        return new Promise((resolve, reject) => {
            const checkSystem = () => {
                if (window.databaseBackupSystem) {
                    this.backupSystem = window.databaseBackupSystem;
                    resolve();
                } else {
                    setTimeout(checkSystem, 100);
                }
            };
            
            checkSystem();
            
            // timeout بعد 10 ثوان
            setTimeout(() => {
                if (!this.backupSystem) {
                    reject(new Error('نظام النسخ الاحتياطي غير متاح'));
                }
            }, 10000);
        });
    }
    
    async updateUI() {
        try {
            // تحديث حالة قواعد البيانات
            await this.updateDatabaseStatus();
            
            // تحديث معلومات قاعدة البيانات الحالية
            this.updateCurrentDatabaseInfo();
            
            // تحديث سجل النسخ الاحتياطية
            await this.updateBackupHistory();
            
            // تحديث حالة مزامنة الجداول
            await this.updateTableSyncStatus();
            
        } catch (error) {
            console.error('❌ خطأ في تحديث الواجهة:', error);
        }
    }
    
    async updateDatabaseStatus() {
        try {
            const health = await this.backupSystem.checkDatabaseHealth();
            
            // تحديث حالة قاعدة البيانات الأساسية
            const primaryCard = document.getElementById('primaryStatus');
            const primaryIcon = document.getElementById('primaryIcon');
            const primaryText = document.getElementById('primaryStatusText');
            const switchToPrimary = document.getElementById('switchToPrimary');
            
            if (health.primary) {
                primaryCard.className = 'status-card active';
                primaryIcon.className = 'status-icon active';
                primaryText.textContent = 'متصلة وتعمل بشكل طبيعي';
                switchToPrimary.disabled = this.backupSystem.currentDatabase === 'primary';
            } else {
                primaryCard.className = 'status-card inactive';
                primaryIcon.className = 'status-icon inactive';
                primaryText.textContent = 'غير متاحة أو بها مشاكل';
                switchToPrimary.disabled = true;
            }
            
            // تحديث حالة قاعدة البيانات الاحتياطية
            const backupCard = document.getElementById('backupStatus');
            const backupIcon = document.getElementById('backupIcon');
            const backupText = document.getElementById('backupStatusText');
            const switchToBackup = document.getElementById('switchToBackup');
            
            if (health.backup) {
                backupCard.className = 'status-card active';
                backupIcon.className = 'status-icon active';
                backupText.textContent = 'متصلة وتعمل بشكل طبيعي';
                switchToBackup.disabled = this.backupSystem.currentDatabase === 'backup';
            } else {
                backupCard.className = 'status-card inactive';
                backupIcon.className = 'status-icon inactive';
                backupText.textContent = 'غير متاحة أو بها مشاكل';
                switchToBackup.disabled = true;
            }
            
            // عرض تحكم الطوارئ إذا كانت الأساسية معطلة والاحتياطية تعمل
            const emergencyControls = document.getElementById('emergencyControls');
            if (!health.primary && health.backup && this.backupSystem.currentDatabase === 'primary') {
                emergencyControls.style.display = 'block';
            } else {
                emergencyControls.style.display = 'none';
            }
            
        } catch (error) {
            console.error('❌ خطأ في تحديث حالة قواعد البيانات:', error);
        }
    }
    
    updateCurrentDatabaseInfo() {
        try {
            const info = this.backupSystem.getCurrentDatabaseInfo();
            
            // تحديث اسم قاعدة البيانات النشطة
            const currentDatabaseName = document.getElementById('currentDatabaseName');
            const syncIndicator = document.getElementById('syncIndicator');
            
            if (info.current === 'primary') {
                currentDatabaseName.textContent = 'قاعدة البيانات الأساسية (Supabase)';
                syncIndicator.className = 'sync-indicator active';
            } else {
                currentDatabaseName.textContent = 'قاعدة البيانات الاحتياطية (Firebase)';
                syncIndicator.className = 'sync-indicator';
            }
            
            // تحديث وقت آخر نسخ احتياطي
            const lastBackupTime = document.getElementById('lastBackupTime');
            if (info.lastSync) {
                const date = new Date(info.lastSync);
                lastBackupTime.textContent = date.toLocaleString('ar-SA');
            } else {
                lastBackupTime.textContent = 'لم يتم إنشاء نسخ احتياطي بعد';
            }
            
        } catch (error) {
            console.error('❌ خطأ في تحديث معلومات قاعدة البيانات الحالية:', error);
        }
    }
    
    async updateBackupHistory() {
        try {
            const backupList = await this.backupSystem.getBackupList();
            const container = document.getElementById('backupHistory');
            
            if (backupList.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox text-muted" style="font-size: 3em;"></i>
                        <h4 class="mt-3 text-muted">لا توجد نسخ احتياطية</h4>
                        <p class="text-muted">قم بإنشاء أول نسخة احتياطية للبدء</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            
            backupList.forEach(backup => {
                const statusClass = this.getBackupStatusClass(backup.status);
                const statusIcon = this.getBackupStatusIcon(backup.status);
                const date = new Date(backup.timestamp).toLocaleString('ar-SA');
                
                html += `
                    <div class="backup-card ${statusClass}">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5>
                                    <i class="${statusIcon}"></i>
                                    نسخة احتياطية ${backup.type === 'full' ? 'كاملة' : 'تزايدية'}
                                </h5>
                                <p class="mb-1">
                                    <strong>التاريخ:</strong> ${date}<br>
                                    <strong>الحالة:</strong> ${this.getBackupStatusText(backup.status)}<br>
                                    <strong>عدد السجلات:</strong> ${backup.totalRecords || 0}
                                </p>
                                ${backup.errors && backup.errors.length > 0 ? 
                                    `<small class="text-danger">أخطاء: ${backup.errors.length}</small>` : ''
                                }
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group-vertical">
                                    <button class="btn btn-sm btn-primary" onclick="showRestoreModal('${backup.id}')"
                                            ${backup.status !== 'completed' ? 'disabled' : ''}>
                                        <i class="fas fa-undo"></i> استعادة
                                    </button>
                                    <button class="btn btn-sm btn-info" onclick="viewBackupDetails('${backup.id}')">
                                        <i class="fas fa-eye"></i> تفاصيل
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteBackup('${backup.id}')">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
        } catch (error) {
            console.error('❌ خطأ في تحديث سجل النسخ الاحتياطية:', error);
            document.getElementById('backupHistory').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    خطأ في تحميل سجل النسخ الاحتياطية
                </div>
            `;
        }
    }
    
    async updateTableSyncStatus() {
        try {
            const container = document.getElementById('tableStatus');
            const tables = this.backupSystem.tablesToBackup;
            
            let html = '';
            
            for (const tableName of tables) {
                const lastSync = await this.backupSystem.getLastSyncDate(tableName);
                const syncDate = lastSync ? new Date(lastSync).toLocaleString('ar-SA') : 'لم يتم النسخ';
                const syncClass = lastSync ? 'text-success' : 'text-warning';
                
                html += `
                    <div class="table-card">
                        <h6><i class="fas fa-table"></i> ${tableName}</h6>
                        <p class="mb-0 ${syncClass}">
                            <small>آخر نسخ: ${syncDate}</small>
                        </p>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            
        } catch (error) {
            console.error('❌ خطأ في تحديث حالة مزامنة الجداول:', error);
        }
    }
    
    getBackupStatusClass(status) {
        const statusMap = {
            'completed': 'completed',
            'completed_with_errors': 'completed',
            'in_progress': 'in-progress',
            'failed': 'failed'
        };
        return statusMap[status] || 'failed';
    }
    
    getBackupStatusIcon(status) {
        const iconMap = {
            'completed': 'fas fa-check-circle text-success',
            'completed_with_errors': 'fas fa-exclamation-triangle text-warning',
            'in_progress': 'fas fa-spinner fa-spin text-primary',
            'failed': 'fas fa-times-circle text-danger'
        };
        return iconMap[status] || 'fas fa-question-circle text-muted';
    }
    
    getBackupStatusText(status) {
        const textMap = {
            'completed': 'مكتملة',
            'completed_with_errors': 'مكتملة مع أخطاء',
            'in_progress': 'قيد التنفيذ',
            'failed': 'فاشلة'
        };
        return textMap[status] || 'غير معروف';
    }
    
    setupEventListeners() {
        // مستمع تقدم النسخ الاحتياطي
        window.addEventListener('backupProgress', (event) => {
            this.updateProgressUI(event.detail.progress, event.detail.message);
        });
        
        // تحديث دوري للواجهة
        this.refreshInterval = setInterval(() => {
            this.updateUI();
        }, 30000); // كل 30 ثانية
    }
    
    updateProgressUI(progress, message) {
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('backupProgress');
        const progressText = document.getElementById('backupProgressText');
        
        if (progress > 0) {
            progressContainer.classList.add('active');
        }
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }
        
        if (progressText) {
            progressText.textContent = message;
        }
        
        if (progress >= 100) {
            setTimeout(() => {
                progressContainer.classList.remove('active');
                this.updateUI(); // تحديث الواجهة بعد اكتمال العملية
            }, 2000);
        }
    }
    
    startPeriodicRefresh() {
        // تحديث كل 30 ثانية
        this.refreshInterval = setInterval(() => {
            if (!this.backupSystem.syncStatus.isRunning) {
                this.updateUI();
            }
        }, 30000);
    }
    
    showAlert(message, type = 'info', duration = 5000) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px; border-radius: 10px;';
        
        const iconMap = {
            'success': 'fas fa-check-circle',
            'warning': 'fas fa-exclamation-triangle',
            'danger': 'fas fa-times-circle',
            'info': 'fas fa-info-circle'
        };
        
        alertDiv.innerHTML = `
            <i class="${iconMap[type] || iconMap.info}" style="margin-left: 8px;"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, duration);
    }
    
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// الوظائف العامة للواجهة
async function createFullBackup() {
    try {
        backupManagerUI.showAlert('بدء إنشاء نسخة احتياطية كاملة...', 'info');
        
        const result = await window.databaseBackupSystem.createFullBackup();
        
        if (result.success) {
            backupManagerUI.showAlert(
                `تم إنشاء النسخة الاحتياطية بنجاح. تم نسخ ${result.totalRecords} سجل.`,
                'success'
            );
        } else {
            backupManagerUI.showAlert('فشل في إنشاء النسخة الاحتياطية', 'danger');
        }
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

async function createIncrementalBackup() {
    try {
        backupManagerUI.showAlert('بدء إنشاء نسخة احتياطية تزايدية...', 'info');
        
        const result = await window.databaseBackupSystem.createIncrementalBackup();
        
        if (result.success) {
            backupManagerUI.showAlert(
                `تم إنشاء النسخة الاحتياطية التزايدية بنجاح. تم نسخ ${result.totalRecords} سجل.`,
                'success'
            );
        } else {
            backupManagerUI.showAlert('فشل في إنشاء النسخة الاحتياطية التزايدية', 'danger');
        }
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء النسخة الاحتياطية التزايدية:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

async function switchToPrimaryDatabase() {
    try {
        if (confirm('هل أنت متأكد من التبديل إلى قاعدة البيانات الأساسية؟')) {
            await window.databaseBackupSystem.switchToPrimaryDatabase();
            backupManagerUI.showAlert('تم التبديل إلى قاعدة البيانات الأساسية', 'success');
            await backupManagerUI.updateUI();
        }
    } catch (error) {
        console.error('❌ خطأ في التبديل إلى قاعدة البيانات الأساسية:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

async function switchToBackupDatabase() {
    try {
        if (confirm('هل أنت متأكد من التبديل إلى قاعدة البيانات الاحتياطية؟')) {
            await window.databaseBackupSystem.switchToBackupDatabase();
            backupManagerUI.showAlert('تم التبديل إلى قاعدة البيانات الاحتياطية', 'warning');
            await backupManagerUI.updateUI();
        }
    } catch (error) {
        console.error('❌ خطأ في التبديل إلى قاعدة البيانات الاحتياطية:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

async function forceBackupSwitch() {
    try {
        await window.databaseBackupSystem.switchToBackupDatabase();
        backupManagerUI.showAlert('تم التبديل الإجباري إلى قاعدة البيانات الاحتياطية', 'warning');
        await backupManagerUI.updateUI();
    } catch (error) {
        console.error('❌ خطأ في التبديل الإجباري:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

async function testDatabaseConnections() {
    try {
        backupManagerUI.showAlert('جاري فحص اتصالات قواعد البيانات...', 'info');
        
        const health = await window.databaseBackupSystem.checkDatabaseHealth();
        
        let message = 'نتائج الفحص:\n';
        message += `قاعدة البيانات الأساسية: ${health.primary ? 'تعمل' : 'معطلة'}\n`;
        message += `قاعدة البيانات الاحتياطية: ${health.backup ? 'تعمل' : 'معطلة'}`;
        
        if (health.errors.length > 0) {
            message += `\nأخطاء: ${health.errors.join(', ')}`;
        }
        
        const alertType = (health.primary || health.backup) ? 'success' : 'danger';
        backupManagerUI.showAlert(message, alertType, 8000);
        
        await backupManagerUI.updateUI();
        
    } catch (error) {
        console.error('❌ خطأ في فحص الاتصالات:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

async function syncDatabases() {
    try {
        backupManagerUI.showAlert('بدء مزامنة قواعد البيانات...', 'info');
        
        const result = await window.databaseBackupSystem.syncDatabases();
        
        if (result.success) {
            backupManagerUI.showAlert('تم مزامنة قواعد البيانات بنجاح', 'success');
        } else {
            backupManagerUI.showAlert('فشل في مزامنة قواعد البيانات', 'danger');
        }
        
    } catch (error) {
        console.error('❌ خطأ في مزامنة قواعد البيانات:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

function showCleanupModal() {
    const modal = new bootstrap.Modal(document.getElementById('cleanupModal'));
    modal.show();
}

async function executeCleanup() {
    try {
        const days = parseInt(document.getElementById('cleanupDays').value);
        const keepSuccessfulOnly = document.getElementById('keepSuccessfulOnly').checked;
        
        backupManagerUI.showAlert('بدء تنظيف النسخ الاحتياطية القديمة...', 'info');
        
        const result = await window.databaseBackupSystem.cleanupOldBackups(days, keepSuccessfulOnly);
        
        backupManagerUI.showAlert(
            `تم حذف ${result.deletedCount} نسخة احتياطية قديمة`,
            'success'
        );
        
        // إغلاق النافذة المنبثقة
        const modal = bootstrap.Modal.getInstance(document.getElementById('cleanupModal'));
        modal.hide();
        
        // تحديث الواجهة
        await backupManagerUI.updateUI();
        
    } catch (error) {
        console.error('❌ خطأ في تنظيف النسخ الاحتياطية:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

function scheduleAutoBackup() {
    backupManagerUI.showAlert('ميزة الجدولة التلقائية قيد التطوير', 'info');
}

function cancelBackup() {
    backupManagerUI.showAlert('ميزة إلغاء النسخ الاحتياطي قيد التطوير', 'info');
}

function showRestoreModal(backupId) {
    backupManagerUI.selectedBackupId = backupId;
    const modal = new bootstrap.Modal(document.getElementById('restoreModal'));
    modal.show();
}

function viewBackupDetails(backupId) {
    backupManagerUI.showAlert(`عرض تفاصيل النسخة: ${backupId}`, 'info');
}

async function deleteBackup(backupId) {
    try {
        if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.')) {
            await window.databaseBackupSystem.deleteBackup(backupId);
            backupManagerUI.showAlert('تم حذف النسخة الاحتياطية بنجاح', 'success');
            await backupManagerUI.updateUI();
        }
    } catch (error) {
        console.error('❌ خطأ في حذف النسخة الاحتياطية:', error);
        backupManagerUI.showAlert(`خطأ: ${error.message}`, 'danger');
    }
}

function executeRestore() {
    if (backupManagerUI.selectedBackupId) {
        backupManagerUI.showAlert(`استعادة من النسخة: ${backupManagerUI.selectedBackupId}`, 'info');
    }
}

// تهيئة واجهة المستخدم عند تحميل الصفحة
let backupManagerUI;

document.addEventListener('DOMContentLoaded', function() {
    backupManagerUI = new DatabaseBackupManagerUI();
});

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', function() {
    if (backupManagerUI) {
        backupManagerUI.destroy();
    }
});
