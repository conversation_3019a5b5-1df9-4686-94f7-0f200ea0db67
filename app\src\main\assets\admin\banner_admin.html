<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البانر والاشتراك المجاني - Mod Etaris Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #ffd700;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .tab-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .tab-btn.active {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .tab-content.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #ffd700;
            font-weight: bold;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #aaa;
        }

        .image-upload-area {
            border: 2px dashed #ffd700;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .image-upload-area:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .image-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .campaign-fields {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .task-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #ffd700;
        }

        .task-controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .preview-content {
            background: var(--card-background, #000);
            border-radius: 15px;
            padding: 20px;
            max-width: 90%;
            max-height: 90%;
            overflow-y: auto;
            position: relative;
        }

        .close-preview {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #fca5a5;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .success-message {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid #22c55e;
            color: #86efac;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .image-requirements {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 إدارة البانر والاشتراك المجاني</h1>
            <p>إنشاء وإدارة البانرات وحملات الاشتراك المجاني</p>
        </div>

        <div class="tabs">
            <button class="tab-btn active" onclick="switchTab('banner')">إدارة البانر</button>
            <button class="tab-btn" onclick="switchTab('campaigns')">حملات الاشتراك</button>
            <button class="tab-btn" onclick="switchTab('tasks')">إدارة المهام</button>
            <button class="tab-btn" onclick="switchTab('analytics')">الإحصائيات</button>
        </div>

        <!-- تبويب إدارة البانر -->
        <div id="banner-tab" class="tab-content active">
            <div class="form-grid">
                <div class="form-section">
                    <h3>إعدادات البانر</h3>
                    
                    <div class="form-group">
                        <label for="bannerType">نوع البانر:</label>
                        <select id="bannerType" onchange="toggleCampaignFields()">
                            <option value="regular">بانر عادي</option>
                            <option value="subscription">بانر اشتراك مجاني</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="bannerTitle">عنوان البانر:</label>
                        <input type="text" id="bannerTitle" placeholder="أدخل عنوان البانر" required>
                    </div>

                    <div class="form-group">
                        <label for="bannerDescription">وصف البانر:</label>
                        <textarea id="bannerDescription" rows="3" placeholder="أدخل وصف البانر"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="bannerUrl">رابط البانر:</label>
                        <input type="url" id="bannerUrl" placeholder="https://example.com">
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="bannerActive"> البانر نشط
                        </label>
                    </div>
                </div>

                <div class="form-section">
                    <h3>صورة البانر</h3>
                    
                    <div class="image-requirements">
                        <strong>متطلبات الصورة:</strong><br>
                        • الحد الأقصى: 800x200 بكسل<br>
                        • الحد الأدنى: 300x100 بكسل<br>
                        • الصيغ المدعومة: JPG, PNG, WebP<br>
                        • الحد الأقصى للحجم: 2MB
                    </div>

                    <div class="image-upload-area" onclick="document.getElementById('bannerImage').click()">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #ffd700; margin-bottom: 10px;"></i>
                        <p>انقر لرفع صورة البانر</p>
                        <input type="file" id="bannerImage" accept="image/*" style="display: none;" onchange="handleImageUpload(this, 'banner')">
                    </div>
                    <img id="bannerPreview" class="image-preview" style="display: none;">
                </div>
            </div>

            <!-- حقول حملة الاشتراك المجاني -->
            <div id="campaignFields" class="campaign-fields">
                <h3>إعدادات حملة الاشتراك المجاني</h3>
                
                <div class="form-grid">
                    <div class="form-section">
                        <div class="form-group">
                            <label for="campaignTitleAr">عنوان الحملة (عربي):</label>
                            <input type="text" id="campaignTitleAr" placeholder="احصل على اشتراك مجاني">
                        </div>

                        <div class="form-group">
                            <label for="campaignTitleEn">عنوان الحملة (إنجليزي):</label>
                            <input type="text" id="campaignTitleEn" placeholder="Get Free Subscription">
                        </div>

                        <div class="form-group">
                            <label for="campaignDescAr">وصف الحملة (عربي):</label>
                            <textarea id="campaignDescAr" rows="3" placeholder="احصل على ميزة مجانية تمكنك من تحميل المودات بدون إعلانات"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="campaignDescEn">وصف الحملة (إنجليزي):</label>
                            <textarea id="campaignDescEn" rows="3" placeholder="Get a free feature that allows you to download mods without ads"></textarea>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="form-group">
                            <label for="subscriptionDuration">مدة الاشتراك (بالأيام):</label>
                            <input type="number" id="subscriptionDuration" value="30" min="1" max="365">
                        </div>

                        <div class="form-group">
                            <label for="maxUsers">الحد الأقصى للمستخدمين:</label>
                            <input type="number" id="maxUsers" placeholder="اتركه فارغاً للعدد غير المحدود">
                        </div>

                        <div class="form-group">
                            <label for="campaignEndDate">تاريخ انتهاء الحملة:</label>
                            <input type="datetime-local" id="campaignEndDate">
                        </div>

                        <h4>صورة النافذة المنبثقة</h4>
                        <div class="image-upload-area" onclick="document.getElementById('popupImage').click()">
                            <i class="fas fa-image" style="font-size: 32px; color: #ffd700; margin-bottom: 10px;"></i>
                            <p>صورة اختيارية للنافذة المنبثقة</p>
                            <input type="file" id="popupImage" accept="image/*" style="display: none;" onchange="handleImageUpload(this, 'popup')">
                        </div>
                        <img id="popupPreview" class="image-preview" style="display: none;">
                    </div>
                </div>
            </div>

            <div class="form-group" style="text-align: center; margin-top: 30px;">
                <button class="btn btn-secondary" onclick="previewBanner()">معاينة البانر</button>
                <button class="btn btn-primary" onclick="saveBanner()">حفظ البانر</button>
                <button class="btn btn-danger" onclick="clearForm()">مسح النموذج</button>
            </div>
        </div>

        <!-- باقي التبويبات سيتم إضافتها -->
        <div id="campaigns-tab" class="tab-content">
            <h3>إدارة حملات الاشتراك المجاني</h3>
            <p>قريباً...</p>
        </div>

        <div id="tasks-tab" class="tab-content">
            <h3>إدارة المهام</h3>
            <p>قريباً...</p>
        </div>

        <div id="analytics-tab" class="tab-content">
            <h3>الإحصائيات</h3>
            <p>قريباً...</p>
        </div>
    </div>

    <!-- نافذة المعاينة -->
    <div id="previewModal" class="preview-modal">
        <div class="preview-content">
            <button class="close-preview" onclick="closePreview()">&times;</button>
            <div id="previewContainer"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="banner_admin.js"></script>
</body>
</html>
