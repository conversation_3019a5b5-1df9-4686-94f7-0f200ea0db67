// نظام التحميل الاحتياطي الذكي
// Smart Download Fallback System

class DownloadFallbackSystem {
    constructor() {
        this.supabaseClient = null;
        this.firebaseConfig = {
            apiKey: "AIzaSyB3t2Ae-24DWUQJxwZ5LCFZVZov0ncaC8c",
            authDomain: "download-e33a2.firebaseapp.com",
            projectId: "download-e33a2",
            storageBucket: "download-e33a2.firebasestorage.app",
            messagingSenderId: "258780355379",
            appId: "1:258780355379:web:df601f640fb82d9c42bc46"
        };
        
        this.downloadAttempts = new Map(); // تتبع محاولات التحميل
        this.failedDownloads = new Map(); // تتبع التحميلات الفاشلة
        this.maxRetries = 3;
        
        this.init();
    }
    
    async init() {
        try {
            // انتظار supabaseManager
            if (window.supabaseManagerFix && window.supabaseManagerFix.waitForSupabaseManager) {
                try {
                    await window.supabaseManagerFix.waitForSupabaseManager(10000);
                    if (window.supabaseManager && window.supabaseManager.getMainClient) {
                        this.supabaseClient = window.supabaseManager.getMainClient();
                    }
                } catch (waitError) {
                    console.warn('⚠️ فشل في انتظار supabaseManager في نظام التحميل الاحتياطي:', waitError);
                }
            } else if (typeof window.supabaseManager !== 'undefined' && window.supabaseManager.getMainClient) {
                this.supabaseClient = window.supabaseManager.getMainClient();
            }

            // تهيئة Firebase
            if (typeof firebase !== 'undefined') {
                if (!firebase.apps.length) {
                    firebase.initializeApp(this.firebaseConfig);
                }
                this.storage = firebase.storage();
            }

            console.log('✅ تم تهيئة نظام التحميل الاحتياطي');

            // إنشاء جدول تتبع الأخطاء إذا لم يكن موجوداً
            await this.ensureErrorTrackingTable();

        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام التحميل الاحتياطي:', error);
        }
    }
    
    async ensureErrorTrackingTable() {
        if (!this.supabaseClient) return;
        
        try {
            // محاولة إنشاء جدول تتبع أخطاء التحميل
            const { data, error } = await this.supabaseClient
                .from('download_errors')
                .select('*')
                .limit(1);
            
            console.log('✅ جدول تتبع الأخطاء متاح');
        } catch (error) {
            console.log('⚠️ جدول تتبع الأخطاء غير موجود، سيتم إنشاؤه في قاعدة البيانات');
        }
    }
    
    async downloadMod(modId, modName, originalUrl, fallbackUrl = null) {
        console.log(`🔄 بدء تحميل المود: ${modName}`);
        
        const attemptKey = `${modId}_${Date.now()}`;
        this.downloadAttempts.set(attemptKey, {
            modId,
            modName,
            originalUrl,
            fallbackUrl,
            attempts: 0,
            startTime: Date.now()
        });
        
        try {
            // محاولة التحميل من الرابط الأصلي
            const success = await this.attemptDownload(originalUrl, modName, attemptKey);
            
            if (success) {
                console.log(`✅ تم تحميل المود بنجاح: ${modName}`);
                this.downloadAttempts.delete(attemptKey);
                return { success: true, source: 'original' };
            }
            
            // في حالة فشل التحميل الأصلي
            console.log(`⚠️ فشل التحميل الأصلي للمود: ${modName}`);
            
            // البحث عن رابط احتياطي
            const backupUrl = fallbackUrl || await this.getBackupDownloadUrl(modId);
            
            if (backupUrl) {
                console.log(`🔄 محاولة التحميل من الرابط الاحتياطي...`);
                const backupSuccess = await this.attemptDownload(backupUrl, modName, attemptKey, true);
                
                if (backupSuccess) {
                    console.log(`✅ تم تحميل المود من الرابط الاحتياطي: ${modName}`);
                    
                    // تسجيل الخطأ للمراجعة
                    await this.reportDownloadError(modId, modName, originalUrl, 'original_failed_backup_success');
                    
                    this.downloadAttempts.delete(attemptKey);
                    return { success: true, source: 'backup' };
                }
            }
            
            // فشل في جميع المحاولات
            await this.handleDownloadFailure(modId, modName, originalUrl, backupUrl);
            this.downloadAttempts.delete(attemptKey);
            
            return { success: false, error: 'جميع روابط التحميل فاشلة' };
            
        } catch (error) {
            console.error(`❌ خطأ في تحميل المود ${modName}:`, error);
            await this.handleDownloadFailure(modId, modName, originalUrl, fallbackUrl, error.message);
            this.downloadAttempts.delete(attemptKey);
            
            return { success: false, error: error.message };
        }
    }
    
    async attemptDownload(url, modName, attemptKey, isBackup = false) {
        const attempt = this.downloadAttempts.get(attemptKey);
        if (!attempt) return false;
        
        attempt.attempts++;
        
        try {
            // إنشاء رابط تحميل مؤقت
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = `${modName}.mcpack`;
            downloadLink.style.display = 'none';
            
            document.body.appendChild(downloadLink);
            
            // محاولة التحميل
            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    document.body.removeChild(downloadLink);
                    resolve(false);
                }, 10000); // مهلة 10 ثوان
                
                downloadLink.addEventListener('click', () => {
                    clearTimeout(timeout);
                    
                    // التحقق من نجاح التحميل بعد فترة قصيرة
                    setTimeout(() => {
                        document.body.removeChild(downloadLink);
                        resolve(true);
                    }, 1000);
                });
                
                // محاولة فتح الرابط
                try {
                    downloadLink.click();
                } catch (error) {
                    clearTimeout(timeout);
                    document.body.removeChild(downloadLink);
                    resolve(false);
                }
            });
            
        } catch (error) {
            console.error(`❌ خطأ في محاولة التحميل:`, error);
            return false;
        }
    }
    
    async getBackupDownloadUrl(modId) {
        try {
            // البحث في Firebase Storage عن ملف احتياطي
            if (this.storage) {
                const ref = this.storage.ref(`backup_mods/${modId}.mcpack`);
                const url = await ref.getDownloadURL();
                console.log(`✅ تم العثور على رابط احتياطي في Firebase: ${modId}`);
                return url;
            }
            
            // البحث في قاعدة البيانات عن رابط احتياطي
            if (this.supabaseClient) {
                const { data, error } = await this.supabaseClient
                    .from('mod_backup_urls')
                    .select('backup_url')
                    .eq('mod_id', modId)
                    .eq('is_active', true)
                    .single();
                
                if (data && data.backup_url) {
                    console.log(`✅ تم العثور على رابط احتياطي في قاعدة البيانات: ${modId}`);
                    return data.backup_url;
                }
            }
            
            console.log(`⚠️ لم يتم العثور على رابط احتياطي للمود: ${modId}`);
            return null;
            
        } catch (error) {
            console.error(`❌ خطأ في البحث عن رابط احتياطي:`, error);
            return null;
        }
    }
    
    async reportDownloadError(modId, modName, originalUrl, backupUrl, errorType, errorMessage = '') {
        try {
            if (!this.supabaseClient) return;
            
            const errorReport = {
                mod_id: modId,
                mod_name: modName,
                original_url: originalUrl,
                backup_url: backupUrl,
                error_type: errorType,
                error_message: errorMessage,
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                is_resolved: false,
                reported_at: new Date().toISOString()
            };
            
            const { data, error } = await this.supabaseClient
                .from('download_errors')
                .insert([errorReport]);
            
            if (error) {
                console.error('❌ خطأ في تسجيل خطأ التحميل:', error);
            } else {
                console.log('✅ تم تسجيل خطأ التحميل للمراجعة');
                
                // إرسال تنبيه للأدمن
                this.notifyAdmin(errorReport);
            }
            
        } catch (error) {
            console.error('❌ خطأ في تقرير خطأ التحميل:', error);
        }
    }
    
    async handleDownloadFailure(modId, modName, originalUrl, backupUrl, errorMessage = '') {
        console.error(`❌ فشل تحميل المود: ${modName}`);
        
        // تسجيل الخطأ
        await this.reportDownloadError(
            modId, 
            modName, 
            originalUrl, 
            backupUrl, 
            'complete_failure', 
            errorMessage
        );
        
        // عرض رسالة للمستخدم
        this.showDownloadErrorMessage(modName);
        
        // إضافة إلى قائمة التحميلات الفاشلة
        this.failedDownloads.set(modId, {
            modName,
            originalUrl,
            backupUrl,
            timestamp: Date.now(),
            errorMessage
        });
    }
    
    showDownloadErrorMessage(modName) {
        const message = `
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>مشكلة في التحميل</strong><br>
                عذراً، حدثت مشكلة في تحميل المود "${modName}".<br>
                <small>تم إرسال تقرير للإدارة وسيتم إصلاح المشكلة قريباً.</small>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة الرسالة إلى أعلى الصفحة
        const container = document.querySelector('.container') || document.body;
        const alertDiv = document.createElement('div');
        alertDiv.innerHTML = message;
        container.insertBefore(alertDiv, container.firstChild);
        
        // إزالة الرسالة تلقائياً بعد 10 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 10000);
    }
    
    notifyAdmin(errorReport) {
        // إرسال تنبيه للأدمن (يمكن تطويره لاحقاً)
        console.log('📧 تم إرسال تنبيه للأدمن:', errorReport);
        
        // يمكن إضافة إرسال إيميل أو webhook هنا
        if (typeof window !== 'undefined' && window.adminNotificationSystem) {
            window.adminNotificationSystem.sendNotification({
                type: 'download_error',
                title: 'خطأ في تحميل مود',
                message: `فشل تحميل المود: ${errorReport.mod_name}`,
                data: errorReport
            });
        }
    }
    
    // وظائف مساعدة للتحقق من حالة التحميل
    async checkDownloadHealth() {
        const healthReport = {
            totalAttempts: this.downloadAttempts.size,
            failedDownloads: this.failedDownloads.size,
            successRate: 0,
            timestamp: new Date().toISOString()
        };
        
        if (this.supabaseClient) {
            try {
                // إحصائيات من قاعدة البيانات
                const { data, error } = await this.supabaseClient
                    .from('download_errors')
                    .select('*')
                    .gte('timestamp', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());
                
                if (data) {
                    healthReport.errorsLast24h = data.length;
                    healthReport.unresolvedErrors = data.filter(e => !e.is_resolved).length;
                }
            } catch (error) {
                console.error('❌ خطأ في فحص صحة التحميل:', error);
            }
        }
        
        return healthReport;
    }
    
    // تنظيف البيانات المؤقتة
    cleanup() {
        // تنظيف المحاولات القديمة (أكثر من ساعة)
        const oneHourAgo = Date.now() - 60 * 60 * 1000;
        
        for (const [key, attempt] of this.downloadAttempts.entries()) {
            if (attempt.startTime < oneHourAgo) {
                this.downloadAttempts.delete(key);
            }
        }
        
        // تنظيف التحميلات الفاشلة القديمة (أكثر من يوم)
        const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
        
        for (const [key, failure] of this.failedDownloads.entries()) {
            if (failure.timestamp < oneDayAgo) {
                this.failedDownloads.delete(key);
            }
        }
    }
}

// إنشاء مثيل عام للنظام
window.downloadFallbackSystem = new DownloadFallbackSystem();

// تنظيف دوري كل ساعة
setInterval(() => {
    window.downloadFallbackSystem.cleanup();
}, 60 * 60 * 1000);

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DownloadFallbackSystem;
}
