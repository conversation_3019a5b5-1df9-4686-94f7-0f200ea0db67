<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منشئ الاشتراك المجاني الذكي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="smart-subscription-creator.css">
</head>
<body>
    <!-- Header -->
    <header class="smart-header">
        <div class="header-content">
            <div class="header-left">
                <h1><i class="fas fa-magic"></i> منشئ الاشتراك المجاني الذكي</h1>
                <p>إنشاء حملات اشتراك مجاني احترافية مع ذكاء اصطناعي</p>
            </div>
            <div class="header-right">
                <button class="btn btn-ghost" onclick="openDisplayCustomizationGuide()">
                    <i class="fas fa-question-circle"></i> دليل التخصيص
                </button>
                <button class="btn btn-ghost" onclick="previewAllDisplayModes()">
                    <i class="fas fa-eye"></i> معاينة شاملة
                </button>
                <button class="btn btn-secondary" onclick="loadTemplate()">
                    <i class="fas fa-file-import"></i> تحميل قالب
                </button>
                <button class="btn btn-secondary" onclick="loadDisplayTemplate()">
                    <i class="fas fa-mobile-alt"></i> قوالب العرض
                </button>
                <button class="btn btn-primary" onclick="saveTemplate()">
                    <i class="fas fa-save"></i> حفظ كقالب
                </button>
                <button class="btn btn-success" onclick="exportFullCampaign()">
                    <i class="fas fa-download"></i> تصدير شامل
                </button>
            </div>
        </div>
    </header>

    <!-- Status Banner -->
    <div id="statusBanner" style="display: none; background: linear-gradient(45deg, #22c55e, #16a34a); color: white; padding: 10px; text-align: center; font-weight: bold;">
        <i class="fas fa-rocket"></i>
        الوضع المحلي: المنشئ يعمل بأقصى سرعة وكفاءة! جميع الوظائف متاحة بدون انتظار.
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-text">
            <span id="progressText">0% مكتمل</span>
            <span id="validationStatus" class="validation-status"></span>
        </div>
    </div>

    <!-- Main Container -->
    <div class="smart-container">
        <!-- Left Sidebar - Smart Assistant -->
        <aside class="smart-sidebar">
            <div class="assistant-card">
                <div class="assistant-header">
                    <i class="fas fa-robot"></i>
                    <h3>المساعد الذكي</h3>
                </div>
                <div class="assistant-body">
                    <div class="suggestion-item active" data-suggestion="quick-setup">
                        <i class="fas fa-bolt"></i>
                        <span>إعداد سريع (5 دقائق)</span>
                    </div>
                    <div class="suggestion-item" data-suggestion="advanced-setup">
                        <i class="fas fa-cogs"></i>
                        <span>إعداد متقدم (15 دقيقة)</span>
                    </div>
                    <div class="suggestion-item" data-suggestion="expert-setup">
                        <i class="fas fa-crown"></i>
                        <span>إعداد خبير (30 دقيقة)</span>
                    </div>
                    <div class="suggestion-item" data-suggestion="ai-optimize">
                        <i class="fas fa-brain"></i>
                        <span>تحسين بالذكاء الاصطناعي</span>
                    </div>
                </div>
            </div>

            <!-- Real-time Validation -->
            <div class="validation-card">
                <h4><i class="fas fa-shield-check"></i> التحقق المباشر</h4>
                <div class="validation-list" id="validationList">
                    <div class="validation-item pending" data-field="title">
                        <i class="fas fa-circle"></i>
                        <span>عنوان الحملة</span>
                    </div>
                    <div class="validation-item pending" data-field="description">
                        <i class="fas fa-circle"></i>
                        <span>وصف الحملة</span>
                    </div>
                    <div class="validation-item pending" data-field="tasks">
                        <i class="fas fa-circle"></i>
                        <span>المهام المطلوبة</span>
                    </div>
                    <div class="validation-item pending" data-field="settings">
                        <i class="fas fa-circle"></i>
                        <span>إعدادات الحملة</span>
                    </div>
                </div>
            </div>

            <!-- Smart Analytics -->
            <div class="analytics-card">
                <h4><i class="fas fa-chart-line"></i> تحليلات ذكية</h4>
                <div class="analytics-item">
                    <span>معدل النجاح المتوقع</span>
                    <div class="analytics-value" id="successRate">--</div>
                </div>
                <div class="analytics-item">
                    <span>الوقت المتوقع للإكمال</span>
                    <div class="analytics-value" id="completionTime">--</div>
                </div>
                <div class="analytics-item">
                    <span>تقييم صعوبة المهام</span>
                    <div class="analytics-value" id="difficultyRating">--</div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="smart-main">
            <!-- Campaign Basic Info -->
            <section class="form-section active" id="basicInfo">
                <div class="section-header">
                    <h2><i class="fas fa-info-circle"></i> معلومات الحملة الأساسية</h2>
                    <div class="section-actions">
                        <button class="btn btn-ghost" onclick="generateWithAI('basic')">
                            <i class="fas fa-magic"></i> توليد بالذكاء الاصطناعي
                        </button>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="campaignTitleAr">
                            عنوان الحملة (عربي) *
                            <span class="field-hint">عنوان جذاب يحفز المستخدمين</span>
                        </label>
                        <input type="text" id="campaignTitleAr" placeholder="احصل على اشتراك مجاني لمدة شهر!" 
                               data-validation="required,min:10,max:100" oninput="validateField(this)">
                        <div class="field-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label for="campaignTitleEn">
                            عنوان الحملة (إنجليزي) *
                            <span class="field-hint">Attractive title that motivates users</span>
                        </label>
                        <input type="text" id="campaignTitleEn" placeholder="Get Free 1-Month Subscription!" 
                               data-validation="required,min:10,max:100" oninput="validateField(this)">
                        <div class="field-feedback"></div>
                    </div>

                    <div class="form-group full-width">
                        <label for="campaignDescAr">
                            وصف الحملة (عربي) *
                            <span class="field-hint">وصف مفصل يوضح فوائد الاشتراك</span>
                        </label>
                        <textarea id="campaignDescAr" rows="4" 
                                  placeholder="احصل على وصول كامل لجميع المودات والميزات المميزة..."
                                  data-validation="required,min:50,max:500" oninput="validateField(this)"></textarea>
                        <div class="field-feedback"></div>
                        <div class="char-counter">
                            <span id="descArCounter">0</span>/500 حرف
                        </div>
                    </div>

                    <div class="form-group full-width">
                        <label for="campaignDescEn">
                            وصف الحملة (إنجليزي) *
                            <span class="field-hint">Detailed description explaining subscription benefits</span>
                        </label>
                        <textarea id="campaignDescEn" rows="4" 
                                  placeholder="Get full access to all mods and premium features..."
                                  data-validation="required,min:50,max:500" oninput="validateField(this)"></textarea>
                        <div class="field-feedback"></div>
                        <div class="char-counter">
                            <span id="descEnCounter">0</span>/500 حرف
                        </div>
                    </div>
                </div>
            </section>

            <!-- Campaign Settings -->
            <section class="form-section" id="campaignSettings">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> إعدادات الحملة المتقدمة</h2>
                    <div class="section-actions">
                        <button class="btn btn-ghost" onclick="optimizeSettings()">
                            <i class="fas fa-brain"></i> تحسين ذكي
                        </button>
                    </div>
                </div>

                <div class="settings-grid">
                    <!-- Duration Settings -->
                    <div class="setting-card">
                        <h4><i class="fas fa-calendar-alt"></i> إعدادات المدة</h4>
                        <div class="form-group">
                            <label for="subscriptionDuration">مدة الاشتراك (بالأيام) *</label>
                            <div class="input-with-slider">
                                <input type="range" id="durationSlider" min="1" max="365" value="30" 
                                       oninput="updateDurationValue(this.value)">
                                <input type="number" id="subscriptionDuration" value="30" min="1" max="365" 
                                       oninput="updateDurationSlider(this.value)">
                            </div>
                            <div class="duration-presets">
                                <button class="preset-btn" onclick="setDuration(7)">أسبوع</button>
                                <button class="preset-btn active" onclick="setDuration(30)">شهر</button>
                                <button class="preset-btn" onclick="setDuration(90)">3 أشهر</button>
                                <button class="preset-btn" onclick="setDuration(365)">سنة</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="campaignEndDate">تاريخ انتهاء الحملة</label>
                            <input type="datetime-local" id="campaignEndDate" onchange="validateEndDate(this)">
                            <div class="field-feedback"></div>
                        </div>
                    </div>

                    <!-- User Limits -->
                    <div class="setting-card">
                        <h4><i class="fas fa-users"></i> حدود المستخدمين</h4>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enableUserLimit" onchange="toggleUserLimit(this)">
                                تفعيل حد أقصى للمستخدمين
                            </label>
                        </div>
                        <div class="form-group" id="userLimitGroup" style="display: none;">
                            <label for="maxUsers">الحد الأقصى للمستخدمين</label>
                            <input type="number" id="maxUsers" min="1" placeholder="مثال: 1000">
                            <div class="limit-presets">
                                <button class="preset-btn" onclick="setUserLimit(100)">100</button>
                                <button class="preset-btn" onclick="setUserLimit(500)">500</button>
                                <button class="preset-btn" onclick="setUserLimit(1000)">1000</button>
                                <button class="preset-btn" onclick="setUserLimit(5000)">5000</button>
                            </div>
                        </div>
                    </div>

                    <!-- Verification Settings -->
                    <div class="setting-card">
                        <h4><i class="fas fa-shield-check"></i> إعدادات التحقق</h4>
                        <div class="form-group">
                            <label for="verificationStrictness">مستوى صرامة التحقق</label>
                            <select id="verificationStrictness" onchange="updateVerificationInfo(this)">
                                <option value="low">منخفض - تحقق بسيط</option>
                                <option value="medium" selected>متوسط - تحقق متوازن</option>
                                <option value="high">عالي - تحقق صارم</option>
                            </select>
                            <div class="verification-info" id="verificationInfo">
                                <i class="fas fa-info-circle"></i>
                                <span>تحقق متوازن مع إمكانية إعادة المحاولة</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="autoVerifyEnabled" checked>
                                تفعيل التحقق التلقائي
                            </label>
                            <small>يقوم النظام بالتحقق تلقائياً من إكمال المهام</small>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Tasks Management -->
            <section class="form-section" id="tasksManagement">
                <div class="section-header">
                    <h2><i class="fas fa-tasks"></i> إدارة المهام المطلوبة</h2>
                    <div class="section-actions">
                        <button class="btn btn-ghost" onclick="addSmartTasks()">
                            <i class="fas fa-magic"></i> إضافة مهام ذكية
                        </button>
                        <button class="btn btn-secondary" onclick="addCustomTask()">
                            <i class="fas fa-plus"></i> إضافة مهمة مخصصة
                        </button>
                    </div>
                </div>

                <div class="tasks-container">
                    <!-- Task Templates -->
                    <div class="task-templates">
                        <h4><i class="fas fa-layer-group"></i> قوالب المهام السريعة</h4>
                        <div class="template-grid">
                            <div class="template-card" onclick="applyTaskTemplate('social')">
                                <i class="fas fa-share-alt"></i>
                                <h5>مهام وسائل التواصل</h5>
                                <p>تيليجرام + يوتيوب + تويتر</p>
                                <span class="template-badge">3 مهام</span>
                            </div>
                            <div class="template-card" onclick="applyTaskTemplate('apps')">
                                <i class="fas fa-mobile-alt"></i>
                                <h5>مهام التطبيقات</h5>
                                <p>تحميل تطبيقات + تقييم</p>
                                <span class="template-badge">2 مهام</span>
                            </div>
                            <div class="template-card" onclick="applyTaskTemplate('complete')">
                                <i class="fas fa-star"></i>
                                <h5>مجموعة شاملة</h5>
                                <p>جميع أنواع المهام</p>
                                <span class="template-badge">5 مهام</span>
                            </div>
                        </div>
                    </div>

                    <!-- Current Tasks List -->
                    <div class="current-tasks">
                        <h4><i class="fas fa-list-check"></i> المهام الحالية</h4>
                        <div class="tasks-list" id="tasksList">
                            <div class="empty-tasks">
                                <i class="fas fa-inbox"></i>
                                <p>لم يتم إضافة أي مهام بعد</p>
                                <button class="btn btn-primary" onclick="addCustomTask()">
                                    <i class="fas fa-plus"></i> إضافة أول مهمة
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Task Creation Form -->
                    <div class="task-form" id="taskForm" style="display: none;">
                        <h4><i class="fas fa-plus-circle"></i> إضافة مهمة جديدة</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="taskType">نوع المهمة *</label>
                                <select id="taskType" onchange="updateTaskForm(this.value)">
                                    <option value="">اختر نوع المهمة</option>
                                    <option value="telegram_subscribe">اشتراك في تيليجرام</option>
                                    <option value="youtube_subscribe">اشتراك في يوتيوب</option>
                                    <option value="twitter_follow">متابعة على تويتر</option>
                                    <option value="facebook_like">إعجاب فيسبوك</option>
                                    <option value="app_download">تحميل تطبيق</option>
                                    <option value="mod_download">تحميل مود</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="taskTitleAr">عنوان المهمة (عربي) *</label>
                                <input type="text" id="taskTitleAr" placeholder="اشترك في قناة التيليجرام">
                            </div>

                            <div class="form-group">
                                <label for="taskTitleEn">عنوان المهمة (إنجليزي) *</label>
                                <input type="text" id="taskTitleEn" placeholder="Subscribe to Telegram Channel">
                            </div>

                            <div class="form-group full-width">
                                <label for="taskDescAr">وصف المهمة (عربي)</label>
                                <textarea id="taskDescAr" rows="2" placeholder="اشترك في قناتنا على التيليجرام للحصول على آخر التحديثات"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="taskDescEn">وصف المهمة (إنجليزي)</label>
                                <textarea id="taskDescEn" rows="2" placeholder="Subscribe to our Telegram channel for latest updates"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="taskUrl">رابط المهمة *</label>
                                <input type="url" id="taskUrl" placeholder="https://t.me/channel">
                            </div>

                            <div class="form-group">
                                <label for="taskRequired">
                                    <input type="checkbox" id="taskRequired" checked>
                                    مهمة إجبارية
                                </label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="saveTask()">
                                <i class="fas fa-save"></i> حفظ المهمة
                            </button>
                            <button class="btn btn-secondary" onclick="cancelTask()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- App Display Customization -->
            <section class="form-section" id="displayCustomization">
                <div class="section-header">
                    <h2><i class="fas fa-mobile-alt"></i> تخصيص العرض في التطبيق</h2>
                    <div class="section-actions">
                        <button class="btn btn-ghost" onclick="previewAppDisplay()">
                            <i class="fas fa-eye"></i> معاينة في التطبيق
                        </button>
                        <button class="btn btn-ghost" onclick="testDisplayModes()">
                            <i class="fas fa-play"></i> اختبار جميع الأنماط
                        </button>
                        <button class="btn btn-ghost" onclick="copyDisplaySettings()">
                            <i class="fas fa-copy"></i> نسخ الإعدادات
                        </button>
                        <button class="btn btn-ghost" onclick="pasteDisplaySettings()">
                            <i class="fas fa-paste"></i> لصق الإعدادات
                        </button>
                        <button class="btn btn-secondary" onclick="resetDisplaySettings()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                        <button class="btn btn-primary" onclick="saveDisplayTemplate()">
                            <i class="fas fa-bookmark"></i> حفظ كقالب
                        </button>
                    </div>
                </div>

                <div class="display-container">
                    <!-- Display Templates -->
                    <div class="display-section">
                        <h4><i class="fas fa-layer-group"></i> قوالب العرض الجاهزة</h4>
                        <div class="template-grid">
                            <div class="display-template" onclick="applyDisplayTemplate('minimal')">
                                <div class="template-preview minimal-preview">
                                    <i class="fas fa-circle"></i>
                                    <span>بسيط</span>
                                </div>
                                <h5>عرض بسيط</h5>
                                <p>نافذة منبثقة بسيطة ونظيفة</p>
                            </div>
                            <div class="display-template" onclick="applyDisplayTemplate('premium')">
                                <div class="template-preview premium-preview">
                                    <i class="fas fa-crown"></i>
                                    <span>مميز</span>
                                </div>
                                <h5>عرض مميز</h5>
                                <p>عرض ذهبي مع تأثيرات متقدمة</p>
                            </div>
                            <div class="display-template" onclick="applyDisplayTemplate('gaming')">
                                <div class="template-preview gaming-preview">
                                    <i class="fas fa-gamepad"></i>
                                    <span>ألعاب</span>
                                </div>
                                <h5>عرض ألعاب</h5>
                                <p>تأثيرات متحركة وألوان زاهية</p>
                            </div>
                            <div class="display-template" onclick="applyDisplayTemplate('professional')">
                                <div class="template-preview professional-preview">
                                    <i class="fas fa-briefcase"></i>
                                    <span>احترافي</span>
                                </div>
                                <h5>عرض احترافي</h5>
                                <p>تصميم أنيق للتطبيقات المهنية</p>
                            </div>
                            <div class="display-template" onclick="applyDisplayTemplate('colorful')">
                                <div class="template-preview colorful-preview">
                                    <i class="fas fa-rainbow"></i>
                                    <span>ملون</span>
                                </div>
                                <h5>عرض ملون</h5>
                                <p>ألوان زاهية ومتدرجة</p>
                            </div>
                            <div class="display-template" onclick="applyDisplayTemplate('dark')">
                                <div class="template-preview dark-preview">
                                    <i class="fas fa-moon"></i>
                                    <span>داكن</span>
                                </div>
                                <h5>عرض داكن</h5>
                                <p>تصميم داكن أنيق</p>
                            </div>
                        </div>
                    </div>

                    <!-- Display Location Settings -->
                    <div class="display-section">
                        <h4><i class="fas fa-map-marker-alt"></i> مكان الظهور في التطبيق</h4>
                        <div class="location-grid">
                            <div class="location-option">
                                <input type="checkbox" id="showInPopup" checked>
                                <label for="showInPopup">
                                    <i class="fas fa-window-maximize"></i>
                                    <span>نافذة منبثقة عند فتح التطبيق</span>
                                </label>
                            </div>
                            <div class="location-option">
                                <input type="checkbox" id="showInBanner">
                                <label for="showInBanner">
                                    <i class="fas fa-rectangle-ad"></i>
                                    <span>بانر في أعلى الشاشة الرئيسية</span>
                                </label>
                            </div>
                            <div class="location-option">
                                <input type="checkbox" id="showInMenu">
                                <label for="showInMenu">
                                    <i class="fas fa-bars"></i>
                                    <span>عنصر في القائمة الجانبية</span>
                                </label>
                            </div>
                            <div class="location-option">
                                <input type="checkbox" id="showInDownload">
                                <label for="showInDownload">
                                    <i class="fas fa-download"></i>
                                    <span>قبل تحميل المودات</span>
                                </label>
                            </div>
                            <div class="location-option">
                                <input type="checkbox" id="showInNotification">
                                <label for="showInNotification">
                                    <i class="fas fa-bell"></i>
                                    <span>إشعار دوري</span>
                                </label>
                            </div>
                            <div class="location-option">
                                <input type="checkbox" id="showInFloating">
                                <label for="showInFloating">
                                    <i class="fas fa-circle"></i>
                                    <span>أيقونة عائمة</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Visual Appearance Settings -->
                    <div class="display-section">
                        <h4><i class="fas fa-palette"></i> المظهر البصري</h4>
                        <div class="appearance-grid">
                            <div class="form-group">
                                <label for="displayStyle">نمط العرض</label>
                                <select id="displayStyle" onchange="updateDisplayPreview()">
                                    <option value="modern">عصري - تدرجات ملونة</option>
                                    <option value="classic">كلاسيكي - ألوان ثابتة</option>
                                    <option value="neon">نيون - توهج مضيء</option>
                                    <option value="minimal">بسيط - تصميم نظيف</option>
                                    <option value="gaming">ألعاب - تأثيرات متحركة</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="primaryColor">اللون الأساسي</label>
                                <div class="color-picker-group">
                                    <input type="color" id="primaryColor" value="#ffd700" onchange="updateDisplayPreview()">
                                    <div class="color-presets">
                                        <div class="color-preset" style="background: #ffd700;" onclick="setColor('primaryColor', '#ffd700')"></div>
                                        <div class="color-preset" style="background: #ff6b6b;" onclick="setColor('primaryColor', '#ff6b6b')"></div>
                                        <div class="color-preset" style="background: #4ecdc4;" onclick="setColor('primaryColor', '#4ecdc4')"></div>
                                        <div class="color-preset" style="background: #45b7d1;" onclick="setColor('primaryColor', '#45b7d1')"></div>
                                        <div class="color-preset" style="background: #96ceb4;" onclick="setColor('primaryColor', '#96ceb4')"></div>
                                        <div class="color-preset" style="background: #feca57;" onclick="setColor('primaryColor', '#feca57')"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="secondaryColor">اللون الثانوي</label>
                                <div class="color-picker-group">
                                    <input type="color" id="secondaryColor" value="#ffcc00" onchange="updateDisplayPreview()">
                                    <div class="color-presets">
                                        <div class="color-preset" style="background: #ffcc00;" onclick="setColor('secondaryColor', '#ffcc00')"></div>
                                        <div class="color-preset" style="background: #ff5252;" onclick="setColor('secondaryColor', '#ff5252')"></div>
                                        <div class="color-preset" style="background: #1dd1a1;" onclick="setColor('secondaryColor', '#1dd1a1')"></div>
                                        <div class="color-preset" style="background: #3742fa;" onclick="setColor('secondaryColor', '#3742fa')"></div>
                                        <div class="color-preset" style="background: #7bed9f;" onclick="setColor('secondaryColor', '#7bed9f')"></div>
                                        <div class="color-preset" style="background: #ff9ff3;" onclick="setColor('secondaryColor', '#ff9ff3')"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="displayIcon">أيقونة العرض</label>
                                <select id="displayIcon" onchange="updateDisplayPreview()">
                                    <option value="fas fa-crown">👑 تاج (VIP)</option>
                                    <option value="fas fa-gift">🎁 هدية</option>
                                    <option value="fas fa-star">⭐ نجمة</option>
                                    <option value="fas fa-rocket">🚀 صاروخ</option>
                                    <option value="fas fa-gem">💎 جوهرة</option>
                                    <option value="fas fa-fire">🔥 نار</option>
                                    <option value="fas fa-bolt">⚡ برق</option>
                                    <option value="fas fa-magic">✨ سحر</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Animation and Effects -->
                    <div class="display-section">
                        <h4><i class="fas fa-play"></i> الحركة والتأثيرات</h4>
                        <div class="effects-grid">
                            <div class="form-group">
                                <label for="animationType">نوع الحركة</label>
                                <select id="animationType" onchange="updateDisplayPreview()">
                                    <option value="fade">تلاشي تدريجي</option>
                                    <option value="slide">انزلاق من الأعلى</option>
                                    <option value="bounce">ارتداد</option>
                                    <option value="zoom">تكبير تدريجي</option>
                                    <option value="rotate">دوران</option>
                                    <option value="pulse">نبضة</option>
                                    <option value="shake">اهتزاز</option>
                                    <option value="glow">توهج</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="animationSpeed">سرعة الحركة</label>
                                <select id="animationSpeed">
                                    <option value="slow">بطيء (2 ثانية)</option>
                                    <option value="normal" selected>عادي (1 ثانية)</option>
                                    <option value="fast">سريع (0.5 ثانية)</option>
                                    <option value="instant">فوري (0.2 ثانية)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enableParticles">
                                    تفعيل تأثيرات الجسيمات
                                </label>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enableSound">
                                    تفعيل الصوت عند الظهور
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Timing and Frequency -->
                    <div class="display-section">
                        <h4><i class="fas fa-clock"></i> التوقيت والتكرار</h4>
                        <div class="timing-grid">
                            <div class="form-group">
                                <label for="showFrequency">تكرار الظهور</label>
                                <select id="showFrequency" onchange="toggleCustomFrequency(this.value)">
                                    <option value="once">مرة واحدة فقط</option>
                                    <option value="daily">يومياً</option>
                                    <option value="session">كل جلسة</option>
                                    <option value="hourly">كل ساعة</option>
                                    <option value="custom">مخصص</option>
                                </select>
                            </div>

                            <div class="form-group" id="customFrequencyGroup" style="display: none;">
                                <label for="customFrequency">التكرار المخصص (بالدقائق)</label>
                                <input type="number" id="customFrequency" min="1" max="1440" value="60">
                            </div>

                            <div class="form-group">
                                <label for="showDelay">تأخير الظهور (ثواني)</label>
                                <input type="range" id="showDelay" min="0" max="30" value="3" oninput="updateDelayValue(this.value)">
                                <span id="delayValue">3 ثواني</span>
                            </div>

                            <div class="form-group">
                                <label for="displayDuration">مدة البقاء (ثواني)</label>
                                <input type="range" id="displayDuration" min="5" max="60" value="15" oninput="updateDurationValue(this.value)">
                                <span id="durationValue">15 ثانية</span>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Display Settings -->
                    <div class="display-section">
                        <h4><i class="fas fa-sliders-h"></i> إعدادات متقدمة للعرض</h4>
                        <div class="advanced-settings-grid">
                            <div class="form-group">
                                <label for="displayPriority">أولوية العرض</label>
                                <select id="displayPriority">
                                    <option value="low">منخفضة - يظهر بعد المحتوى</option>
                                    <option value="normal" selected>عادية - يظهر مع المحتوى</option>
                                    <option value="high">عالية - يظهر قبل المحتوى</option>
                                    <option value="urgent">عاجلة - يظهر فوراً</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="displaySize">حجم العرض</label>
                                <select id="displaySize">
                                    <option value="small">صغير - 250x150</option>
                                    <option value="medium" selected>متوسط - 350x200</option>
                                    <option value="large">كبير - 450x250</option>
                                    <option value="fullscreen">ملء الشاشة</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="displayPosition">موضع العرض</label>
                                <select id="displayPosition">
                                    <option value="center" selected>وسط الشاشة</option>
                                    <option value="top">أعلى الشاشة</option>
                                    <option value="bottom">أسفل الشاشة</option>
                                    <option value="left">يسار الشاشة</option>
                                    <option value="right">يمين الشاشة</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="backgroundBlur">ضبابية الخلفية</label>
                                <input type="range" id="backgroundBlur" min="0" max="20" value="10" oninput="updateBlurValue(this.value)">
                                <span id="blurValue">10px</span>
                            </div>

                            <div class="form-group">
                                <label for="displayOpacity">شفافية العرض</label>
                                <input type="range" id="displayOpacity" min="50" max="100" value="95" oninput="updateOpacityValue(this.value)">
                                <span id="opacityValue">95%</span>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enableCloseButton" checked>
                                    إظهار زر الإغلاق
                                </label>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enableDragMove">
                                    السماح بسحب النافذة
                                </label>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enableAutoClose">
                                    إغلاق تلقائي بعد انتهاء المدة
                                </label>
                            </div>

                            <div class="form-group">
                                <label for="customCSS">CSS مخصص</label>
                                <textarea id="customCSS" rows="3" placeholder="أضف CSS مخصص هنا..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- User Interaction Settings -->
                    <div class="display-section">
                        <h4><i class="fas fa-hand-pointer"></i> إعدادات التفاعل</h4>
                        <div class="interaction-grid">
                            <div class="form-group">
                                <label for="clickAction">إجراء عند النقر</label>
                                <select id="clickAction">
                                    <option value="start_tasks">بدء المهام مباشرة</option>
                                    <option value="show_details" selected>عرض تفاصيل الحملة</option>
                                    <option value="redirect_page">توجيه لصفحة مخصصة</option>
                                    <option value="close_popup">إغلاق النافذة</option>
                                </select>
                            </div>

                            <div class="form-group" id="redirectUrlGroup" style="display: none;">
                                <label for="redirectUrl">رابط التوجيه</label>
                                <input type="url" id="redirectUrl" placeholder="https://example.com">
                            </div>

                            <div class="form-group">
                                <label for="hoverEffect">تأثير عند التمرير</label>
                                <select id="hoverEffect">
                                    <option value="none">بدون تأثير</option>
                                    <option value="scale" selected>تكبير</option>
                                    <option value="glow">توهج</option>
                                    <option value="shake">اهتزاز</option>
                                    <option value="rotate">دوران</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enableHapticFeedback">
                                    تفعيل الاهتزاز عند اللمس
                                </label>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enableSwipeGestures">
                                    تفعيل إيماءات السحب
                                </label>
                            </div>

                            <div class="form-group">
                                <label for="swipeAction">إجراء السحب</label>
                                <select id="swipeAction">
                                    <option value="close">إغلاق عند السحب لأعلى</option>
                                    <option value="minimize">تصغير عند السحب لأسفل</option>
                                    <option value="next">التالي عند السحب يميناً</option>
                                    <option value="previous">السابق عند السحب يساراً</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Live Preview -->
                    <div class="display-section">
                        <h4><i class="fas fa-eye"></i> معاينة مباشرة</h4>
                        <div class="live-display-preview">
                            <div class="phone-mockup-display">
                                <div class="phone-screen-display">
                                    <div class="app-header">
                                        <span>Minecraft Mods</span>
                                        <i class="fas fa-bars"></i>
                                    </div>

                                    <!-- Banner Preview -->
                                    <div class="banner-preview" id="bannerPreview" style="display: none;">
                                        <i class="fas fa-crown"></i>
                                        <span>اشتراك مجاني متاح!</span>
                                    </div>

                                    <!-- Popup Preview -->
                                    <div class="popup-preview" id="popupPreview">
                                        <div class="popup-content">
                                            <i class="fas fa-crown popup-icon"></i>
                                            <h3>اشتراك مجاني!</h3>
                                            <p>احصل على اشتراك مجاني الآن</p>
                                            <button class="popup-btn">ابدأ الآن</button>
                                        </div>
                                    </div>

                                    <!-- Floating Icon Preview -->
                                    <div class="floating-preview" id="floatingPreview" style="display: none;">
                                        <i class="fas fa-crown"></i>
                                    </div>

                                    <div class="app-content">
                                        <div class="mod-item">Mod 1</div>
                                        <div class="mod-item">Mod 2</div>
                                        <div class="mod-item">Mod 3</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Preview & Publish -->
            <section class="form-section" id="previewPublish">
                <div class="section-header">
                    <h2><i class="fas fa-eye"></i> معاينة ونشر الحملة</h2>
                    <div class="section-actions">
                        <button class="btn btn-ghost" onclick="refreshPreview()">
                            <i class="fas fa-sync"></i> تحديث المعاينة
                        </button>
                    </div>
                </div>

                <div class="preview-container">
                    <!-- Campaign Summary -->
                    <div class="campaign-summary">
                        <h4><i class="fas fa-info-circle"></i> ملخص الحملة</h4>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">عنوان الحملة:</span>
                                <span class="summary-value" id="previewTitle">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">مدة الاشتراك:</span>
                                <span class="summary-value" id="previewDuration">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">عدد المهام:</span>
                                <span class="summary-value" id="previewTasksCount">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">مستوى التحقق:</span>
                                <span class="summary-value" id="previewVerification">--</span>
                            </div>
                        </div>
                    </div>

                    <!-- Live Preview -->
                    <div class="live-preview">
                        <h4><i class="fas fa-mobile-alt"></i> معاينة مباشرة</h4>
                        <div class="phone-mockup">
                            <div class="phone-screen">
                                <div class="preview-header">
                                    <h3 id="previewTitleDisplay">عنوان الحملة</h3>
                                    <p id="previewDescDisplay">وصف الحملة</p>
                                </div>
                                <div class="preview-tasks" id="previewTasksDisplay">
                                    <!-- Tasks will be populated here -->
                                </div>
                                <div class="preview-footer">
                                    <button class="preview-btn">ابدأ المهام</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Validation Results -->
                    <div class="final-validation">
                        <h4><i class="fas fa-shield-check"></i> التحقق النهائي</h4>
                        <div class="validation-results" id="finalValidation">
                            <!-- Validation results will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Publish Actions -->
                <div class="publish-actions">
                    <button class="btn btn-success btn-large" onclick="publishCampaign()" id="publishBtn" disabled>
                        <i class="fas fa-rocket"></i> نشر الحملة
                    </button>
                    <button class="btn btn-secondary" onclick="saveDraft()">
                        <i class="fas fa-save"></i> حفظ كمسودة
                    </button>
                    <button class="btn btn-ghost" onclick="exportCampaign()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                </div>
            </section>

            <!-- Navigation -->
            <div class="section-navigation">
                <button class="btn btn-secondary" id="prevBtn" onclick="previousSection()" style="display: none;">
                    <i class="fas fa-arrow-right"></i> السابق
                </button>
                <button class="btn btn-primary" id="nextBtn" onclick="nextSection()">
                    التالي <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="smart-subscription-creator.js"></script>
</body>
</html>
