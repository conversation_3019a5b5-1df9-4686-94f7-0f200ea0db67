// Image Loading Fix for Mod Cards
// This script fixes the "no image" and white image issues

(function() {
    'use strict';

    console.log('🖼️ Loading Image Fix...');

    // Default placeholder image as base64 SVG
    const DEFAULT_PLACEHOLDER = 'image/placeholder.svg';

    // Store globally
    window.DEFAULT_PLACEHOLDER_IMAGE = DEFAULT_PLACEHOLDER;

    // List of essential UI images that should NEVER be modified
    const ESSENTIAL_UI_IMAGES = [
        'menu.png',
        'search.png', 
        'app_name.png',
        'downarrow.png',
        'close_icon.png',
        'back.png',
        'heart.png',
        'Time-icon.png',
        'icon_Addons.png',
        'icon_shaders.png',
        'texter.png',
        'suggested_icon.png'
    ];

    // Check if image is essential UI element
    function isEssentialUIImage(src) {
        return ESSENTIAL_UI_IMAGES.some(img => src.includes(img));
    }

    // Enhanced image error handler - ONLY for failed images
    function handleImageError(img, retryCount = 0) {
        const maxRetries = 2;
        const originalSrc = img.getAttribute('data-original-src') || img.src;
        
        console.warn(`🖼️ Image failed to load (attempt ${retryCount + 1}):`, img.src);

        // Skip if already using placeholder
        if (img.src.includes('placeholder.svg')) {
            return;
        }

        // NEVER modify essential UI images
        if (isEssentialUIImage(img.src)) {
            console.log('🔒 Skipping essential UI image:', img.src);
            return;
        }

        // Try different fallback strategies
        if (retryCount < maxRetries) {
            setTimeout(() => {
                // Try with different path formats ONLY for mod images
                if (retryCount === 0 && originalSrc.includes('../image/')) {
                    img.src = originalSrc.replace('../image/', 'image/');
                    img.setAttribute('data-original-src', originalSrc);
                } else if (retryCount === 1 && originalSrc.includes('image/') && !originalSrc.startsWith('image/')) {
                    img.src = originalSrc.replace(/.*image\//, 'image/');
                } else {
                    // Use placeholder for final attempt
                    img.src = DEFAULT_PLACEHOLDER;
                    img.alt = 'صورةغير متاحة';
                    img.classList.add('placeholder-image');
                }
                
                // Set up error handler for retry
                img.onerror = () => handleImageError(img, retryCount + 1);
            }, 500 * (retryCount + 1));
        } else {
            // Final fallback
            img.src = DEFAULT_PLACEHOLDER;
            img.alt = 'صورة غير متاحة';
            img.classList.add('placeholder-image');
            img.style.opacity = '0.8';
        }
    }

    // Enhanced getMainImage function - CONSERVATIVE approach
    function getMainImageSafe(imageUrls, fallbackPath = null) {
        if (!imageUrls) {
            return fallbackPath || DEFAULT_PLACEHOLDER;
        }

        try {
            let urls = [];
            
            if (Array.isArray(imageUrls)) {
                urls = imageUrls;
            } else if (typeof imageUrls === 'string') {
                // Don't modify existing valid URLs
                if (imageUrls.startsWith('http') || imageUrls.startsWith('data:') || imageUrls.startsWith('image/')) {
                    return imageUrls;
                } else if (imageUrls.trim().startsWith('[') && imageUrls.trim().endsWith(']')) {
                    try {
                        const parsed = JSON.parse(imageUrls);
                        urls = Array.isArray(parsed) ? parsed : [parsed];
                    } catch (e) {
                        console.warn('⚠️ Failed to parse image URLs as JSON:', e);
                        urls = [imageUrls];
                    }
                } else {
                    urls = [imageUrls];
                }
            }

            // Filter and validate URLs - VERY CONSERVATIVE
            const validUrls = urls.filter(url => {
                if (typeof url !== 'string') return false;
                // ONLY reject obvious bad URLs
                return !url.endsWith('.html') && !url.includes('file://') && !url.includes('chrome-extension://');
            });

            if (validUrls.length > 0) {
                return validUrls[0];
            }

        } catch (error) {
            console.error('❌ Error processing image URLs:', error);
        }

        return fallbackPath || DEFAULT_PLACEHOLDER;
    }

    // Override existing functions
    window.getMainImageSafe = getMainImageSafe;
    window.handleImageError = handleImageError;

    // Override getMainImage if it exists
    if (typeof window.getMainImage === 'undefined') {
        window.getMainImage = getMainImageSafe;
    }

    // Add global error handling for all images - ONLY when they fail
    document.addEventListener('error', function(e) {
        if (e.target.tagName === 'IMG' && !e.target.classList.contains('error-handled')) {
            // Skip essential UI images
            if (isEssentialUIImage(e.target.src)) {
                console.log('🔒 Skipping error handling for essential UI image:', e.target.src);
                return;
            }
            e.target.classList.add('error-handled');
            handleImageError(e.target);
        }
    }, true);

    // Fix existing images on page - VERY CONSERVATIVE
    function fixExistingImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            // NEVER touch essential UI images
            if (img.src && isEssentialUIImage(img.src)) {
                return;
            }

            // ONLY fix images that are clearly broken
            if (!img.src || img.src.includes('placeholder.png') || img.src.includes('data:image/svg+xml;base64')) {
                // Try to get image URL from data attributes
                const dataSrc = img.getAttribute('data-src');
                if (dataSrc) {
                    img.src = getMainImageSafe(dataSrc);
                } else {
                    img.src = DEFAULT_PLACEHOLDER;
                }
            }

            // Add error handler if not already present (but not for UI images)
            if (!img.onerror && !isEssentialUIImage(img.src)) {
                img.onerror = () => handleImageError(img);
            }
        });
    }

    // Apply fixes when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixExistingImages);
    } else {
        fixExistingImages();
    }

    // Also fix images after a delay to catch dynamically loaded content
    setTimeout(fixExistingImages, 2000);
    setTimeout(fixExistingImages, 5000);

    // Monitor for new images added to the DOM
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const images = node.tagName === 'IMG' ? [node] : node.querySelectorAll('img');
                        images.forEach(img => {
                            // Only add error handlers to non-UI images
                            if (!img.onerror && !isEssentialUIImage(img.src)) {
                                img.onerror = () => handleImageError(img);
                            }
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    console.log('✅ Image Fix loaded and applied');

})();
