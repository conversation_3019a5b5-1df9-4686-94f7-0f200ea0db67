<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات لوحة الإدارة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #ffd700;
        }
        
        .test-button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        
        .result {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success {
            color: #4ade80;
        }
        
        .error {
            color: #f87171;
        }
        
        .warning {
            color: #fbbf24;
        }
        
        .info {
            color: #60a5fa;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-success {
            background: #4ade80;
        }
        
        .status-error {
            background: #f87171;
        }
        
        .status-warning {
            background: #fbbf24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاحات لوحة الإدارة</h1>
        <p>هذه الصفحة تختبر جميع الإصلاحات المطبقة على لوحة الإدارة</p>
        
        <!-- Test Section 1: Basic Fixes -->
        <div class="test-section">
            <h2>🛠️ اختبار الإصلاحات الأساسية</h2>
            <button class="test-button" onclick="testBasicFixes()">اختبار الإصلاحات الأساسية</button>
            <button class="test-button" onclick="testSupabaseClient()">اختبار عميل Supabase</button>
            <button class="test-button" onclick="testErrorHandling()">اختبار معالجة الأخطاء</button>
            <div id="basic-results" class="result"></div>
        </div>
        
        <!-- Test Section 2: User Data Functions -->
        <div class="test-section">
            <h2>👥 اختبار دوال بيانات المستخدمين</h2>
            <button class="test-button" onclick="testUserDataFunctions()">اختبار تحميل بيانات المستخدمين</button>
            <button class="test-button" onclick="testUserActivity()">اختبار نشاط المستخدمين</button>
            <button class="test-button" onclick="testUserAnalytics()">اختبار تحليلات المستخدمين</button>
            <div id="user-results" class="result"></div>
        </div>
        
        <!-- Test Section 3: Dashboard Functions -->
        <div class="test-section">
            <h2>📊 اختبار دوال لوحة المعلومات</h2>
            <button class="test-button" onclick="testDashboardStats()">اختبار إحصائيات لوحة المعلومات</button>
            <button class="test-button" onclick="testFallbackData()">اختبار البيانات الاحتياطية</button>
            <button class="test-button" onclick="testNetworkRecovery()">اختبار استرداد الشبكة</button>
            <div id="dashboard-results" class="result"></div>
        </div>
        
        <!-- Test Section 4: System Health -->
        <div class="test-section">
            <h2>🏥 اختبار صحة النظام</h2>
            <button class="test-button" onclick="runFullSystemTest()">اختبار شامل للنظام</button>
            <button class="test-button" onclick="checkConsoleErrors()">فحص أخطاء وحدة التحكم</button>
            <button class="test-button" onclick="generateReport()">إنشاء تقرير شامل</button>
            <div id="system-results" class="result"></div>
        </div>
        
        <!-- Status Summary -->
        <div class="test-section">
            <h2>📋 ملخص الحالة</h2>
            <div id="status-summary">
                <p>الإصلاحات الأساسية: <span class="status-indicator" id="basic-status"></span></p>
                <p>دوال المستخدمين: <span class="status-indicator" id="user-status"></span></p>
                <p>لوحة المعلومات: <span class="status-indicator" id="dashboard-status"></span></p>
                <p>صحة النظام: <span class="status-indicator" id="system-status"></span></p>
            </div>
        </div>
    </div>

    <!-- Load Required Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="admin-panel-fixes.js"></script>
    <script src="unified-admin.js"></script>
    <script src="advanced-admin-features.js"></script>

    <script>
        // Test Results Storage
        let testResults = {
            basic: [],
            user: [],
            dashboard: [],
            system: []
        };

        // Utility Functions
        function log(message, type = 'info', section = 'basic') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            
            testResults[section].push({ message: logEntry, type });
            
            const resultDiv = document.getElementById(`${section}-results`);
            if (resultDiv) {
                const logElement = document.createElement('div');
                logElement.className = type;
                logElement.textContent = logEntry;
                resultDiv.appendChild(logElement);
                resultDiv.scrollTop = resultDiv.scrollHeight;
            }
        }

        function updateStatus(section, status) {
            const statusElement = document.getElementById(`${section}-status`);
            if (statusElement) {
                statusElement.className = `status-indicator status-${status}`;
            }
        }

        function clearResults(section) {
            const resultDiv = document.getElementById(`${section}-results`);
            if (resultDiv) {
                resultDiv.innerHTML = '';
            }
            testResults[section] = [];
        }

        // Test Functions
        async function testBasicFixes() {
            clearResults('basic');
            log('🔧 بدء اختبار الإصلاحات الأساسية...', 'info', 'basic');

            try {
                // Test admin panel fixes loading
                if (typeof window.adminPanelFixesApplied !== 'undefined') {
                    log('✅ تم تحميل إصلاحات لوحة الإدارة بنجاح', 'success', 'basic');
                } else {
                    log('⚠️ لم يتم تحميل إصلاحات لوحة الإدارة', 'warning', 'basic');
                }

                // Test error handling enhancement
                if (typeof console.originalError !== 'undefined') {
                    log('✅ تم تحسين معالجة الأخطاء', 'success', 'basic');
                } else {
                    log('ℹ️ معالجة الأخطاء الأساسية متاحة', 'info', 'basic');
                }

                updateStatus('basic', 'success');
                log('✅ اكتمل اختبار الإصلاحات الأساسية', 'success', 'basic');

            } catch (error) {
                log(`❌ خطأ في اختبار الإصلاحات الأساسية: ${error.message}`, 'error', 'basic');
                updateStatus('basic', 'error');
            }
        }

        async function testSupabaseClient() {
            log('🔗 اختبار عميل Supabase...', 'info', 'basic');

            try {
                if (window.supabaseClient) {
                    log('✅ عميل Supabase متاح', 'success', 'basic');
                    
                    // Test basic query
                    const { data, error } = await window.supabaseClient
                        .from('mods')
                        .select('count', { count: 'exact', head: true });
                    
                    if (error && error.code !== '401') {
                        throw error;
                    }
                    
                    log('✅ اختبار الاستعلام الأساسي نجح', 'success', 'basic');
                } else {
                    log('❌ عميل Supabase غير متاح', 'error', 'basic');
                }
            } catch (error) {
                log(`⚠️ خطأ في اختبار Supabase: ${error.message}`, 'warning', 'basic');
            }
        }

        async function testErrorHandling() {
            log('🛡️ اختبار معالجة الأخطاء...', 'info', 'basic');

            try {
                // Simulate 401 error
                const mockError = { code: '401', message: 'Unauthorized' };
                
                // Test if error handling is working
                if (typeof handleFunctionFallback === 'function') {
                    log('✅ دوال معالجة الأخطاء متاحة', 'success', 'basic');
                } else {
                    log('ℹ️ معالجة الأخطاء الأساسية فقط', 'info', 'basic');
                }

                log('✅ اختبار معالجة الأخطاء مكتمل', 'success', 'basic');
            } catch (error) {
                log(`❌ خطأ في اختبار معالجة الأخطاء: ${error.message}`, 'error', 'basic');
            }
        }

        async function testUserDataFunctions() {
            clearResults('user');
            log('👥 اختبار دوال بيانات المستخدمين...', 'info', 'user');

            try {
                // Test loadUsersData function
                if (typeof loadUsersData === 'function') {
                    log('✅ دالة loadUsersData متاحة', 'success', 'user');
                    await loadUsersData();
                    log('✅ تم تنفيذ loadUsersData بنجاح', 'success', 'user');
                } else {
                    log('❌ دالة loadUsersData غير متاحة', 'error', 'user');
                }

                updateStatus('user', 'success');
            } catch (error) {
                log(`❌ خطأ في اختبار دوال المستخدمين: ${error.message}`, 'error', 'user');
                updateStatus('user', 'error');
            }
        }

        async function testUserActivity() {
            log('📊 اختبار نشاط المستخدمين...', 'info', 'user');

            try {
                if (typeof loadUserActivity === 'function') {
                    log('✅ دالة loadUserActivity متاحة', 'success', 'user');
                    await loadUserActivity();
                    log('✅ تم تنفيذ loadUserActivity بنجاح', 'success', 'user');
                } else {
                    log('❌ دالة loadUserActivity غير متاحة', 'error', 'user');
                }
            } catch (error) {
                log(`⚠️ خطأ في اختبار نشاط المستخدمين: ${error.message}`, 'warning', 'user');
            }
        }

        async function testUserAnalytics() {
            log('📈 اختبار تحليلات المستخدمين...', 'info', 'user');

            try {
                if (typeof loadUserAnalytics === 'function') {
                    log('✅ دالة loadUserAnalytics متاحة', 'success', 'user');
                    await loadUserAnalytics();
                    log('✅ تم تنفيذ loadUserAnalytics بنجاح', 'success', 'user');
                } else {
                    log('❌ دالة loadUserAnalytics غير متاحة', 'error', 'user');
                }
            } catch (error) {
                log(`⚠️ خطأ في اختبار تحليلات المستخدمين: ${error.message}`, 'warning', 'user');
            }
        }

        async function testDashboardStats() {
            clearResults('dashboard');
            log('📊 اختبار إحصائيات لوحة المعلومات...', 'info', 'dashboard');

            try {
                if (typeof updateDashboardStats === 'function') {
                    log('✅ دالة updateDashboardStats متاحة', 'success', 'dashboard');
                    await updateDashboardStats();
                    log('✅ تم تنفيذ updateDashboardStats بنجاح', 'success', 'dashboard');
                } else {
                    log('❌ دالة updateDashboardStats غير متاحة', 'error', 'dashboard');
                }

                updateStatus('dashboard', 'success');
            } catch (error) {
                log(`❌ خطأ في اختبار إحصائيات لوحة المعلومات: ${error.message}`, 'error', 'dashboard');
                updateStatus('dashboard', 'error');
            }
        }

        async function testFallbackData() {
            log('🔄 اختبار البيانات الاحتياطية...', 'info', 'dashboard');

            try {
                if (typeof updateFallbackStats === 'function') {
                    log('✅ دالة updateFallbackStats متاحة', 'success', 'dashboard');
                    updateFallbackStats();
                    log('✅ تم تنفيذ البيانات الاحتياطية بنجاح', 'success', 'dashboard');
                } else {
                    log('⚠️ دالة updateFallbackStats غير متاحة', 'warning', 'dashboard');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار البيانات الاحتياطية: ${error.message}`, 'error', 'dashboard');
            }
        }

        async function testNetworkRecovery() {
            log('🌐 اختبار استرداد الشبكة...', 'info', 'dashboard');

            try {
                // Test network status
                if (navigator.onLine) {
                    log('✅ الاتصال بالشبكة متاح', 'success', 'dashboard');
                } else {
                    log('⚠️ لا يوجد اتصال بالشبكة', 'warning', 'dashboard');
                }

                log('✅ اختبار استرداد الشبكة مكتمل', 'success', 'dashboard');
            } catch (error) {
                log(`❌ خطأ في اختبار استرداد الشبكة: ${error.message}`, 'error', 'dashboard');
            }
        }

        async function runFullSystemTest() {
            clearResults('system');
            log('🏥 بدء الاختبار الشامل للنظام...', 'info', 'system');

            try {
                // Test all major components
                await testBasicFixes();
                await testUserDataFunctions();
                await testDashboardStats();

                log('✅ اكتمل الاختبار الشامل للنظام بنجاح', 'success', 'system');
                updateStatus('system', 'success');
            } catch (error) {
                log(`❌ خطأ في الاختبار الشامل: ${error.message}`, 'error', 'system');
                updateStatus('system', 'error');
            }
        }

        async function checkConsoleErrors() {
            log('🔍 فحص أخطاء وحدة التحكم...', 'info', 'system');

            try {
                // Check if error filtering is working
                const errorCount = testResults.basic.filter(r => r.type === 'error').length +
                                 testResults.user.filter(r => r.type === 'error').length +
                                 testResults.dashboard.filter(r => r.type === 'error').length;

                log(`📊 إجمالي الأخطاء المسجلة: ${errorCount}`, 'info', 'system');
                
                if (errorCount === 0) {
                    log('✅ لا توجد أخطاء مسجلة', 'success', 'system');
                } else {
                    log(`⚠️ تم العثور على ${errorCount} أخطاء`, 'warning', 'system');
                }
            } catch (error) {
                log(`❌ خطأ في فحص أخطاء وحدة التحكم: ${error.message}`, 'error', 'system');
            }
        }

        async function generateReport() {
            log('📋 إنشاء تقرير شامل...', 'info', 'system');

            try {
                const report = {
                    timestamp: new Date().toISOString(),
                    basicTests: testResults.basic.length,
                    userTests: testResults.user.length,
                    dashboardTests: testResults.dashboard.length,
                    systemTests: testResults.system.length,
                    totalErrors: Object.values(testResults).flat().filter(r => r.type === 'error').length,
                    totalWarnings: Object.values(testResults).flat().filter(r => r.type === 'warning').length,
                    totalSuccess: Object.values(testResults).flat().filter(r => r.type === 'success').length
                };

                log(`📊 تقرير الاختبار:`, 'info', 'system');
                log(`   - إجمالي الاختبارات: ${report.basicTests + report.userTests + report.dashboardTests + report.systemTests}`, 'info', 'system');
                log(`   - النجح: ${report.totalSuccess}`, 'success', 'system');
                log(`   - التحذيرات: ${report.totalWarnings}`, 'warning', 'system');
                log(`   - الأخطاء: ${report.totalErrors}`, 'error', 'system');

                console.log('📋 تقرير الاختبار الشامل:', report);
                log('✅ تم إنشاء التقرير الشامل', 'success', 'system');
            } catch (error) {
                log(`❌ خطأ في إنشاء التقرير: ${error.message}`, 'error', 'system');
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testBasicFixes();
            }, 2000);
        });
    </script>
</body>
</html>
