// Translation system for Mod Etaris app
class TranslationManager {
    constructor() {
        this.currentLanguage = localStorage.getItem('selectedLanguage') || 'en';
        this.translations = {
            ar: {
                // General UI
                'app_name': 'مود إيتاريس',
                'loading': 'جاري التحميل...',
                'error': 'خطأ',
                'success': 'نجح',
                'cancel': 'إلغاء',
                'ok': 'موافق',
                'close': 'إغلاق',
                'back': 'رجوع',
                'next': 'التالي',
                'previous': 'السابق',
                'save': 'حفظ',
                'delete': 'حذف',
                'edit': 'تعديل',
                'add': 'إضافة',
                'search': 'بحث',
                'filter': 'تصفية',
                'sort': 'ترتيب',
                'settings': 'الإعدادات',

                // Categories
                'all': 'الكل',
                'addons': 'الإضافات',
                'shaders': 'الشيدرز',
                'texture_pack': 'حزم النسيج',
                'maps': 'الخرائط',
                'seeds': 'البذور',
                'news': 'الأخبار',
                'suggested': 'مقترح',
                'free_addons': 'إضافات مجانية',

                // Mod details
                'mod_name': 'اسم المود',
                'description': 'الوصف',
                'category': 'الفئة',
                'version': 'الإصدار',
                'size': 'الحجم',
                'downloads': 'التحميلات',
                'likes': 'الإعجابات',
                'creator': 'المطور',
                'download': 'تحميل',
                'like': 'إعجاب',
                'share': 'مشاركة',
                'no_description': 'لا يوجد وصف متاح.',

                // Sorting options
                'most_liked': 'الأكثر إعجاباً',
                'newest': 'الأحدث',
                'most_downloads': 'الأكثر تحميلاً',
                'oldest': 'الأقدم',
                'see_all': 'عرض الكل',

                // Badges
                'new_badge': 'جديد',
                'popular_badge': 'شائع',
                'free_addon_badge': 'إضافة مجانية',

                // Platform
                'platform': 'المنصة',
                'bedrock': 'بيدروك',
                'java': 'جافا',

                // Shader warning
                'shader_warning_title': '⚠️ تحذير مهم',
                'shader_warning_description': 'جميع الشيدرز تعمل فقط مع إصدار ماين كرافت المُعدّل (Patched) وتطبيق BetterRenderDragon. تأكد من تثبيت هذه المتطلبات قبل تحميل أي شيدر.',
                'shader_warning_understand': 'فهمت',
                'shader_warning_dont_show': 'عدم الإظهار مجدداً',
                'shader_warning_translate_ar': 'عربي',
                'shader_warning_translate_en': 'English',

                // Download messages
                'download_starting': 'بدء التحميل...',
                'download_complete': 'اكتمل التحميل',
                'download_failed': 'فشل التحميل',
                'download_invalid_link': 'رابط التحميل غير صالح!',
                'download_unavailable': 'رابط التحميل غير متاح أو غير صالح!',
                'download_error': 'خطأ في بدء التحميل.',
                'download_fallback': 'لا يمكن بدء التحميل التلقائي. سيتم فتح الرابط في المتصفح.',

                // Error messages
                'error_loading_mods': 'خطأ في تحميل المودات',
                'error_updating_like': 'خطأ في تحديث الإعجاب.',
                'error_unexpected_like': 'خطأ غير متوقع في تحديث الإعجاب.',
                'error_modal_data': 'بيانات غير صالحة لعرض التفاصيل.',
                'error_supabase_init': 'خطأ في تهيئة قاعدة البيانات',

                // Language selection
                'choose_language': 'اختر لغتك',
                'language_subtitle': 'اختر لغتك المفضلة',
                'language_change_later': 'يمكنك تغيير اللغة لاحقاً من الإعدادات',
                'change_language': 'تعديل اللغة',
                'language_changed_success': 'تم تغيير اللغة بنجاح! سيتم إعادة تحميل الصفحة...',
                'arabic': 'العربية',
                'english': 'الإنجليزية',

                // Time and dates
                'created_at': 'تاريخ الإنشاء',
                'updated_at': 'تاريخ التحديث',
                'today': 'اليوم',
                'yesterday': 'أمس',
                'days_ago': 'منذ {0} أيام',
                'weeks_ago': 'منذ {0} أسابيع',
                'months_ago': 'منذ {0} أشهر',

                // File operations
                'file_not_found': 'لم يتم العثور على الملف المحمل. ربما تم حذفه؟',
                'file_access_error': 'خطأ في الوصول إلى الملف.',
                'file_name_error': 'لا يمكن تحديد اسم الملف.',

                // Installation
                'installation_guide': 'دليل التثبيت',
                'installation_instructions': 'تعليمات التثبيت',
                'step': 'الخطوة',

                // Premium features
                'premium_required': 'يتطلب اشتراك مميز',
                'get_premium': 'احصل على المميز',
                'premium_benefits': 'مزايا الاشتراك المميز',

                // Creator info
                'creator_info_title': 'معلومات صانع المود',
                'creator_label': 'صانع المود:',
                'social_label': 'وسائل التواصل الاجتماعي:',
                'copyright_title': 'حقوق الطبع والنشر',
                'copyright_desc': 'نحن نشارك اسم صانع المود ووسائل التواصل الاجتماعي الخاصة به كما هي موجودة في المود المنشور على المواقع الأصلية. يمكن لصانع المود المطالبة بإزالة المود من التطبيق عبر التواصل معنا.',
                'contact_title': 'للتواصل معنا:',
                'contact_info': 'البريد الإلكتروني: <EMAIL>',
                'no_creator': 'غير محدد',
                'no_social': 'لا توجد وسائل تواصل متاحة',
                'close_btn': 'إغلاق',

                // Custom copyright
                'custom_copyright_desc': 'هذا النوع من المودات يتطلب من المالك فرض اختصار روابط أو مشاهدة إعلانات، وأرباحها تذهب إلى المالك مباشرة من خلال الإعلانات التي يشاهدها المستخدم. يمكن للمالك طلب إزالة المود من التطبيق عبر التواصل معنا.'
            },
            en: {
                // General UI
                'app_name': 'Mod Etaris',
                'loading': 'Loading...',
                'error': 'Error',
                'success': 'Success',
                'cancel': 'Cancel',
                'ok': 'OK',
                'close': 'Close',
                'back': 'Back',
                'next': 'Next',
                'previous': 'Previous',
                'save': 'Save',
                'delete': 'Delete',
                'edit': 'Edit',
                'add': 'Add',
                'search': 'Search',
                'filter': 'Filter',
                'sort': 'Sort',
                'settings': 'Settings',

                // Categories
                'all': 'All',
                'addons': 'Addons',
                'shaders': 'Shaders',
                'texture_pack': 'Texture Packs',
                'maps': 'Maps',
                'seeds': 'Seeds',
                'news': 'News',
                'suggested': 'Suggested',
                'free_addons': 'Free Addons',

                // Mod details
                'mod_name': 'Mod Name',
                'description': 'Description',
                'category': 'Category',
                'version': 'Version',
                'size': 'Size',
                'downloads': 'Downloads',
                'likes': 'Likes',
                'creator': 'Creator',
                'download': 'Download',
                'like': 'Like',
                'share': 'Share',
                'no_description': 'No description available.',

                // Sorting options
                'most_liked': 'Most Liked',
                'newest': 'Newest',
                'most_downloads': 'Most Downloads',
                'oldest': 'Oldest',
                'see_all': 'See All',

                // Badges
                'new_badge': 'NEW',
                'popular_badge': 'Popular',
                'free_addon_badge': 'Free Addon',

                // Platform
                'platform': 'Platform',
                'bedrock': 'Bedrock',
                'java': 'Java',

                // Shader warning
                'shader_warning_title': '⚠️ Important Warning',
                'shader_warning_description': 'All shaders only work with Minecraft Patched version and BetterRenderDragon app. Make sure to install these requirements before downloading any shader.',
                'shader_warning_understand': 'I Understand',
                'shader_warning_dont_show': 'Don\'t Show Again',
                'shader_warning_translate_ar': 'عربي',
                'shader_warning_translate_en': 'English',

                // Download messages
                'download_starting': 'Starting download...',
                'download_complete': 'Download complete',
                'download_failed': 'Download failed',
                'download_invalid_link': 'Download link is unavailable or invalid!',
                'download_unavailable': 'Download link is unavailable or invalid!',
                'download_error': 'Error starting download.',
                'download_fallback': 'Automatic download cannot be started. The link will be opened in the browser.',

                // Error messages
                'error_loading_mods': 'Error loading mods',
                'error_updating_like': 'Error updating like.',
                'error_unexpected_like': 'Unexpected error updating like.',
                'error_modal_data': 'Invalid item data for modal.',
                'error_supabase_init': 'Error initializing database',

                // Language selection
                'choose_language': 'Choose Your Language',
                'language_subtitle': 'Choose your preferred language',
                'language_change_later': 'You can change the language later from settings',
                'change_language': 'Change Language',
                'language_changed_success': 'Language changed successfully! Page will reload...',
                'arabic': 'Arabic',
                'english': 'English',

                // Time and dates
                'created_at': 'Created At',
                'updated_at': 'Updated At',
                'today': 'Today',
                'yesterday': 'Yesterday',
                'days_ago': '{0} days ago',
                'weeks_ago': '{0} weeks ago',
                'months_ago': '{0} months ago',

                // File operations
                'file_not_found': 'Downloaded file not found. It may have been deleted.',
                'file_access_error': 'Error accessing file.',
                'file_name_error': 'Cannot determine file name.',

                // Installation
                'installation_guide': 'Installation Guide',
                'installation_instructions': 'Installation Instructions',
                'step': 'Step',

                // Premium features
                'premium_required': 'Premium subscription required',
                'get_premium': 'Get Premium',
                'premium_benefits': 'Premium Benefits',

                // Creator info
                'creator_info_title': 'Mod Creator Information',
                'creator_label': 'Mod Creator:',
                'social_label': 'Social Media:',
                'copyright_title': 'Copyright Information',
                'copyright_desc': 'We share the mod creator\'s name and social media links as they appear in the original mod published on official websites. The mod creator can request removal of their mod from our app by contacting us.',
                'contact_title': 'Contact Us:',
                'contact_info': 'Email: <EMAIL>',
                'no_creator': 'Not specified',
                'no_social': 'No social media available',
                'close_btn': 'Close',

                // Custom copyright
                'custom_copyright_desc': 'This type of mod requires the owner to impose link shortening or ad viewing, and the profits go directly to the owner through the ads viewed by the user. The owner can request removal of the mod from the app by contacting us.'
            }
        };
    }

    // Get translation for a key
    t(key, ...args) {
        const translation = this.translations[this.currentLanguage]?.[key] ||
                          this.translations['en'][key] ||
                          key;

        // Replace placeholders {0}, {1}, etc. with arguments
        return translation.replace(/\{(\d+)\}/g, (match, index) => {
            return args[index] !== undefined ? args[index] : match;
        });
    }

    // Set current language
    setLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;
            localStorage.setItem('selectedLanguage', language);

            // Update document direction and language
            if (language === 'ar') {
                document.documentElement.dir = 'rtl';
                document.documentElement.lang = 'ar';
            } else {
                document.documentElement.dir = 'ltr';
                document.documentElement.lang = 'en';
            }

            // Trigger language change event
            window.dispatchEvent(new CustomEvent('languageChanged', {
                detail: { language: language }
            }));
        }
    }

    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Check if current language is RTL
    isRTL() {
        return this.currentLanguage === 'ar';
    }

    // Initialize translation system
    init() {
        const savedLanguage = localStorage.getItem('selectedLanguage');
        if (savedLanguage && this.translations[savedLanguage]) {
            this.setLanguage(savedLanguage);
        }
    }
}

// Create global translation manager instance
window.translationManager = new TranslationManager();

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    window.translationManager.init();
});

// Helper function for easy access to translations
window.t = (key, ...args) => window.translationManager.t(key, ...args);
