// Enhanced Subscription Settings Management
let supabaseClient;
let currentSettings = {};
let campaigns = [];

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeSupabase();
    loadSettings();
    loadCampaigns();
    loadStatistics();
    setupEventListeners();
});

// Initialize Supabase client
function initializeSupabase() {
    const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';

    try {
        supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
        console.log('Supabase client initialized successfully');
    } catch (error) {
        console.error('Error initializing Supabase:', error);
        showMessage('خطأ في الاتصال بقاعدة البيانات', 'error');
    }
}

// Setup event listeners
function setupEventListeners() {
    // Toggle switches
    document.getElementById('enableFloatingIcon').addEventListener('change', function() {
        const details = document.getElementById('floatingIconDetails');
        if (this.checked) {
            details.classList.remove('hidden');
        } else {
            details.classList.add('hidden');
        }
    });

    // Default campaign selection
    document.getElementById('defaultCampaign').addEventListener('change', function() {
        const campaignId = this.value;
        const bannerSettings = document.getElementById('campaignBannerSettings');

        if (campaignId) {
            bannerSettings.style.display = 'block';
            loadCampaignBannerSettings(campaignId);
        } else {
            bannerSettings.style.display = 'none';
        }
    });

    // Save floating icon settings
    document.getElementById('saveFloatingIconSettings').addEventListener('click', saveFloatingIconSettings);

    // Campaign banner management
    document.getElementById('saveCampaignBanner').addEventListener('click', saveCampaignBanner);
    document.getElementById('resetCampaignBannerForm').addEventListener('click', resetCampaignBannerForm);
}

// Load current settings
async function loadSettings() {
    try {
        // Load from localStorage first (fallback)
        const localSettings = {
            showOnEntry: localStorage.getItem('showOnEntry') === 'true',
            showBeforeDownload: localStorage.getItem('showSubscriptionBeforeDownload') === 'true',
            useSubscriptionPage: localStorage.getItem('useSubscriptionPage') === 'true',
            enableBanners: localStorage.getItem('enableSubscriptionBanners') === 'true',
            enableFloatingIcon: localStorage.getItem('enableFloatingIcon') === 'true',
            floatingIconImageUrl: localStorage.getItem('floatingIconImageUrl') || '',
            defaultCampaign: localStorage.getItem('defaultSubscriptionCampaign') || '',
            floatingIconDelay: parseFloat(localStorage.getItem('floatingIconDelay') || '2.5'),
            bannerDelay: parseFloat(localStorage.getItem('bannerDelay') || '3'),
            bannerRotationDuration: parseInt(localStorage.getItem('bannerRotationDuration') || '5'),
            bannerDisplayLimit: parseInt(localStorage.getItem('bannerDisplayLimit') || '0')
        };

        currentSettings = localSettings;
        updateUIFromSettings();

        console.log('Settings loaded successfully');
    } catch (error) {
        console.error('Error loading settings:', error);
        showMessage('خطأ في تحميل الإعدادات', 'error');
    }
}

// Update UI elements from settings
function updateUIFromSettings() {
    document.getElementById('showOnEntry').checked = currentSettings.showOnEntry;
    document.getElementById('showBeforeDownload').checked = currentSettings.showBeforeDownload;
    document.getElementById('useSubscriptionPage').checked = currentSettings.useSubscriptionPage;
    document.getElementById('enableBanners').checked = currentSettings.enableBanners;
    document.getElementById('enableFloatingIcon').checked = currentSettings.enableFloatingIcon;
    document.getElementById('floatingIconImageUrl').value = currentSettings.floatingIconImageUrl;
    document.getElementById('floatingIconDelay').value = currentSettings.floatingIconDelay;
    document.getElementById('bannerDelay').value = currentSettings.bannerDelay;
    document.getElementById('bannerRotationDuration').value = currentSettings.bannerRotationDuration;
    document.getElementById('bannerDisplayLimit').value = currentSettings.bannerDisplayLimit;

    // Show/hide floating icon details
    const floatingIconDetails = document.getElementById('floatingIconDetails');
    if (currentSettings.enableFloatingIcon) {
        floatingIconDetails.classList.remove('hidden');
    } else {
        floatingIconDetails.classList.add('hidden');
    }
}

// Load campaigns for dropdown
async function loadCampaigns() {
    try {
        if (!supabaseClient) {
            console.warn('Supabase client not initialized');
            return;
        }

        const { data, error } = await supabaseClient
            .from('subscription_campaigns')
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error loading campaigns:', error);
            return;
        }

        campaigns = data || [];
        updateCampaignDropdown();

    } catch (error) {
        console.error('Error loading campaigns:', error);
    }
}

// Update campaign dropdown
function updateCampaignDropdown() {
    const dropdown = document.getElementById('defaultCampaign');
    dropdown.innerHTML = '<option value="">اختر حملة...</option>';

    campaigns.forEach(campaign => {
        const option = document.createElement('option');
        option.value = campaign.id;
        option.textContent = campaign.title || `حملة ${campaign.id}`;
        if (campaign.id === currentSettings.defaultCampaign) {
            option.selected = true;
        }
        dropdown.appendChild(option);
    });
}

// Load statistics
async function loadStatistics() {
    try {
        // Active campaigns count
        document.getElementById('activeCampaigns').textContent = campaigns.length;

        // Active subscribers (from localStorage)
        const activeSubscribers = Object.keys(localStorage).filter(key =>
            key.startsWith('subscription_') && localStorage.getItem(key) === 'active'
        ).length;
        document.getElementById('activeSubscribers').textContent = activeSubscribers;

        // Total tasks (estimate)
        const totalTasks = campaigns.reduce((sum, campaign) => {
            try {
                const tasks = JSON.parse(campaign.tasks || '[]');
                return sum + tasks.length;
            } catch {
                return sum;
            }
        }, 0);
        document.getElementById('totalTasks').textContent = totalTasks;

        // Banner views (from localStorage)
        const bannerViews = Object.keys(localStorage).filter(key =>
            key.includes('banner_view_count_')
        ).reduce((sum, key) => {
            return sum + parseInt(localStorage.getItem(key) || '0');
        }, 0);
        document.getElementById('bannerViews').textContent = bannerViews;

    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

// Save floating icon settings
function saveFloatingIconSettings() {
    try {
        const enabled = document.getElementById('enableFloatingIcon').checked;
        const imageUrl = document.getElementById('floatingIconImageUrl').value.trim();

        if (enabled && !imageUrl) {
            showMessage('يرجى إدخال رابط صورة الأيقونة', 'error');
            return;
        }

        localStorage.setItem('enableFloatingIcon', enabled.toString());
        localStorage.setItem('floatingIconImageUrl', imageUrl);

        currentSettings.enableFloatingIcon = enabled;
        currentSettings.floatingIconImageUrl = imageUrl;

        showMessage('تم حفظ إعدادات الأيقونة العائمة بنجاح', 'success');
    } catch (error) {
        console.error('Error saving floating icon settings:', error);
        showMessage('خطأ في حفظ إعدادات الأيقونة العائمة', 'error');
    }
}

// Load campaign banner settings
async function loadCampaignBannerSettings(campaignId) {
    try {
        if (!supabaseClient) return;

        const { data, error } = await supabaseClient
            .from('banner_ads')
            .select('*')
            .eq('campaign_id', campaignId)
            .eq('banner_type', 'subscription')
            .single();

        if (error && error.code !== 'PGRST116') {
            console.error('Error loading campaign banner:', error);
            return;
        }

        if (data) {
            document.getElementById('campaignBannerId').value = data.id;
            document.getElementById('campaignBannerTitle').value = data.title || '';
            document.getElementById('campaignBannerDescription').value = data.description || '';
            document.getElementById('campaignBannerImageUrl').value = data.image_url || '';
            document.getElementById('campaignBannerTargetUrl').value = data.target_url || '';
            document.getElementById('campaignBannerDisplayOrder').value = data.display_order || 1;
            document.getElementById('campaignBannerIsActive').checked = data.is_active || false;
        } else {
            resetCampaignBannerForm();
        }
    } catch (error) {
        console.error('Error loading campaign banner settings:', error);
    }
}

// Save campaign banner
async function saveCampaignBanner() {
    try {
        const campaignId = document.getElementById('defaultCampaign').value;
        if (!campaignId) {
            showMessage('يرجى اختيار حملة أولاً', 'error');
            return;
        }

        const bannerId = document.getElementById('campaignBannerId').value;
        const bannerData = {
            campaign_id: campaignId,
            title: document.getElementById('campaignBannerTitle').value.trim(),
            description: document.getElementById('campaignBannerDescription').value.trim(),
            image_url: document.getElementById('campaignBannerImageUrl').value.trim(),
            target_url: document.getElementById('campaignBannerTargetUrl').value.trim(),
            display_order: parseInt(document.getElementById('campaignBannerDisplayOrder').value) || 1,
            is_active: document.getElementById('campaignBannerIsActive').checked,
            banner_type: 'subscription',
            created_at: new Date().toISOString()
        };

        if (!bannerData.title || !bannerData.image_url) {
            showMessage('يرجى إدخال عنوان البانر ورابط الصورة', 'error');
            return;
        }

        if (!supabaseClient) {
            showMessage('خطأ في الاتصال بقاعدة البيانات', 'error');
            return;
        }

        let result;
        if (bannerId) {
            // Update existing banner
            result = await supabaseClient
                .from('banner_ads')
                .update(bannerData)
                .eq('id', bannerId);
        } else {
            // Create new banner
            result = await supabaseClient
                .from('banner_ads')
                .insert([bannerData]);
        }

        if (result.error) {
            console.error('Error saving campaign banner:', result.error);
            showMessage('خطأ في حفظ بانر الحملة', 'error');
            return;
        }

        showMessage('تم حفظ بانر الحملة بنجاح', 'success');

        // Reload banner settings
        setTimeout(() => {
            loadCampaignBannerSettings(campaignId);
        }, 1000);

    } catch (error) {
        console.error('Error saving campaign banner:', error);
        showMessage('خطأ في حفظ بانر الحملة', 'error');
    }
}

// Reset campaign banner form
function resetCampaignBannerForm() {
    document.getElementById('campaignBannerId').value = '';
    document.getElementById('campaignBannerTitle').value = '';
    document.getElementById('campaignBannerDescription').value = '';
    document.getElementById('campaignBannerImageUrl').value = '';
    document.getElementById('campaignBannerTargetUrl').value = '';
    document.getElementById('campaignBannerDisplayOrder').value = '1';
    document.getElementById('campaignBannerIsActive').checked = true;
}

// Save all settings
function saveSettings() {
    try {
        // Collect all settings from UI
        const settings = {
            showOnEntry: document.getElementById('showOnEntry').checked,
            showBeforeDownload: document.getElementById('showBeforeDownload').checked,
            useSubscriptionPage: document.getElementById('useSubscriptionPage').checked,
            enableBanners: document.getElementById('enableBanners').checked,
            enableFloatingIcon: document.getElementById('enableFloatingIcon').checked,
            floatingIconImageUrl: document.getElementById('floatingIconImageUrl').value.trim(),
            defaultCampaign: document.getElementById('defaultCampaign').value,
            floatingIconDelay: parseFloat(document.getElementById('floatingIconDelay').value),
            bannerDelay: parseFloat(document.getElementById('bannerDelay').value),
            bannerRotationDuration: parseInt(document.getElementById('bannerRotationDuration').value),
            bannerDisplayLimit: parseInt(document.getElementById('bannerDisplayLimit').value)
        };

        // Validate settings
        if (settings.enableFloatingIcon && !settings.floatingIconImageUrl) {
            showMessage('يرجى إدخال رابط صورة الأيقونة العائمة', 'error');
            return;
        }

        // Save to localStorage
        localStorage.setItem('showOnEntry', settings.showOnEntry.toString());
        localStorage.setItem('showSubscriptionBeforeDownload', settings.showBeforeDownload.toString());
        localStorage.setItem('useSubscriptionPage', settings.useSubscriptionPage.toString());
        localStorage.setItem('enableSubscriptionBanners', settings.enableBanners.toString());
        localStorage.setItem('enableFloatingIcon', settings.enableFloatingIcon.toString());
        localStorage.setItem('floatingIconImageUrl', settings.floatingIconImageUrl);
        localStorage.setItem('defaultSubscriptionCampaign', settings.defaultCampaign);
        localStorage.setItem('floatingIconDelay', settings.floatingIconDelay.toString());
        localStorage.setItem('bannerDelay', settings.bannerDelay.toString());
        localStorage.setItem('bannerRotationDuration', settings.bannerRotationDuration.toString());
        localStorage.setItem('bannerDisplayLimit', settings.bannerDisplayLimit.toString());

        currentSettings = settings;
        showMessage('تم حفظ جميع الإعدادات بنجاح', 'success');

    } catch (error) {
        console.error('Error saving settings:', error);
        showMessage('خطأ في حفظ الإعدادات', 'error');
    }
}

// Reset all settings
function resetSettings() {
    if (!confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        return;
    }

    try {
        // Clear localStorage settings
        const settingsKeys = [
            'showOnEntry', 'showSubscriptionBeforeDownload', 'useSubscriptionPage',
            'enableSubscriptionBanners', 'enableFloatingIcon', 'floatingIconImageUrl',
            'defaultSubscriptionCampaign', 'floatingIconDelay', 'bannerDelay',
            'bannerRotationDuration', 'bannerDisplayLimit'
        ];

        settingsKeys.forEach(key => localStorage.removeItem(key));

        // Reset to defaults
        currentSettings = {
            showOnEntry: false,
            showBeforeDownload: false,
            useSubscriptionPage: false,
            enableBanners: false,
            enableFloatingIcon: false,
            floatingIconImageUrl: '',
            defaultCampaign: '',
            floatingIconDelay: 2.5,
            bannerDelay: 3,
            bannerRotationDuration: 5,
            bannerDisplayLimit: 0
        };

        updateUIFromSettings();
        showMessage('تم إعادة تعيين جميع الإعدادات', 'success');

    } catch (error) {
        console.error('Error resetting settings:', error);
        showMessage('خطأ في إعادة تعيين الإعدادات', 'error');
    }
}

// Test subscription page
function testSubscriptionPage() {
    const defaultCampaign = document.getElementById('defaultCampaign').value;

    if (!defaultCampaign) {
        showMessage('يرجى اختيار حملة افتراضية أولاً', 'error');
        return;
    }

    const testUrl = `../subscription-page.html?campaign=${defaultCampaign}&test=true`;
    window.open(testUrl, '_blank');
}

// Clear user data for testing
function clearUserData() {
    if (!confirm('هل أنت متأكد من مسح بيانات المستخدم للاختبار؟ سيتم مسح جميع الاشتراكات والإحصائيات.')) {
        return;
    }

    try {
        // Clear subscription-related data
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (
                key.startsWith('subscription_') ||
                key.startsWith('banner_view_count_') ||
                key.startsWith('campaign_completion_') ||
                key.includes('_task_completed_')
            )) {
                keysToRemove.push(key);
            }
        }

        keysToRemove.forEach(key => localStorage.removeItem(key));

        // Reload statistics
        loadStatistics();

        showMessage(`تم مسح ${keysToRemove.length} عنصر من بيانات المستخدم`, 'success');

    } catch (error) {
        console.error('Error clearing user data:', error);
        showMessage('خطأ في مسح بيانات المستخدم', 'error');
    }
}

// Export settings
function exportSettings() {
    try {
        const exportData = {
            settings: currentSettings,
            campaigns: campaigns,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `subscription-settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showMessage('تم تصدير الإعدادات بنجاح', 'success');

    } catch (error) {
        console.error('Error exporting settings:', error);
        showMessage('خطأ في تصدير الإعدادات', 'error');
    }
}

// Import settings
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importData = JSON.parse(e.target.result);

                if (!importData.settings) {
                    showMessage('ملف الإعدادات غير صالح', 'error');
                    return;
                }

                // Import settings
                currentSettings = importData.settings;

                // Save to localStorage
                Object.keys(currentSettings).forEach(key => {
                    const value = currentSettings[key];
                    if (typeof value === 'boolean') {
                        localStorage.setItem(key === 'showBeforeDownload' ? 'showSubscriptionBeforeDownload' : key, value.toString());
                    } else {
                        localStorage.setItem(key === 'showBeforeDownload' ? 'showSubscriptionBeforeDownload' : key, value.toString());
                    }
                });

                updateUIFromSettings();
                showMessage('تم استيراد الإعدادات بنجاح', 'success');

            } catch (error) {
                console.error('Error importing settings:', error);
                showMessage('خطأ في استيراد الإعدادات', 'error');
            }
        };

        reader.readAsText(file);
    };

    input.click();
}

// Show message
function showMessage(message, type = 'info') {
    const messagesArea = document.getElementById('messagesArea');
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;

    messagesArea.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}