{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:allay", "materials": {"default": "enderman"}, "textures": {"default": "textures/entity/allay/allay"}, "geometry": {"default": "geometry.allay"}, "spawn_egg": {"base_color": "#00daff", "overlay_color": "#00adff"}, "scripts": {"pre_animation": ["variable.holding_trident = query.is_item_name_any('slot.weapon.mainhand', 'minecraft:trident');"]}, "animations": {"idle": "animation.allay.idle", "fly": "animation.allay.fly", "hold_item": "animation.allay.hold_item", "hold_item_fly": "animation.allay.hold_item_fly", "look_at_target_default": "animation.allay.look_at_target.default", "dance": "animation.allay.dance"}, "animation_controllers": [{"controller": "controller.animation.allay.general"}, {"controller": "controller.animation.allay.look_at_target"}, {"controller": "controller.animation.allay.holding"}, {"controller": "controller.animation.allay.dancing"}], "render_controllers": ["controller.render.allay"], "enable_attachables": true, "held_item_ignores_lighting": true}}}