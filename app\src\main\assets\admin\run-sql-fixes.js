// SQL Fixes Runner - تشغيل إصلاحات SQL مباشرة
// يقوم بتشغيل جميع إصلاحات قاعدة البيانات المطلوبة

(function() {
    'use strict';

    console.log('🗄️ SQL Fixes Runner loaded');

    class SQLFixesRunner {
        constructor() {
            this.client = null;
            this.fixesApplied = [];
            this.errors = [];
        }

        async initialize() {
            // Get Supabase client
            this.client = window.supabaseManager?.getMainClient();
            if (!this.client) {
                console.error('❌ Supabase client not available');
                return false;
            }
            return true;
        }

        async runAllSQLFixes() {
            console.log('🚀 Starting SQL fixes...');

            if (!await this.initialize()) {
                return false;
            }

            try {
                // Fix 1: Create error_reports table
                await this.createErrorReportsTable();

                // Fix 2: Fix mods table structure
                await this.fixModsTableStructure();

                // Fix 3: Create missing essential tables
                await this.createEssentialTables();

                // Fix 4: Create RPC functions
                await this.createRPCFunctions();

                // Fix 5: Create indexes for performance
                await this.createIndexes();

                console.log(`✅ SQL fixes completed. Applied ${this.fixesApplied.length} fixes`);
                
                if (this.errors.length > 0) {
                    console.warn(`⚠️ ${this.errors.length} errors occurred`);
                }

                return true;

            } catch (error) {
                console.error('❌ SQL fixes failed:', error);
                this.errors.push(error);
                return false;
            }
        }

        async createErrorReportsTable() {
            try {
                const { error } = await this.client.rpc('execute_sql', {
                    sql_query: `
                        CREATE TABLE IF NOT EXISTS error_reports (
                            id SERIAL PRIMARY KEY,
                            category TEXT,
                            "errorCode" TEXT,
                            "errorMessage" TEXT,
                            timestamp TIMESTAMP DEFAULT NOW(),
                            "userAgent" TEXT,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                        
                        CREATE INDEX IF NOT EXISTS idx_error_reports_category ON error_reports(category);
                        CREATE INDEX IF NOT EXISTS idx_error_reports_timestamp ON error_reports(timestamp);
                    `
                });

                if (error) {
                    console.warn('⚠️ Could not create error_reports table via RPC:', error);
                    
                    // Fallback: try direct insert to create table
                    const { error: insertError } = await this.client
                        .from('error_reports')
                        .insert([{
                            category: 'system',
                            errorCode: 'INIT',
                            errorMessage: 'Table initialization',
                            userAgent: navigator.userAgent
                        }]);

                    if (!insertError) {
                        this.fixesApplied.push('error_reports table created via insert');
                    } else {
                        this.errors.push(insertError);
                    }
                } else {
                    this.fixesApplied.push('error_reports table created');
                }

            } catch (error) {
                console.error('❌ Failed to create error_reports table:', error);
                this.errors.push(error);
            }
        }

        async fixModsTableStructure() {
            try {
                const { error } = await this.client.rpc('execute_sql', {
                    sql_query: `
                        -- Add missing columns to mods table
                        ALTER TABLE mods 
                        ADD COLUMN IF NOT EXISTS description_ar TEXT,
                        ADD COLUMN IF NOT EXISTS image_urls TEXT[],
                        ADD COLUMN IF NOT EXISTS creator_name TEXT,
                        ADD COLUMN IF NOT EXISTS creator_social_media JSONB,
                        ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
                        ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE,
                        ADD COLUMN IF NOT EXISTS backup_download_link TEXT,
                        ADD COLUMN IF NOT EXISTS file_size TEXT,
                        ADD COLUMN IF NOT EXISTS version TEXT,
                        ADD COLUMN IF NOT EXISTS minecraft_version TEXT,
                        ADD COLUMN IF NOT EXISTS tags TEXT[],
                        ADD COLUMN IF NOT EXISTS rating DECIMAL(3,2) DEFAULT 0.0,
                        ADD COLUMN IF NOT EXISTS review_count INTEGER DEFAULT 0,
                        ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP DEFAULT NOW();
                    `
                });

                if (error) {
                    console.warn('⚠️ Could not alter mods table:', error);
                    this.errors.push(error);
                } else {
                    this.fixesApplied.push('mods table structure updated');
                }

            } catch (error) {
                console.error('❌ Failed to fix mods table structure:', error);
                this.errors.push(error);
            }
        }

        async createEssentialTables() {
            const tables = [
                {
                    name: 'user_languages',
                    sql: `
                        CREATE TABLE IF NOT EXISTS user_languages (
                            id SERIAL PRIMARY KEY,
                            user_id TEXT,
                            language TEXT DEFAULT 'ar',
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'banner_ads',
                    sql: `
                        CREATE TABLE IF NOT EXISTS banner_ads (
                            id SERIAL PRIMARY KEY,
                            title TEXT,
                            image_url TEXT,
                            click_url TEXT,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'featured_addons',
                    sql: `
                        CREATE TABLE IF NOT EXISTS featured_addons (
                            id SERIAL PRIMARY KEY,
                            mod_id INTEGER,
                            position INTEGER,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'free_addons',
                    sql: `
                        CREATE TABLE IF NOT EXISTS free_addons (
                            id SERIAL PRIMARY KEY,
                            mod_id INTEGER,
                            position INTEGER,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'suggested_mods',
                    sql: `
                        CREATE TABLE IF NOT EXISTS suggested_mods (
                            id SERIAL PRIMARY KEY,
                            mod_id INTEGER,
                            position INTEGER,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'custom_mod_dialogs',
                    sql: `
                        CREATE TABLE IF NOT EXISTS custom_mod_dialogs (
                            id SERIAL PRIMARY KEY,
                            title TEXT,
                            title_ar TEXT,
                            content TEXT,
                            content_ar TEXT,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'custom_dialog_mods',
                    sql: `
                        CREATE TABLE IF NOT EXISTS custom_dialog_mods (
                            id SERIAL PRIMARY KEY,
                            dialog_id INTEGER,
                            mod_id INTEGER,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'custom_copyright_mods',
                    sql: `
                        CREATE TABLE IF NOT EXISTS custom_copyright_mods (
                            id SERIAL PRIMARY KEY,
                            mod_id INTEGER,
                            copyright_text TEXT,
                            copyright_text_ar TEXT,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'entry_subscription_ads',
                    sql: `
                        CREATE TABLE IF NOT EXISTS entry_subscription_ads (
                            id SERIAL PRIMARY KEY,
                            title TEXT,
                            title_ar TEXT,
                            description TEXT,
                            description_ar TEXT,
                            image_url TEXT,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'custom_sections',
                    sql: `
                        CREATE TABLE IF NOT EXISTS custom_sections (
                            id SERIAL PRIMARY KEY,
                            name TEXT,
                            name_ar TEXT,
                            icon_url TEXT,
                            position INTEGER,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'update_notifications',
                    sql: `
                        CREATE TABLE IF NOT EXISTS update_notifications (
                            id SERIAL PRIMARY KEY,
                            title TEXT,
                            title_ar TEXT,
                            message TEXT,
                            message_ar TEXT,
                            version TEXT,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'app_announcements',
                    sql: `
                        CREATE TABLE IF NOT EXISTS app_announcements (
                            id SERIAL PRIMARY KEY,
                            title TEXT,
                            title_ar TEXT,
                            message TEXT,
                            message_ar TEXT,
                            type TEXT DEFAULT 'info',
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                },
                {
                    name: 'drawer_links',
                    sql: `
                        CREATE TABLE IF NOT EXISTS drawer_links (
                            id SERIAL PRIMARY KEY,
                            title TEXT,
                            title_ar TEXT,
                            url TEXT,
                            icon_url TEXT,
                            position INTEGER,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT NOW()
                        );
                    `
                }
            ];

            for (const table of tables) {
                try {
                    const { error } = await this.client.rpc('execute_sql', {
                        sql_query: table.sql
                    });

                    if (error) {
                        console.warn(`⚠️ Could not create table ${table.name}:`, error);
                        this.errors.push(error);
                    } else {
                        this.fixesApplied.push(`${table.name} table ensured`);
                    }
                } catch (error) {
                    console.error(`❌ Failed to create table ${table.name}:`, error);
                    this.errors.push(error);
                }
            }
        }

        async createRPCFunctions() {
            const functions = [
                {
                    name: 'increment_clicks',
                    sql: `
                        CREATE OR REPLACE FUNCTION increment_clicks(mod_id_in INTEGER)
                        RETURNS VOID AS $$
                        BEGIN
                            UPDATE mods 
                            SET downloads = downloads + 1 
                            WHERE id = mod_id_in;
                        END;
                        $$ LANGUAGE plpgsql;
                    `
                },
                {
                    name: 'increment_likes',
                    sql: `
                        CREATE OR REPLACE FUNCTION increment_likes(mod_id_in INTEGER)
                        RETURNS VOID AS $$
                        BEGIN
                            UPDATE mods 
                            SET likes = likes + 1 
                            WHERE id = mod_id_in;
                        END;
                        $$ LANGUAGE plpgsql;
                    `
                },
                {
                    name: 'execute_sql',
                    sql: `
                        CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
                        RETURNS VOID AS $$
                        BEGIN
                            EXECUTE sql_query;
                        END;
                        $$ LANGUAGE plpgsql;
                    `
                }
            ];

            for (const func of functions) {
                try {
                    const { error } = await this.client.rpc('execute_sql', {
                        sql_query: func.sql
                    });

                    if (error) {
                        console.warn(`⚠️ Could not create function ${func.name}:`, error);
                        this.errors.push(error);
                    } else {
                        this.fixesApplied.push(`${func.name} function created`);
                    }
                } catch (error) {
                    console.error(`❌ Failed to create function ${func.name}:`, error);
                    this.errors.push(error);
                }
            }
        }

        async createIndexes() {
            try {
                const { error } = await this.client.rpc('execute_sql', {
                    sql_query: `
                        -- Create indexes for better performance
                        CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
                        CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at);
                        CREATE INDEX IF NOT EXISTS idx_mods_is_featured ON mods(is_featured);
                        CREATE INDEX IF NOT EXISTS idx_mods_is_popular ON mods(is_popular);
                        CREATE INDEX IF NOT EXISTS idx_mods_downloads ON mods(downloads);
                        CREATE INDEX IF NOT EXISTS idx_mods_likes ON mods(likes);
                    `
                });

                if (error) {
                    console.warn('⚠️ Could not create indexes:', error);
                    this.errors.push(error);
                } else {
                    this.fixesApplied.push('Performance indexes created');
                }

            } catch (error) {
                console.error('❌ Failed to create indexes:', error);
                this.errors.push(error);
            }
        }

        getReport() {
            return {
                fixesApplied: this.fixesApplied,
                errors: this.errors,
                success: this.errors.length === 0
            };
        }
    }

    // Create global instance
    const sqlFixesRunner = new SQLFixesRunner();
    window.sqlFixesRunner = sqlFixesRunner;

    // Auto-run SQL fixes
    setTimeout(() => {
        sqlFixesRunner.runAllSQLFixes().then(success => {
            if (success) {
                console.log('✅ All SQL fixes completed successfully');
            } else {
                console.warn('⚠️ Some SQL fixes failed');
            }
        });
    }, 2000);

    console.log('🗄️ SQL Fixes Runner ready');

})();
