-- Direct Database Fix - إصلاح مباشر لقاعدة البيانات
-- يجب تشغيل هذا السكريبت مباشرة في Supabase SQL Editor

-- 1. إضافة الأعمدة المفقودة إلى جدول mods
ALTER TABLE mods 
ADD COLUMN IF NOT EXISTS description_ar TEXT,
ADD COLUMN IF NOT EXISTS image_urls TEXT[],
ADD COLUMN IF NOT EXISTS creator_name TEXT,
ADD COLUMN IF NOT EXISTS creator_social_media JSONB,
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS backup_download_link TEXT,
ADD COLUMN IF NOT EXISTS file_size TEXT,
ADD COLUMN IF NOT EXISTS version TEXT,
ADD COLUMN IF NOT EXISTS minecraft_version TEXT,
ADD COLUMN IF NOT EXISTS tags TEXT[],
ADD COLUMN IF NOT EXISTS rating DECIMAL(3,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS review_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP DEFAULT NOW();

-- 2. إنشاء جدول error_reports
CREATE TABLE IF NOT EXISTS error_reports (
    id SERIAL PRIMARY KEY,
    category TEXT,
    "errorCode" TEXT,
    "errorMessage" TEXT,
    timestamp TIMESTAMP DEFAULT NOW(),
    "userAgent" TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3. إنشاء الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at);
CREATE INDEX IF NOT EXISTS idx_mods_is_featured ON mods(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_mods_is_popular ON mods(is_popular) WHERE is_popular = true;
CREATE INDEX IF NOT EXISTS idx_mods_downloads ON mods(downloads);
CREATE INDEX IF NOT EXISTS idx_mods_likes ON mods(likes);

CREATE INDEX IF NOT EXISTS idx_error_reports_category ON error_reports(category);
CREATE INDEX IF NOT EXISTS idx_error_reports_timestamp ON error_reports(timestamp);

-- 4. إنشاء أو تحديث دالة execute_sql
CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS TEXT AS $$
BEGIN
    EXECUTE sql_query;
    RETURN 'Success';
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Error: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. إنشاء دوال RPC للتطبيق
CREATE OR REPLACE FUNCTION increment_clicks(mod_id_in INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE mods 
    SET downloads = COALESCE(downloads, 0) + 1 
    WHERE id = mod_id_in;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_likes(mod_id_in INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE mods 
    SET likes = COALESCE(likes, 0) + 1 
    WHERE id = mod_id_in;
END;
$$ LANGUAGE plpgsql;

-- 6. إدراج بيانات تجريبية إذا كان الجدول فارغاً
INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 
    'Sample Addon',
    'This is a sample addon for testing purposes',
    'هذا إضافة تجريبية لأغراض الاختبار',
    '/app/src/main/assets/image/icon_Addons.png',
    'Addons',
    100,
    50,
    false,
    false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Addons' LIMIT 1);

INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 
    'Sample Shader',
    'This is a sample shader for testing purposes',
    'هذا شيدر تجريبي لأغراض الاختبار',
    '/app/src/main/assets/image/icon_shaders.png',
    'Shaders',
    80,
    40,
    false,
    false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Shaders' LIMIT 1);

INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 
    'Sample Texture Pack',
    'This is a sample texture pack for testing purposes',
    'هذه حزمة نسيج تجريبية لأغراض الاختبار',
    '/app/src/main/assets/image/texter.png',
    'Texture',
    120,
    60,
    false,
    false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Texture' LIMIT 1);

INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 
    'Sample Map',
    'This is a sample map for testing purposes',
    'هذه خريطة تجريبية لأغراض الاختبار',
    '/app/src/main/assets/image/icon_Addons.png',
    'Maps',
    90,
    45,
    false,
    false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Maps' LIMIT 1);

INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 
    'Sample Seed',
    'This is a sample seed for testing purposes',
    'هذه بذرة تجريبية لأغراض الاختبار',
    '/app/src/main/assets/image/icon_Addons.png',
    'Seeds',
    70,
    35,
    false,
    false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Seeds' LIMIT 1);

-- 7. إدراج سجل في error_reports للتأكد من وجود الجدول
INSERT INTO error_reports (category, "errorCode", "errorMessage", "userAgent")
VALUES ('system', 'INIT', 'Database initialization completed', 'System')
ON CONFLICT DO NOTHING;

-- 8. منح الصلاحيات اللازمة
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon;

-- 9. تحديث إحصائيات الجداول
ANALYZE mods;
ANALYZE error_reports;

-- 10. رسالة تأكيد
SELECT 
    'Database fix completed successfully!' as status,
    (SELECT COUNT(*) FROM mods) as total_mods,
    (SELECT COUNT(DISTINCT category) FROM mods) as categories_count,
    (SELECT COUNT(*) FROM error_reports) as error_reports_count,
    NOW() as completed_at;
