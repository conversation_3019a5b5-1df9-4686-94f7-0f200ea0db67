// System Control Panel - لوحة التحكم في النظام
// This file creates a control panel for managing the fix systems

class SystemControlPanel {
    constructor() {
        this.isVisible = false;
        this.systemStatus = {
            databaseErrorHandler: false,
            quickFixes: false,
            networkHandler: false,
            backupSystem: false
        };
        this.init();
    }

    init() {
        this.createControlPanel();
        this.attachEventListeners();
        this.updateSystemStatus();
        
        // تحديث الحالة كل 5 ثوان
        setInterval(() => this.updateSystemStatus(), 5000);
        
        console.log('🎛️ System Control Panel initialized');
    }

    createControlPanel() {
        // إنشاء زر التحكم العائم
        const controlButton = document.createElement('div');
        controlButton.id = 'system-control-button';
        controlButton.innerHTML = '⚙️';
        controlButton.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            user-select: none;
        `;

        // تأثيرات التفاعل
        controlButton.addEventListener('mouseenter', () => {
            controlButton.style.transform = 'scale(1.1)';
            controlButton.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.4)';
        });

        controlButton.addEventListener('mouseleave', () => {
            controlButton.style.transform = 'scale(1)';
            controlButton.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.3)';
        });

        document.body.appendChild(controlButton);

        // إنشاء لوحة التحكم
        const controlPanel = document.createElement('div');
        controlPanel.id = 'system-control-panel';
        controlPanel.style.cssText = `
            position: fixed;
            top: 80px;
            left: 20px;
            width: 350px;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            border: 2px solid #667eea;
        `;

        controlPanel.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #667eea;">🎛️ لوحة التحكم</h3>
                <button id="close-control-panel" style="
                    background: none;
                    border: none;
                    color: white;
                    font-size: 20px;
                    cursor: pointer;
                    padding: 5px;
                ">✕</button>
            </div>

            <div style="margin-bottom: 20px;">
                <h4 style="margin: 0 0 10px 0; color: #ffd700;">📊 حالة النظام</h4>
                <div id="system-status-indicators">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h4 style="margin: 0 0 10px 0; color: #ffd700;">🔧 أدوات التحكم</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <button id="start-all-systems" class="control-btn">
                        🚀 تشغيل الكل
                    </button>
                    <button id="restart-systems" class="control-btn">
                        🔄 إعادة تشغيل
                    </button>
                    <button id="run-diagnostics" class="control-btn">
                        🔍 فحص شامل
                    </button>
                    <button id="clear-errors" class="control-btn">
                        🧹 مسح الأخطاء
                    </button>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h4 style="margin: 0 0 10px 0; color: #ffd700;">📈 إحصائيات</h4>
                <div id="system-statistics" style="
                    background: rgba(255, 255, 255, 0.1);
                    padding: 10px;
                    border-radius: 8px;
                    font-size: 12px;
                ">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>

            <div>
                <h4 style="margin: 0 0 10px 0; color: #ffd700;">📝 سجل الأحداث</h4>
                <div id="system-log" style="
                    background: rgba(0, 0, 0, 0.3);
                    padding: 10px;
                    border-radius: 8px;
                    height: 100px;
                    overflow-y: auto;
                    font-size: 11px;
                    font-family: monospace;
                ">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>
        `;

        // إضافة أنماط الأزرار والرسوم المتحركة
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInLeft {
                from {
                    transform: translateX(-100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutLeft {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(-100%);
                    opacity: 0;
                }
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .control-btn {
                background: linear-gradient(45deg, #667eea, #764ba2);
                border: none;
                color: white;
                padding: 8px 12px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 11px;
                transition: all 0.3s ease;
                white-space: nowrap;
            }
            .control-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
                background: linear-gradient(45deg, #764ba2, #667eea);
            }
            .control-btn:active {
                transform: translateY(0);
                animation: pulse 0.3s ease;
            }
            .status-indicator {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 5px 10px;
                margin: 5px 0;
                border-radius: 5px;
                font-size: 12px;
                transition: all 0.3s ease;
            }
            .status-active {
                background: rgba(34, 197, 94, 0.2);
                border-left: 3px solid #22c55e;
            }
            .status-inactive {
                background: rgba(239, 68, 68, 0.2);
                border-left: 3px solid #ef4444;
            }
            .status-indicator:hover {
                background: rgba(255, 255, 255, 0.1);
                transform: translateX(5px);
            }

            #system-control-button {
                animation: pulse 2s infinite;
            }

            #system-control-panel {
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(controlPanel);
    }

    attachEventListeners() {
        // زر فتح/إغلاق اللوحة
        document.getElementById('system-control-button').addEventListener('click', () => {
            this.togglePanel();
        });

        // زر إغلاق اللوحة
        document.getElementById('close-control-panel').addEventListener('click', () => {
            this.hidePanel();
        });

        // أزرار التحكم
        document.getElementById('start-all-systems').addEventListener('click', () => {
            this.startAllSystems();
        });

        document.getElementById('restart-systems').addEventListener('click', () => {
            this.restartSystems();
        });

        document.getElementById('run-diagnostics').addEventListener('click', () => {
            this.runDiagnostics();
        });

        document.getElementById('clear-errors').addEventListener('click', () => {
            this.clearErrors();
        });

        // إغلاق اللوحة عند النقر خارجها
        document.addEventListener('click', (e) => {
            const panel = document.getElementById('system-control-panel');
            const button = document.getElementById('system-control-button');
            
            if (this.isVisible && 
                !panel.contains(e.target) && 
                !button.contains(e.target)) {
                this.hidePanel();
            }
        });
    }

    togglePanel() {
        if (this.isVisible) {
            this.hidePanel();
        } else {
            this.showPanel();
        }
    }

    showPanel() {
        const panel = document.getElementById('system-control-panel');
        if (panel) {
            panel.style.display = 'block';
            panel.style.animation = 'slideInLeft 0.3s ease';
            this.isVisible = true;
            this.updateSystemStatus();
            this.updateStatistics();
            this.addLogEntry('🎛️ لوحة التحكم مفتوحة');
        } else {
            console.warn('⚠️ System control panel element not found');
        }
    }

    hidePanel() {
        const panel = document.getElementById('system-control-panel');
        if (panel) {
            panel.style.animation = 'slideOutLeft 0.3s ease';
            setTimeout(() => {
                if (panel) {
                    panel.style.display = 'none';
                }
            }, 300);
        }
        this.isVisible = false;
    }

    updateSystemStatus() {
        // فحص حالة الأنظمة
        this.systemStatus.databaseErrorHandler = typeof window.databaseErrorHandler !== 'undefined';
        this.systemStatus.quickFixes = typeof window.quickFixesApplied !== 'undefined';
        this.systemStatus.networkHandler = typeof window.networkHandler !== 'undefined';
        this.systemStatus.backupSystem = typeof window.databaseBackupSystem !== 'undefined';

        // تحديث المؤشرات
        const container = document.getElementById('system-status-indicators');
        if (container) {
            container.innerHTML = `
                <div class="status-indicator ${this.systemStatus.databaseErrorHandler ? 'status-active' : 'status-inactive'}">
                    <span>🛠️ معالج أخطاء قاعدة البيانات</span>
                    <span>${this.systemStatus.databaseErrorHandler ? '✅' : '❌'}</span>
                </div>
                <div class="status-indicator ${this.systemStatus.quickFixes ? 'status-active' : 'status-inactive'}">
                    <span>⚡ الإصلاحات السريعة</span>
                    <span>${this.systemStatus.quickFixes ? '✅' : '❌'}</span>
                </div>
                <div class="status-indicator ${this.systemStatus.networkHandler ? 'status-active' : 'status-inactive'}">
                    <span>🌐 معالج الشبكة</span>
                    <span>${this.systemStatus.networkHandler ? '✅' : '❌'}</span>
                </div>
                <div class="status-indicator ${this.systemStatus.backupSystem ? 'status-active' : 'status-inactive'}">
                    <span>💾 نظام النسخ الاحتياطي</span>
                    <span>${this.systemStatus.backupSystem ? '✅' : '❌'}</span>
                </div>
            `;
        }
    }

    updateStatistics() {
        const container = document.getElementById('system-statistics');
        if (container) {
            let stats = {
                totalErrors: 0,
                fixedErrors: 0,
                uptime: 'غير متاح',
                lastCheck: new Date().toLocaleTimeString('ar-SA')
            };

            // جمع الإحصائيات من الأنظمة المختلفة
            if (window.databaseErrorHandler) {
                const errorStats = window.databaseErrorHandler.getErrorStats();
                stats.totalErrors = errorStats.total;
                stats.fixedErrors = errorStats.total - errorStats.recent;
            }

            container.innerHTML = `
                <div>📊 إجمالي الأخطاء: ${stats.totalErrors}</div>
                <div>✅ الأخطاء المحلولة: ${stats.fixedErrors}</div>
                <div>⏰ آخر فحص: ${stats.lastCheck}</div>
                <div>🔄 حالة النظام: ${Object.values(this.systemStatus).every(s => s) ? 'مستقر' : 'يحتاج انتباه'}</div>
            `;
        }
    }

    addLogEntry(message) {
        const logContainer = document.getElementById('system-log');
        if (logContainer) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.style.marginBottom = '2px';
            entry.innerHTML = `<span style="color: #888;">[${timestamp}]</span> ${message}`;
            
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // الاحتفاظ بآخر 50 إدخال فقط
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
    }

    async startAllSystems() {
        this.addLogEntry('🚀 بدء تشغيل جميع الأنظمة...');

        try {
            // تشغيل معالج أخطاء قاعدة البيانات
            if (window.databaseErrorHandler) {
                window.databaseErrorHandler.init();
                this.addLogEntry('✅ تم تشغيل معالج أخطاء قاعدة البيانات');
            }

            // تطبيق الإصلاحات السريعة
            if (typeof applyAllFixes === 'function') {
                applyAllFixes();
                this.addLogEntry('✅ تم تطبيق الإصلاحات السريعة');
            }

            // تشغيل نظام النسخ الاحتياطي
            if (window.databaseBackupSystem) {
                await window.databaseBackupSystem.checkDatabaseHealth();
                this.addLogEntry('✅ تم فحص نظام النسخ الاحتياطي');
            }

            this.addLogEntry('🎉 تم تشغيل جميع الأنظمة بنجاح');
            this.updateSystemStatus();

        } catch (error) {
            this.addLogEntry(`❌ خطأ في تشغيل الأنظمة: ${error.message}`);
        }
    }

    async restartSystems() {
        this.addLogEntry('🔄 إعادة تشغيل الأنظمة...');
        
        // إعادة تحميل الصفحة لإعادة تشغيل كامل
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    async runDiagnostics() {
        this.addLogEntry('🔍 بدء الفحص الشامل...');

        const diagnostics = {
            supabaseConnection: false,
            networkStatus: navigator.onLine,
            localStorageAvailable: false,
            scriptsLoaded: 0
        };

        try {
            // فحص اتصال Supabase
            if (window.supabaseClient) {
                const { error } = await window.supabaseClient
                    .from('mods')
                    .select('count', { count: 'exact', head: true });
                diagnostics.supabaseConnection = !error;
            }

            // فحص localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                diagnostics.localStorageAvailable = true;
            } catch (e) {
                diagnostics.localStorageAvailable = false;
            }

            // عد السكريبتات المحملة
            diagnostics.scriptsLoaded = document.querySelectorAll('script[src]').length;

            // عرض النتائج
            this.addLogEntry(`📊 نتائج الفحص:`);
            this.addLogEntry(`   🔗 اتصال Supabase: ${diagnostics.supabaseConnection ? '✅' : '❌'}`);
            this.addLogEntry(`   🌐 حالة الشبكة: ${diagnostics.networkStatus ? '✅' : '❌'}`);
            this.addLogEntry(`   💾 التخزين المحلي: ${diagnostics.localStorageAvailable ? '✅' : '❌'}`);
            this.addLogEntry(`   📜 السكريبتات المحملة: ${diagnostics.scriptsLoaded}`);

        } catch (error) {
            this.addLogEntry(`❌ خطأ في الفحص: ${error.message}`);
        }
    }

    clearErrors() {
        this.addLogEntry('🧹 مسح سجلات الأخطاء...');

        try {
            // مسح سجل أخطاء قاعدة البيانات
            if (window.databaseErrorHandler) {
                window.databaseErrorHandler.errorLog = [];
                window.databaseErrorHandler.saveErrorLog();
            }

            // مسح سجلات أخرى
            localStorage.removeItem('databaseErrorLog');
            localStorage.removeItem('networkErrors');

            // مسح سجل اللوحة
            const logContainer = document.getElementById('system-log');
            if (logContainer) {
                logContainer.innerHTML = '';
            }

            this.addLogEntry('✅ تم مسح جميع سجلات الأخطاء');
            this.updateStatistics();

        } catch (error) {
            this.addLogEntry(`❌ خطأ في مسح السجلات: ${error.message}`);
        }
    }
}

// إنشاء وتشغيل لوحة التحكم
const systemControlPanel = new SystemControlPanel();

// جعلها متاحة عالمياً
window.systemControlPanel = systemControlPanel;

console.log('🎛️ System Control Panel loaded and ready');
