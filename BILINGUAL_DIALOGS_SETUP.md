# 🌍 نظام المربعات المخصصة ثنائية اللغة
# Bilingual Custom Dialogs System

## 📋 نظرة عامة / Overview

تم إنشاء نظام متكامل للمربعات المخصصة يدعم اللغتين العربية والإنجليزية، ويتكامل مع نظام اختيار اللغة الموجود في التطبيق.

A comprehensive bilingual custom dialogs system has been created that supports Arabic and English languages and integrates with the existing language selection system.

## 🔧 الخطوات المطلوبة / Required Steps

### الخطوة 1: تحديث قاعدة البيانات / Step 1: Update Database

نفذ هذا الكود في Supabase SQL Editor:

```sql
-- إضافة الحقول الإنجليزية
ALTER TABLE custom_mod_dialogs 
ADD COLUMN IF NOT EXISTS title_en VARCHAR(255),
ADD COLUMN IF NOT EXISTS description_en TEXT,
ADD COLUMN IF NOT EXISTS button_text_en VARCHAR(100) DEFAULT 'OK';

-- إضافة تعليقات توضيحية
COMMENT ON COLUMN custom_mod_dialogs.title IS 'Arabic title';
COMMENT ON COLUMN custom_mod_dialogs.description IS 'Arabic description';
COMMENT ON COLUMN custom_mod_dialogs.button_text IS 'Arabic button text';
COMMENT ON COLUMN custom_mod_dialogs.title_en IS 'English title';
COMMENT ON COLUMN custom_mod_dialogs.description_en IS 'English description';
COMMENT ON COLUMN custom_mod_dialogs.button_text_en IS 'English button text';

-- إنشاء فهارس للبحث
CREATE INDEX IF NOT EXISTS idx_custom_mod_dialogs_title_en ON custom_mod_dialogs USING gin(to_tsvector('english', title_en));
CREATE INDEX IF NOT EXISTS idx_custom_mod_dialogs_description_en ON custom_mod_dialogs USING gin(to_tsvector('english', description_en));
```

### الخطوة 2: إضافة الملفات للتطبيق / Step 2: Add Files to App

أضف هذا السطر في `index.html` بعد تحميل `translations.js`:

```html
<script src="bilingual-dialogs.js"></script>
```

### الخطوة 3: اختبار النظام / Step 3: Test System

افتح `test-bilingual-dialogs.html` لاختبار النظام.

## 🎯 كيف يعمل النظام / How the System Works

### 1. تحديد اللغة / Language Detection
- يستخدم `localStorage.getItem('selectedLanguage')` (نفس المفتاح المستخدم في نظام اللغة الموجود)
- يتكامل مع `TranslationManager`
- يراقب تغييرات اللغة تلقائياً

### 2. عرض المحتوى / Content Display
```javascript
// للمستخدمين العرب
if (userLanguage === 'ar') {
    title = dialog.title || dialog.title_en || 'عنوان غير محدد';
    description = dialog.description || dialog.description_en || '';
    buttonText = dialog.button_text || dialog.button_text_en || 'تم';
}

// للمستخدمين الإنجليز
else {
    title = dialog.title_en || dialog.title || 'No title';
    description = dialog.description_en || dialog.description || '';
    buttonText = dialog.button_text_en || dialog.button_text || 'OK';
}
```

### 3. التكامل مع نظام اللغة الموجود / Integration with Existing Language System
- يراقب حدث `languageChanged` من `TranslationManager`
- يراقب تغييرات `localStorage` للمفتاح `selectedLanguage`
- يعيد تعيين المربعات المعروضة عند تغيير اللغة

## 📱 الاستخدام / Usage

### في التطبيق الرئيسي / In Main App
```javascript
// عرض المربعات المرتبطة بمود معين
showModDialogs('mod-uuid-here');

// أو استخدام المدير مباشرة
window.bilingualDialogManager.showModDialogs('mod-uuid-here');
```

### في صفحة الإدارة / In Admin Page
1. افتح `admin/custom_dialogs.html`
2. أنشئ مربع جديد مع محتوى عربي وإنجليزي
3. اربط المربع بالمودات المطلوبة في `admin/dialog_mods.html`

### دوال الاختبار / Test Functions
```javascript
// اختبار مربع بلغة محددة
testBilingualDialog('ar');  // عربي
testBilingualDialog('en');  // إنجليزي
testBilingualDialog();      // تلقائي حسب لغة المستخدم

// الحصول على اللغة الحالية
getCurrentDialogLanguage();

// تغيير اللغة يدوياً
setDialogLanguage('ar');

// إعادة تعيين المربعات المعروضة
resetShownDialogs();
```

## 🔄 سيناريو الاستخدام الكامل / Complete Usage Scenario

### 1. المستخدم الجديد / New User
1. يفتح التطبيق لأول مرة
2. تظهر صفحة اختيار اللغة (`language-selection.html`)
3. يختار العربية أو الإنجليزية
4. يتم حفظ الاختيار في `localStorage` و `user_languages` table

### 2. عرض المربعات / Showing Dialogs
1. المستخدم يفتح تفاصيل مود
2. النظام يتحقق من وجود مربعات مخصصة للمود
3. يعرض المربعات باللغة المحددة من المستخدم
4. يحترم إعداد "عدم الإظهار مرة أخرى"

### 3. تغيير اللغة / Language Change
1. المستخدم يغير اللغة من الإعدادات
2. النظام يحدث `localStorage` و `TranslationManager`
3. نظام المربعات يتلقى إشعار التغيير
4. يعيد تعيين المربعات المعروضة للسماح بعرضها باللغة الجديدة

## 🎨 تخصيص التصميم / Design Customization

### الألوان والأنماط / Colors and Styles
يمكن تخصيص مظهر المربعات من خلال تعديل CSS في `bilingual-dialogs.js`:

```css
.custom-dialog-modal {
    background: rgba(0, 0, 0, 0.7); /* خلفية شفافة */
}

.custom-dialog-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dialog-ok-button {
    background: linear-gradient(45deg, #3498db, #2980b9);
}
```

### الاتجاه والخط / Direction and Font
- يدعم RTL للعربية و LTR للإنجليزية تلقائياً
- يمكن إضافة خطوط مخصصة للغات المختلفة

## 🐛 استكشاف الأخطاء / Troubleshooting

### المربعات لا تظهر / Dialogs Not Showing
1. تحقق من وجود الجداول في قاعدة البيانات
2. تحقق من ربط المربعات بالمودات في `custom_dialog_mods`
3. تحقق من أن `is_active = true` للمربعات

### اللغة خاطئة / Wrong Language
1. تحقق من `localStorage.getItem('selectedLanguage')`
2. تحقق من عمل `TranslationManager`
3. استخدم `setDialogLanguage('ar')` للتغيير اليدوي

### المربعات تظهر مرة واحدة فقط / Dialogs Show Only Once
- هذا سلوك طبيعي لمنع الإزعاج
- استخدم `resetShownDialogs()` لإعادة تعيين العداد
- أو امسح `localStorage` للمربعات المحددة

## 📊 مراقبة الأداء / Performance Monitoring

### في Console
```javascript
// مراقبة الرسائل التفصيلية
console.log('🌍 User language from localStorage: ar');
console.log('🎯 Showing dialogs for mod: mod-123 in language: ar');
console.log('📋 Found 2 dialogs for mod mod-123');
console.log('📱 Showing dialog: مرحبا بك (ar)');
console.log('✅ Dialog 1 result: {"action":"ok"}');
```

### في صفحة الاختبار
افتح `test-bilingual-dialogs.html` لمراقبة شاملة للنظام.

## 🔮 التطوير المستقبلي / Future Development

### ميزات مقترحة / Suggested Features
1. دعم لغات إضافية (فرنسية، إسبانية، إلخ)
2. قوالب مربعات جاهزة
3. إحصائيات تفاعل المستخدمين مع المربعات
4. جدولة عرض المربعات
5. مربعات تفاعلية مع أزرار متعددة

### تحسينات الأداء / Performance Improvements
1. تخزين مؤقت للمربعات
2. تحميل كسول للصور
3. ضغط البيانات المنقولة
4. تحسين استعلامات قاعدة البيانات
