# 🌍 تحديث المربعات المخصصة ثنائية اللغة
# Bilingual Custom Dialogs Update

## ✅ التحديثات المنجزة / Completed Updates

### 1. تحديث دالة `showCustomDialog`
تم تحديث دالة عرض المربع المخصص لتدعم اللغتين العربية والإنجليزية:

**التحسينات المضافة:**
- ✅ الحصول على لغة المستخدم من `localStorage.getItem('selectedLanguage')`
- ✅ عرض المحتوى بناءً على اللغة المختارة (عربي/إنجليزي)
- ✅ دعم اتجاه النص (RTL للعربية، LTR للإنجليزية)
- ✅ ترجمة نص "عدم الظهور مجدداً" حسب اللغة
- ✅ تموضع زر الإغلاق حسب اتجاه اللغة

### 2. تحديث دالة `checkCustomDialog`
تم تحديث دالة فحص المربعات المخصصة لجلب الحقول ثنائية اللغة:

**الحقول المضافة:**
- ✅ `title_en` - العنوان بالإنجليزية
- ✅ `description_en` - الوصف بالإنجليزية  
- ✅ `button_text_en` - نص الزر بالإنجليزية

### 3. منطق اختيار اللغة
```javascript
// للمستخدمين العرب
if (isArabic) {
    dialogTitle = dialog.title || dialog.title_en || 'عنوان غير محدد';
    dialogDescription = dialog.description || dialog.description_en || '';
    dialogButtonText = dialog.button_text || dialog.button_text_en || 'تم';
    dontShowAgainText = 'عدم الظهور مجدداً';
}

// للمستخدمين الإنجليز
else {
    dialogTitle = dialog.title_en || dialog.title || 'No title specified';
    dialogDescription = dialog.description_en || dialog.description || '';
    dialogButtonText = dialog.button_text_en || dialog.button_text || 'OK';
    dontShowAgainText = "Don't show again";
}
```

## 🔧 متطلبات قاعدة البيانات / Database Requirements

تأكد من تنفيذ هذا الكود في Supabase SQL Editor:

```sql
-- إضافة الحقول الإنجليزية لجدول custom_mod_dialogs
ALTER TABLE custom_mod_dialogs 
ADD COLUMN IF NOT EXISTS title_en VARCHAR(255),
ADD COLUMN IF NOT EXISTS description_en TEXT,
ADD COLUMN IF NOT EXISTS button_text_en VARCHAR(100) DEFAULT 'OK';
```

## 🧪 اختبار النظام / Testing the System

### 1. ملف الاختبار
تم إنشاء ملف اختبار: `test-bilingual-custom-dialogs.html`

**المميزات:**
- ✅ اختبار تغيير اللغة
- ✅ معاينة المحتوى حسب اللغة
- ✅ اختبار عرض المربع المخصص
- ✅ فحص حالة النظام

### 2. كيفية الاختبار
1. افتح ملف `test-bilingual-custom-dialogs.html` في المتصفح
2. اختر اللغة (عربي/إنجليزي)
3. انقر على "عرض مربع تجريبي"
4. تحقق من ظهور المحتوى باللغة الصحيحة

## 📱 كيفية عمل النظام في التطبيق / How it Works in the App

### 1. عند النقر على مود
```javascript
// في دالة showModal
checkCustomDialog(item, (confirmedItem) => {
    // يتم فحص وجود مربع مخصص للمود
    // إذا وُجد، يتم عرضه بناءً على لغة المستخدم
    displayModalContent(confirmedItem);
});
```

### 2. تحديد اللغة
```javascript
const userLanguage = localStorage.getItem('selectedLanguage') || 'en';
const isArabic = userLanguage === 'ar';
```

### 3. عرض المحتوى
- **للمستخدمين العرب:** يتم عرض `title`, `description`, `button_text`
- **للمستخدمين الإنجليز:** يتم عرض `title_en`, `description_en`, `button_text_en`
- **في حالة عدم وجود ترجمة:** يتم استخدام النص الافتراضي

## 🎯 النتيجة المتوقعة / Expected Result

### عند اختيار اللغة العربية:
- ✅ المربع يظهر باتجاه RTL
- ✅ العنوان والوصف بالعربية
- ✅ نص الزر "تم"
- ✅ "عدم الظهور مجدداً"
- ✅ زر الإغلاق في الجهة اليسرى

### عند اختيار اللغة الإنجليزية:
- ✅ المربع يظهر باتجاه LTR
- ✅ العنوان والوصف بالإنجليزية
- ✅ نص الزر "OK"
- ✅ "Don't show again"
- ✅ زر الإغلاق في الجهة اليمنى

## 🔍 استكشاف الأخطاء / Troubleshooting

### إذا لم يظهر المربع باللغة الصحيحة:
1. تحقق من `localStorage.getItem('selectedLanguage')`
2. تأكد من وجود الحقول `title_en`, `description_en`, `button_text_en` في قاعدة البيانات
3. تحقق من console للأخطاء

### إذا لم يظهر المربع نهائياً:
1. تحقق من وجود جدول `custom_mod_dialogs` في قاعدة البيانات
2. تأكد من وجود بيانات في جدول `custom_dialog_mods`
3. تحقق من أن `is_active = true` للمربع

## 📝 ملاحظات مهمة / Important Notes

1. **التوافق مع النظام الموجود:** التحديث متوافق تماماً مع نظام اختيار اللغة الموجود
2. **الأمان:** يتم استخدام `escapeHtml()` لحماية النصوص من XSS
3. **الأداء:** لا يؤثر التحديث على أداء التطبيق
4. **المرونة:** يمكن إضافة لغات أخرى بسهولة في المستقبل

## 🚀 الخطوات التالية / Next Steps

1. ✅ اختبار النظام في بيئة التطوير
2. ⏳ إضافة مربعات مخصصة جديدة من لوحة الإدارة
3. ⏳ اختبار النظام مع مودات حقيقية
4. ⏳ نشر التحديث في الإنتاج

---

**تم إنجاز التحديث بنجاح! 🎉**
**Update completed successfully! 🎉**
