-- ========================================
-- تحديث الجداول الموجودة للنظام المحسن
-- Update Existing Tables for Enhanced System
-- ========================================

-- 1. تحديث جدول banner_ads لدعم حملات الاشتراك المجاني
-- Update banner_ads table to support subscription campaigns
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS campaign_id UUID REFERENCES free_subscription_campaigns(id);
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS banner_type VARCHAR(20) DEFAULT 'regular'; -- regular, subscription
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS image_width INTEGER;
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS image_height INTEGER;

-- إضافة فهرس للأداء
CREATE INDEX IF NOT EXISTS idx_banner_ads_campaign_id ON banner_ads(campaign_id);
CREATE INDEX IF NOT EXISTS idx_banner_ads_banner_type ON banner_ads(banner_type);

-- 2. تحديث جدول mods لدعم الاشتراك المجاني
-- Update mods table to support free subscription
ALTER TABLE mods ADD COLUMN IF NOT EXISTS requires_subscription BOOLEAN DEFAULT false;
ALTER TABLE mods ADD COLUMN IF NOT EXISTS subscription_campaign_id UUID REFERENCES free_subscription_campaigns(id);

-- إضافة فهرس للأداء
CREATE INDEX IF NOT EXISTS idx_mods_requires_subscription ON mods(requires_subscription);
CREATE INDEX IF NOT EXISTS idx_mods_subscription_campaign_id ON mods(subscription_campaign_id);

-- 3. تحديث جدول user_languages لدعم معلومات الاشتراك
-- Update user_languages table to support subscription info
ALTER TABLE user_languages ADD COLUMN IF NOT EXISTS has_active_subscription BOOLEAN DEFAULT false;
ALTER TABLE user_languages ADD COLUMN IF NOT EXISTS subscription_expires_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE user_languages ADD COLUMN IF NOT EXISTS last_subscription_check TIMESTAMP WITH TIME ZONE;

-- إضافة فهرس للأداء
CREATE INDEX IF NOT EXISTS idx_user_languages_has_subscription ON user_languages(has_active_subscription);
CREATE INDEX IF NOT EXISTS idx_user_languages_subscription_expires ON user_languages(subscription_expires_at);

-- 4. إنشاء جدول user_subscription_status (إذا لم يكن موجوداً)
-- Create user_subscription_status table if not exists
CREATE TABLE IF NOT EXISTS user_subscription_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL UNIQUE,
    has_active_subscription BOOLEAN DEFAULT false,
    current_campaign_id UUID REFERENCES free_subscription_campaigns(id),
    subscription_started_at TIMESTAMP WITH TIME ZONE,
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    total_subscriptions_count INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_subscription_status_user_id ON user_subscription_status(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscription_status_active ON user_subscription_status(has_active_subscription);
CREATE INDEX IF NOT EXISTS idx_user_subscription_status_expires ON user_subscription_status(subscription_expires_at);

-- 5. تحديث جدول task_types الموجود (إذا كان موجوداً)
-- Update existing task_types table if exists
DO $$
BEGIN
    -- التحقق من وجود الجدول
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'task_types') THEN
        -- إضافة الأعمدة الجديدة إذا لم تكن موجودة
        ALTER TABLE task_types ADD COLUMN IF NOT EXISTS verification_method VARCHAR(50) DEFAULT 'smart';
        ALTER TABLE task_types ADD COLUMN IF NOT EXISTS api_endpoint TEXT;
        
        -- تحديث البيانات الموجودة لتدعم المنصات الثلاث فقط
        UPDATE task_types SET is_active = false 
        WHERE name NOT IN ('youtube_subscribe', 'telegram_subscribe', 'discord_join');
        
        -- إضافة المنصات الثلاث إذا لم تكن موجودة
        INSERT INTO task_types (name, display_name_ar, display_name_en, icon, verification_method) VALUES
        ('youtube_subscribe', 'اشتراك في قناة يوتيوب', 'Subscribe to YouTube Channel', 'fab fa-youtube', 'smart'),
        ('telegram_subscribe', 'اشتراك في قناة تيليجرام', 'Subscribe to Telegram Channel', 'fab fa-telegram', 'smart'),
        ('discord_join', 'انضمام لخادم ديسكورد', 'Join Discord Server', 'fab fa-discord', 'smart')
        ON CONFLICT (name) DO UPDATE SET
            verification_method = EXCLUDED.verification_method,
            is_active = true;
    END IF;
END $$;

-- 6. تحديث جدول campaign_tasks الموجود (إذا كان موجوداً)
-- Update existing campaign_tasks table if exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'campaign_tasks') THEN
        -- إضافة الأعمدة الجديدة
        ALTER TABLE campaign_tasks ADD COLUMN IF NOT EXISTS target_id VARCHAR(200);
        ALTER TABLE campaign_tasks ADD COLUMN IF NOT EXISTS verification_config JSONB DEFAULT '{}';
        ALTER TABLE campaign_tasks ADD COLUMN IF NOT EXISTS retry_attempts INTEGER DEFAULT 3;
        ALTER TABLE campaign_tasks ADD COLUMN IF NOT EXISTS verification_delay_seconds INTEGER DEFAULT 30;
        
        -- تحديث طريقة التحقق للمهام الموجودة
        UPDATE campaign_tasks SET verification_method = 'smart' 
        WHERE task_type IN ('youtube_subscribe', 'telegram_subscribe', 'discord_join');
    END IF;
END $$;

-- 7. تحديث جدول user_task_progress الموجود (إذا كان موجوداً)
-- Update existing user_task_progress table if exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_task_progress') THEN
        -- إضافة الأعمدة الجديدة
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS verification_attempts INTEGER DEFAULT 0;
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS verification_score INTEGER DEFAULT 0;
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP WITH TIME ZONE;
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS error_message TEXT;
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
        
        -- تحديث الحالات الموجودة
        UPDATE user_task_progress 
        SET status = 'verified', verification_score = 100 
        WHERE status = 'completed';
    END IF;
END $$;

-- 8. تحديث جدول user_subscriptions الموجود (إذا كان موجوداً)
-- Update existing user_subscriptions table if exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_subscriptions') THEN
        -- إضافة الأعمدة الجديدة
        ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS verification_score INTEGER DEFAULT 0;
        ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS last_verified_at TIMESTAMP WITH TIME ZONE;
        ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    END IF;
END $$;

-- 9. تحديث جدول free_subscription_campaigns الموجود (إذا كان موجوداً)
-- Update existing free_subscription_campaigns table if exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'free_subscription_campaigns') THEN
        -- إضافة الأعمدة الجديدة
        ALTER TABLE free_subscription_campaigns ADD COLUMN IF NOT EXISTS verification_strictness VARCHAR(20) DEFAULT 'medium';
        ALTER TABLE free_subscription_campaigns ADD COLUMN IF NOT EXISTS auto_verify_enabled BOOLEAN DEFAULT true;
        ALTER TABLE free_subscription_campaigns ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    END IF;
END $$;

-- 10. إنشاء دالة لتحديث حالة الاشتراك للمستخدم
-- Create function to update user subscription status
CREATE OR REPLACE FUNCTION update_user_subscription_status(
    p_user_id VARCHAR(100),
    p_campaign_id UUID,
    p_expires_at TIMESTAMP WITH TIME ZONE
)
RETURNS VOID AS $$
BEGIN
    -- تحديث جدول user_subscription_status
    INSERT INTO user_subscription_status (
        user_id, has_active_subscription, current_campaign_id,
        subscription_started_at, subscription_expires_at,
        total_subscriptions_count, last_activity_at
    ) VALUES (
        p_user_id, true, p_campaign_id,
        CURRENT_TIMESTAMP, p_expires_at,
        1, CURRENT_TIMESTAMP
    )
    ON CONFLICT (user_id) DO UPDATE SET
        has_active_subscription = true,
        current_campaign_id = p_campaign_id,
        subscription_started_at = CURRENT_TIMESTAMP,
        subscription_expires_at = p_expires_at,
        total_subscriptions_count = user_subscription_status.total_subscriptions_count + 1,
        last_activity_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP;
    
    -- تحديث جدول user_languages إذا كان موجوداً
    UPDATE user_languages 
    SET 
        has_active_subscription = true,
        subscription_expires_at = p_expires_at,
        last_subscription_check = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- 11. إنشاء دالة للتحقق من انتهاء الاشتراكات
-- Create function to check expired subscriptions
CREATE OR REPLACE FUNCTION check_expired_subscriptions()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER := 0;
BEGIN
    -- تحديث الاشتراكات المنتهية في user_subscriptions
    UPDATE user_subscriptions 
    SET status = 'expired', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active' AND expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    
    -- تحديث حالة المستخدمين في user_subscription_status
    UPDATE user_subscription_status 
    SET 
        has_active_subscription = false,
        current_campaign_id = NULL,
        last_activity_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE has_active_subscription = true 
      AND subscription_expires_at < CURRENT_TIMESTAMP;
    
    -- تحديث user_languages
    UPDATE user_languages 
    SET 
        has_active_subscription = false,
        last_subscription_check = CURRENT_TIMESTAMP
    WHERE has_active_subscription = true 
      AND subscription_expires_at < CURRENT_TIMESTAMP;
    
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- 12. إنشاء دالة للتحقق من حالة اشتراك المستخدم
-- Create function to check user subscription status
CREATE OR REPLACE FUNCTION get_user_subscription_status(p_user_id VARCHAR(100))
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    subscription_record RECORD;
BEGIN
    -- البحث عن الاشتراك النشط
    SELECT 
        us.status,
        us.expires_at,
        us.verification_score,
        fsc.title_ar,
        fsc.title_en,
        fsc.subscription_duration_days
    INTO subscription_record
    FROM user_subscriptions us
    JOIN free_subscription_campaigns fsc ON us.campaign_id = fsc.id
    WHERE us.user_id = p_user_id 
      AND us.status = 'active'
      AND us.expires_at > CURRENT_TIMESTAMP
    ORDER BY us.expires_at DESC
    LIMIT 1;
    
    IF FOUND THEN
        result := jsonb_build_object(
            'has_subscription', true,
            'status', subscription_record.status,
            'expires_at', subscription_record.expires_at,
            'verification_score', subscription_record.verification_score,
            'campaign_title_ar', subscription_record.title_ar,
            'campaign_title_en', subscription_record.title_en,
            'duration_days', subscription_record.subscription_duration_days,
            'days_remaining', EXTRACT(DAY FROM subscription_record.expires_at - CURRENT_TIMESTAMP)
        );
    ELSE
        result := jsonb_build_object(
            'has_subscription', false,
            'status', 'none',
            'message', 'No active subscription found'
        );
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 13. منح الصلاحيات للدوال الجديدة
-- Grant permissions for new functions
GRANT EXECUTE ON FUNCTION update_user_subscription_status(VARCHAR, UUID, TIMESTAMP WITH TIME ZONE) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION check_expired_subscriptions() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_user_subscription_status(VARCHAR) TO anon, authenticated;

-- 14. إنشاء trigger لتحديث updated_at تلقائياً
-- Create triggers for automatic updated_at
CREATE TRIGGER update_user_subscription_status_updated_at
    BEFORE UPDATE ON user_subscription_status
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 15. إضافة سياسات RLS للجداول الجديدة
-- Add RLS policies for new tables
ALTER TABLE user_subscription_status ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow users to read their own subscription status"
    ON user_subscription_status FOR SELECT
    USING (true);

CREATE POLICY "Allow users to insert their own subscription status"
    ON user_subscription_status FOR INSERT
    WITH CHECK (true);

CREATE POLICY "Allow users to update their own subscription status"
    ON user_subscription_status FOR UPDATE
    USING (true);

-- 16. تنظيف البيانات القديمة (اختياري)
-- Clean up old data (optional)
-- حذف أنواع المهام غير المدعومة
DELETE FROM task_types 
WHERE name NOT IN ('youtube_subscribe', 'telegram_subscribe', 'discord_join')
  AND is_active = false;

-- تحديث المهام الموجودة لتستخدم المنصات المدعومة فقط
UPDATE campaign_tasks 
SET task_type = 'youtube_subscribe' 
WHERE task_type IN ('youtube_follow', 'youtube_like');

UPDATE campaign_tasks 
SET task_type = 'telegram_subscribe' 
WHERE task_type IN ('telegram_join', 'telegram_follow');

-- 17. إضافة فهارس إضافية للأداء
-- Add additional indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_status ON user_subscriptions(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_expires_status ON user_subscriptions(expires_at, status);
CREATE INDEX IF NOT EXISTS idx_campaign_tasks_type_campaign ON campaign_tasks(task_type, campaign_id);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_user_status ON user_task_progress(user_id, status);

-- 18. إضافة تعليقات للجداول والأعمدة
-- Add comments for tables and columns
COMMENT ON TABLE user_subscription_status IS 'جدول حالة اشتراك المستخدمين - User subscription status table';
COMMENT ON COLUMN user_subscription_status.verification_score IS 'نقاط التحقق من صحة الاشتراك';
COMMENT ON COLUMN campaign_tasks.target_id IS 'معرف الهدف (معرف القناة أو الخادم)';
COMMENT ON COLUMN campaign_tasks.verification_config IS 'إعدادات التحقق المخصصة';

-- انتهاء التحديثات
SELECT 'تم تحديث الجداول الموجودة بنجاح!' as message;
