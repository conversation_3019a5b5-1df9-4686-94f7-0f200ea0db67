<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار تكامل إعدادات المستخدم</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            transition: all 0.3s ease;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            border: 2px solid rgba(255, 215, 0, 0.3);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        
        button {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        
        .setting-demo {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            border: 1px solid rgba(255, 215, 0, 0.1);
        }
        
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #ffd700;
            border-radius: 50%;
            animation: float 8s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>
    
    <div class="test-container">
        <h1 style="text-align: center; color: #ffd700; margin-bottom: 30px;">
            🧪 اختبار تكامل إعدادات المستخدم
        </h1>
        
        <div id="test-results"></div>
        
        <div class="test-section">
            <h3 style="color: #ffd700;">🎮 اختبارات سريعة للإعدادات</h3>
            <button onclick="testDarkMode()">🌙 اختبار الوضع الليلي</button>
            <button onclick="testVisualEffects()">✨ اختبار التأثيرات البصرية</button>
            <button onclick="testLanguage()">🌍 اختبار اللغة</button>
            <button onclick="testBackgroundOpacity()">🎨 اختبار الشفافية</button>
            <button onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
        </div>
        
        <div class="test-section">
            <h3 style="color: #ffd700;">⚙️ تحكم مباشر في الإعدادات</h3>
            <div class="setting-demo">
                <label>الوضع الليلي:</label>
                <input type="checkbox" id="darkModeToggle" onchange="toggleDarkMode(this.checked)">
            </div>
            <div class="setting-demo">
                <label>التأثيرات البصرية:</label>
                <input type="checkbox" id="effectsToggle" onchange="toggleVisualEffects(this.checked)" checked>
            </div>
            <div class="setting-demo">
                <label>شفافية الخلفية:</label>
                <input type="range" id="opacitySlider" min="10" max="100" value="80" onchange="changeOpacity(this.value)">
                <span id="opacityDisplay">80%</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3 style="color: #ffd700;">📊 حالة الإعدادات الحالية</h3>
            <div id="current-settings"></div>
            <button onclick="loadCurrentSettings()">🔄 تحديث الحالة</button>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function testDarkMode() {
            addResult('🧪 اختبار الوضع الليلي...', 'info');
            
            const currentMode = localStorage.getItem('darkMode');
            if (currentMode !== null) {
                addResult('✅ إعداد الوضع الليلي موجود: ' + currentMode, 'success');
                
                // تطبيق الوضع
                const isDark = currentMode !== 'false';
                applyDarkMode(isDark);
                addResult('✅ تم تطبيق الوضع الليلي بنجاح', 'success');
            } else {
                addResult('⚠️ إعداد الوضع الليلي غير موجود', 'warning');
            }
        }

        function testVisualEffects() {
            addResult('🧪 اختبار التأثيرات البصرية...', 'info');
            
            const effectsSetting = localStorage.getItem('visualEffects');
            if (effectsSetting !== null) {
                addResult('✅ إعداد التأثيرات البصرية موجود: ' + effectsSetting, 'success');
                
                const enabled = effectsSetting !== 'false';
                applyVisualEffects(enabled);
                addResult('✅ تم تطبيق التأثيرات البصرية بنجاح', 'success');
            } else {
                addResult('⚠️ إعداد التأثيرات البصرية غير موجود', 'warning');
            }
        }

        function testLanguage() {
            addResult('🧪 اختبار إعدادات اللغة...', 'info');
            
            const language = localStorage.getItem('selectedLanguage');
            if (language) {
                addResult('✅ إعداد اللغة موجود: ' + language, 'success');
            } else {
                addResult('⚠️ إعداد اللغة غير موجود', 'warning');
            }
            
            const descType = localStorage.getItem('descriptionType');
            if (descType) {
                addResult('✅ نوع الأوصاف موجود: ' + descType, 'success');
            } else {
                addResult('⚠️ نوع الأوصاف غير موجود', 'warning');
            }
        }

        function testBackgroundOpacity() {
            addResult('🧪 اختبار شفافية الخلفية...', 'info');
            
            const opacity = localStorage.getItem('backgroundOpacity');
            if (opacity) {
                addResult('✅ إعداد الشفافية موجود: ' + opacity + '%', 'success');
                applyBackgroundOpacity(opacity);
                addResult('✅ تم تطبيق الشفافية بنجاح', 'success');
            } else {
                addResult('⚠️ إعداد الشفافية غير موجود', 'warning');
            }
        }

        function runAllTests() {
            clearResults();
            addResult('🚀 بدء جميع الاختبارات...', 'info');
            
            setTimeout(() => testDarkMode(), 500);
            setTimeout(() => testVisualEffects(), 1000);
            setTimeout(() => testLanguage(), 1500);
            setTimeout(() => testBackgroundOpacity(), 2000);
            setTimeout(() => addResult('🏁 انتهت جميع الاختبارات', 'info'), 2500);
        }

        function toggleDarkMode(enabled) {
            localStorage.setItem('darkMode', enabled.toString());
            applyDarkMode(enabled);
            addResult(enabled ? '🌙 تم تفعيل الوضع الليلي' : '☀️ تم تفعيل الوضع النهاري', 'success');
        }

        function toggleVisualEffects(enabled) {
            localStorage.setItem('visualEffects', enabled.toString());
            applyVisualEffects(enabled);
            addResult(enabled ? '✨ تم تفعيل التأثيرات البصرية' : '🚫 تم إلغاء التأثيرات البصرية', 'success');
        }

        function changeOpacity(value) {
            localStorage.setItem('backgroundOpacity', value);
            applyBackgroundOpacity(value);
            document.getElementById('opacityDisplay').textContent = value + '%';
            addResult('🎨 تم تغيير الشفافية إلى ' + value + '%', 'success');
        }

        function applyDarkMode(enabled) {
            if (enabled) {
                document.body.style.background = 'linear-gradient(135deg, #1a1a2e, #16213e, #0f3460)';
                document.body.style.color = 'white';
            } else {
                document.body.style.background = 'linear-gradient(135deg, #f0f0f0, #e0e0e0, #d0d0d0)';
                document.body.style.color = '#333';
            }
        }

        function applyVisualEffects(enabled) {
            const particles = document.getElementById('particles');
            if (particles) {
                particles.style.display = enabled ? 'block' : 'none';
            }
        }

        function applyBackgroundOpacity(opacity) {
            const opacityValue = opacity / 100;
            const sections = document.querySelectorAll('.test-section, .setting-demo');
            sections.forEach(section => {
                section.style.background = `rgba(255, 255, 255, ${opacityValue * 0.08})`;
            });
        }

        function loadCurrentSettings() {
            const settingsDiv = document.getElementById('current-settings');
            const settings = {
                'اللغة المحددة': localStorage.getItem('selectedLanguage') || 'غير محدد',
                'نوع الأوصاف': localStorage.getItem('descriptionType') || 'غير محدد',
                'الوضع الليلي': localStorage.getItem('darkMode') || 'غير محدد',
                'التأثيرات البصرية': localStorage.getItem('visualEffects') || 'غير محدد',
                'شفافية الخلفية': (localStorage.getItem('backgroundOpacity') || 'غير محدد') + '%',
                'جودة الصور': localStorage.getItem('imageQuality') || 'غير محدد',
                'الحذف التلقائي': localStorage.getItem('autoDeleteFiles') || 'غير محدد'
            };

            let html = '<div style="display: grid; gap: 10px;">';
            for (const [key, value] of Object.entries(settings)) {
                html += `<div style="display: flex; justify-content: space-between; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 5px;">
                    <span style="color: #ffd700;">${key}:</span>
                    <span>${value}</span>
                </div>`;
            }
            html += '</div>';
            settingsDiv.innerHTML = html;
        }

        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            setInterval(() => {
                if (Math.random() < 0.3) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                    particlesContainer.appendChild(particle);

                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.remove();
                        }
                    }, 10000);
                }
            }, 800);
        }

        // تهيئة الصفحة
        window.addEventListener('load', function() {
            // تحميل الإعدادات الحالية
            loadCurrentSettings();
            
            // تطبيق الإعدادات المحفوظة
            const darkMode = localStorage.getItem('darkMode');
            if (darkMode !== null) {
                const isDark = darkMode !== 'false';
                document.getElementById('darkModeToggle').checked = isDark;
                applyDarkMode(isDark);
            }
            
            const visualEffects = localStorage.getItem('visualEffects');
            if (visualEffects !== null) {
                const enabled = visualEffects !== 'false';
                document.getElementById('effectsToggle').checked = enabled;
                applyVisualEffects(enabled);
            }
            
            const opacity = localStorage.getItem('backgroundOpacity');
            if (opacity) {
                document.getElementById('opacitySlider').value = opacity;
                document.getElementById('opacityDisplay').textContent = opacity + '%';
                applyBackgroundOpacity(opacity);
            }
            
            // إنشاء الجسيمات
            createParticles();
            
            // تشغيل اختبار سريع
            setTimeout(() => {
                addResult('🎉 تم تحميل صفحة اختبار الإعدادات بنجاح!', 'success');
                addResult('💡 استخدم الأزرار أعلاه لاختبار الإعدادات المختلفة', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
