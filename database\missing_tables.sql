-- ========================================
-- إنشاء الجداول المفقودة في Supabase
-- Create Missing Tables in Supabase
-- ========================================

-- ========================================
-- إنشاء الجداول المفقودة في Supabase
-- Create Missing Tables in Supabase
-- ========================================

-- 1. إنشاء جدول featured_mods
-- Create featured_mods table
CREATE TABLE IF NOT EXISTS featured_mods (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_featured_mod UNIQUE (mod_id)
);

-- 2. إنشاء جدول download_errors
-- Create download_errors table
CREATE TABLE IF NOT EXISTS download_errors (
    id SERIAL PRIMARY KEY,
    user_id TEXT,
    mod_id UUID,
    error_type VARCHAR(255),
    error_message TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. إنشاء جدول custom_mod_dialogs
-- Create custom_mod_dialogs table
CREATE TABLE IF NOT EXISTS custom_mod_dialogs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100) DEFAULT 'تم',
    show_dont_show_again BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. إنشاء جدول custom_dialog_mods (junction table)
-- Create custom_dialog_mods table (junction table)
CREATE TABLE IF NOT EXISTS custom_dialog_mods (
    id SERIAL PRIMARY KEY,
    dialog_id INTEGER NOT NULL,
    mod_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_dialog_mods_dialog_id FOREIGN KEY (dialog_id) REFERENCES custom_mod_dialogs(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_dialog_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_dialog_mod UNIQUE (dialog_id, mod_id)
);

-- 3. إنشاء جدول custom_copyright_mods
-- Create custom_copyright_mods table
CREATE TABLE IF NOT EXISTS custom_copyright_mods (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_copyright_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_copyright_mod UNIQUE (mod_id)
);

-- 4. إنشاء جدول custom_sections
-- Create custom_sections table
CREATE TABLE IF NOT EXISTS custom_sections (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    section_type VARCHAR(50) DEFAULT 'custom',
    mod_selection_type VARCHAR(50) DEFAULT 'manual',
    auto_criteria JSONB,
    max_mods_display INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 5. إنشاء جدول custom_section_mods (ربط الأقسام بالمودات)
-- Create custom_section_mods table (linking sections to mods)
CREATE TABLE IF NOT EXISTS custom_section_mods (
    id SERIAL PRIMARY KEY,
    section_id INTEGER REFERENCES custom_sections(id) ON DELETE CASCADE,
    mod_id INTEGER REFERENCES mods(id) ON DELETE CASCADE,
    display_order INTEGER DEFAULT 0,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(section_id, mod_id)
);

-- 6. إنشاء دالة execute_sql للاستخدام المستقبلي (اختيارية)
-- Create execute_sql function for future use (optional)
CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- هذه الدالة محدودة لأغراض الأمان
    -- This function is limited for security purposes
    -- يمكن توسيعها حسب الحاجة
    -- Can be expanded as needed
    
    -- للأمان، نرفض تنفيذ أي استعلامات خطيرة
    -- For security, reject any dangerous queries
    IF sql_query ILIKE '%DROP%' OR 
       sql_query ILIKE '%DELETE%' OR 
       sql_query ILIKE '%TRUNCATE%' OR
       sql_query ILIKE '%ALTER%' THEN
        RAISE EXCEPTION 'Dangerous SQL operations are not allowed';
    END IF;
    
    -- تنفيذ الاستعلام
    -- Execute the query
    EXECUTE sql_query;
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error executing SQL: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. إضافة تعليقات توضيحية
-- Add documentation comments
COMMENT ON TABLE featured_mods IS 'Stores information about featured mods';
COMMENT ON TABLE download_errors IS 'Stores records of download errors for analysis';
COMMENT ON TABLE custom_mod_dialogs IS 'Stores custom dialog configurations for mods';
COMMENT ON TABLE custom_dialog_mods IS 'Junction table linking dialogs to specific mods';
COMMENT ON TABLE custom_copyright_mods IS 'Tracks mods that have custom copyright descriptions';
COMMENT ON TABLE custom_sections IS 'Stores custom section configurations';
COMMENT ON TABLE custom_section_mods IS 'Junction table linking custom sections to mods';

-- 10. إنشاء فهارس لتحسين الأداء
-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_featured_mods_active ON featured_mods(is_active);
CREATE INDEX IF NOT EXISTS idx_featured_mods_order ON featured_mods(display_order);
CREATE INDEX IF NOT EXISTS idx_download_errors_timestamp ON download_errors(timestamp);
CREATE INDEX IF NOT EXISTS idx_download_errors_resolved ON download_errors(is_resolved);
CREATE INDEX IF NOT EXISTS idx_custom_dialog_mods_dialog_id ON custom_dialog_mods(dialog_id);
CREATE INDEX IF NOT EXISTS idx_custom_dialog_mods_mod_id ON custom_dialog_mods(mod_id);
CREATE INDEX IF NOT EXISTS idx_custom_copyright_mods_mod_id ON custom_copyright_mods(mod_id);
CREATE INDEX IF NOT EXISTS idx_custom_mod_dialogs_active ON custom_mod_dialogs(is_active);
CREATE INDEX IF NOT EXISTS idx_custom_sections_active ON custom_sections(is_active);
CREATE INDEX IF NOT EXISTS idx_custom_sections_order ON custom_sections(display_order);
CREATE INDEX IF NOT EXISTS idx_custom_section_mods_section ON custom_section_mods(section_id);
CREATE INDEX IF NOT EXISTS idx_custom_section_mods_mod ON custom_section_mods(mod_id);

-- 11. إنشاء trigger لتحديث updated_at تلقائياً
-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق trigger على الجداول
-- Apply trigger to tables
CREATE TRIGGER update_featured_mods_updated_at
    BEFORE UPDATE ON featured_mods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_errors_updated_at
    BEFORE UPDATE ON download_errors
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_mod_dialogs_updated_at
    BEFORE UPDATE ON custom_mod_dialogs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_copyright_mods_updated_at
    BEFORE UPDATE ON custom_copyright_mods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_sections_updated_at
    BEFORE UPDATE ON custom_sections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 12. إنشاء view للإحصائيات
-- Create statistics view
CREATE OR REPLACE VIEW featured_mods_stats AS
SELECT
    COUNT(*) as total_featured_mods,
    COUNT(*) FILTER (WHERE is_active = true) as active_featured_mods
FROM featured_mods;

CREATE OR REPLACE VIEW download_errors_stats AS
SELECT
    COUNT(*) as total_errors,
    COUNT(*) FILTER (WHERE is_resolved = false) as unresolved_errors,
    COUNT(DISTINCT user_id) as affected_users,
    COUNT(DISTINCT mod_id) as affected_mods
FROM download_errors;

CREATE OR REPLACE VIEW custom_dialogs_stats AS
SELECT 
    COUNT(*) as total_dialogs,
    COUNT(*) FILTER (WHERE is_active = true) as active_dialogs,
    COUNT(DISTINCT cdm.mod_id) as mods_with_dialogs,
    AVG(CASE WHEN is_active THEN 1 ELSE 0 END) * 100 as active_percentage
FROM custom_mod_dialogs cmd
LEFT JOIN custom_dialog_mods cdm ON cmd.id = cdm.dialog_id;

-- 13. دالة للحصول على dialogs خاصة بمود معين
-- Function to get dialogs for a specific mod
CREATE OR REPLACE FUNCTION get_mod_dialogs(p_mod_id UUID)
RETURNS TABLE (
    dialog_id INTEGER,
    title VARCHAR(255),
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100),
    show_dont_show_again BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cmd.id,
        cmd.title,
        cmd.description,
        cmd.image_url,
        cmd.button_text,
        cmd.show_dont_show_again
    FROM custom_mod_dialogs cmd
    INNER JOIN custom_dialog_mods cdm ON cmd.id = cdm.dialog_id
    WHERE cdm.mod_id = p_mod_id 
    AND cmd.is_active = true
    ORDER BY cmd.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 14. دالة للتحقق من وجود copyright مخصص لمود
-- Function to check if mod has custom copyright
CREATE OR REPLACE FUNCTION has_custom_copyright(p_mod_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    result BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM custom_copyright_mods 
        WHERE mod_id = p_mod_id AND is_active = true
    ) INTO result;
    
    RETURN COALESCE(result, false);
END;
$$ LANGUAGE plpgsql;
