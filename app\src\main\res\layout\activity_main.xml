<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- AdView at the bottom -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adViewBanner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        app:adSize="BANNER"
        app:adUnitId="ca-app-pub-4373910379376809/6035179861">
        <!-- Make sure to replace adUnitId with your actual ID if needed -->
    </com.google.android.gms.ads.AdView>

    <!-- CustomWebView filling the space above the AdView -->
    <com.example.modetaris.CustomWebView
        android:id="@+id/webViewMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/adViewBanner" />

</RelativeLayout>
