<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الاشتراك المحسن</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            border: 2px solid #ffd700;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.08);
            margin-bottom: 30px;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .test-title {
            color: #ffd700;
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }

        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.enabled {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #86efac;
        }

        .status.disabled {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #fca5a5;
        }

        .test-result {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            border-left: 4px solid #ffd700;
        }

        .floating-test-icon {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #ffd700, #ffcc00);
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
            50% { box-shadow: 0 0 30px rgba(255, 215, 0, 1); }
            100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
        }

        .floating-test-icon img {
            width: 85%;
            height: 85%;
            object-fit: contain;
            border-radius: 50%;
        }

        .banner-test {
            background: linear-gradient(45deg, #667eea, #764ba2);
            padding: 20px;
            border-radius: 15px;
            margin: 10px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .banner-test::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .subscription-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار نظام الاشتراك المحسن</h1>
            <p>اختبر جميع مكونات النظام والتأكد من عملها بشكل صحيح</p>
        </div>

        <!-- Test Floating Icon -->
        <div class="test-section">
            <h3 class="test-title">
                🔵 اختبار الأيقونة العائمة
            </h3>
            <p>اختبر ظهور وعمل الأيقونة العائمة في يمين الشاشة</p>
            
            <div id="floatingIconStatus" class="status disabled">
                الأيقونة العائمة: غير مفعلة
            </div>
            
            <button onclick="testFloatingIcon()" class="btn">عرض الأيقونة العائمة</button>
            <button onclick="hideFloatingIcon()" class="btn btn-danger">إخفاء الأيقونة العائمة</button>
            <button onclick="changeFloatingIconImage()" class="btn btn-info">تغيير صورة الأيقونة</button>
            
            <div id="floatingIconResult" class="test-result" style="display: none;">
                <strong>نتيجة الاختبار:</strong>
                <div id="floatingIconResultText"></div>
            </div>
        </div>

        <!-- Test Subscription Modal -->
        <div class="test-section">
            <h3 class="test-title">
                📱 اختبار مودال الاشتراك قبل التحميل
            </h3>
            <p>اختبر ظهور نافذة الاشتراك مع زر التخطي</p>
            
            <button onclick="testSubscriptionModal()" class="btn">عرض مودال الاشتراك</button>
            <button onclick="testSubscriptionPage()" class="btn btn-info">اختبار صفحة الاشتراك</button>
            
            <div id="modalResult" class="test-result" style="display: none;">
                <strong>نتيجة الاختبار:</strong>
                <div id="modalResultText"></div>
            </div>
        </div>

        <!-- Test Banners -->
        <div class="test-section">
            <h3 class="test-title">
                🎯 اختبار البانرات
            </h3>
            <p>اختبر عرض بانرات الاشتراك المختلفة</p>
            
            <div id="bannerStatus" class="status disabled">
                البانرات: غير مفعلة
            </div>
            
            <button onclick="testBanners()" class="btn">عرض بانرات الاختبار</button>
            <button onclick="clearBanners()" class="btn btn-danger">مسح البانرات</button>
            
            <div id="bannerTestArea"></div>
            
            <div id="bannerResult" class="test-result" style="display: none;">
                <strong>نتيجة الاختبار:</strong>
                <div id="bannerResultText"></div>
            </div>
        </div>

        <!-- Test Settings -->
        <div class="test-section">
            <h3 class="test-title">
                ⚙️ اختبار الإعدادات
            </h3>
            <p>اختبر حفظ وتحميل الإعدادات</p>
            
            <button onclick="loadCurrentSettings()" class="btn">تحميل الإعدادات الحالية</button>
            <button onclick="testSettingsSave()" class="btn btn-success">اختبار حفظ الإعدادات</button>
            <button onclick="resetTestSettings()" class="btn btn-danger">إعادة تعيين إعدادات الاختبار</button>
            
            <div id="settingsResult" class="test-result" style="display: none;">
                <strong>الإعدادات الحالية:</strong>
                <div id="settingsResultText"></div>
            </div>
        </div>

        <!-- Test Statistics -->
        <div class="test-section">
            <h3 class="test-title">
                📊 اختبار الإحصائيات
            </h3>
            <p>اختبر تتبع وعرض الإحصائيات</p>
            
            <button onclick="generateTestData()" class="btn">إنشاء بيانات اختبار</button>
            <button onclick="viewStatistics()" class="btn btn-info">عرض الإحصائيات</button>
            <button onclick="clearTestData()" class="btn btn-danger">مسح بيانات الاختبار</button>
            
            <div id="statisticsResult" class="test-result" style="display: none;">
                <strong>الإحصائيات:</strong>
                <div id="statisticsResultText"></div>
            </div>
        </div>

        <!-- Overall Test Results -->
        <div class="test-section">
            <h3 class="test-title">
                ✅ نتائج الاختبار الشاملة
            </h3>
            <p>ملخص جميع الاختبارات</p>
            
            <button onclick="runAllTests()" class="btn btn-success">تشغيل جميع الاختبارات</button>
            <button onclick="generateTestReport()" class="btn btn-info">إنشاء تقرير الاختبار</button>
            
            <div id="overallResult" class="test-result" style="display: none;">
                <strong>تقرير الاختبار الشامل:</strong>
                <div id="overallResultText"></div>
            </div>
        </div>
    </div>

    <!-- Test Floating Icon -->
    <div id="testFloatingIcon" class="floating-test-icon" style="display: none;" onclick="handleTestIconClick()">
        <img src="https://cdn-icons-png.flaticon.com/512/2917/2917995.png" alt="Test Icon">
    </div>

    <script>
        // Test Variables
        let testResults = {
            floatingIcon: false,
            subscriptionModal: false,
            banners: false,
            settings: false,
            statistics: false
        };

        // Test Floating Icon
        function testFloatingIcon() {
            const icon = document.getElementById('testFloatingIcon');
            const status = document.getElementById('floatingIconStatus');
            const result = document.getElementById('floatingIconResult');
            const resultText = document.getElementById('floatingIconResultText');
            
            icon.style.display = 'flex';
            status.className = 'status enabled';
            status.textContent = 'الأيقونة العائمة: مفعلة';
            
            result.style.display = 'block';
            resultText.innerHTML = `
                ✅ تم عرض الأيقونة العائمة بنجاح<br>
                📍 الموقع: يمين الشاشة في المنتصف<br>
                🎨 التصميم: دائري ذهبي مع تأثير النبض<br>
                🖱️ التفاعل: قابل للنقر
            `;
            
            testResults.floatingIcon = true;
        }

        function hideFloatingIcon() {
            const icon = document.getElementById('testFloatingIcon');
            const status = document.getElementById('floatingIconStatus');
            
            icon.style.display = 'none';
            status.className = 'status disabled';
            status.textContent = 'الأيقونة العائمة: غير مفعلة';
        }

        function changeFloatingIconImage() {
            const icon = document.getElementById('testFloatingIcon');
            const img = icon.querySelector('img');
            const images = [
                'https://cdn-icons-png.flaticon.com/512/2917/2917995.png',
                'https://cdn-icons-png.flaticon.com/512/3135/3135715.png',
                'https://cdn-icons-png.flaticon.com/512/1828/1828884.png',
                'https://cdn-icons-png.flaticon.com/512/2702/2702602.png'
            ];
            
            const randomImage = images[Math.floor(Math.random() * images.length)];
            img.src = randomImage;
            
            alert('تم تغيير صورة الأيقونة بنجاح!');
        }

        function handleTestIconClick() {
            alert('🎉 تم النقر على الأيقونة العائمة!\n\nفي التطبيق الحقيقي، سيتم توجيه المستخدم إلى صفحة الاشتراك أو عرض مودال الاشتراك.');
        }

        // Test Subscription Modal
        function testSubscriptionModal() {
            const result = document.getElementById('modalResult');
            const resultText = document.getElementById('modalResultText');
            
            // Simulate modal display
            const modalHTML = `
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                " onclick="this.remove()">
                    <div style="
                        background: linear-gradient(135deg, #1a1a2e, #16213e);
                        padding: 30px;
                        border-radius: 20px;
                        border: 2px solid #ffd700;
                        text-align: center;
                        max-width: 400px;
                        color: white;
                    " onclick="event.stopPropagation()">
                        <h2 style="color: #ffd700; margin-bottom: 15px;">🎁 احصل على اشتراك مجاني!</h2>
                        <p style="margin-bottom: 20px;">أكمل المهام البسيطة واحصل على تحميل بدون إعلانات!</p>
                        <button style="
                            background: linear-gradient(45deg, #28a745, #20c997);
                            color: white;
                            border: none;
                            padding: 12px 25px;
                            border-radius: 25px;
                            margin: 5px;
                            cursor: pointer;
                        " onclick="alert('تم النقر على زر الاشتراك!'); this.closest('div').remove();">
                            احصل على الاشتراك المجاني
                        </button>
                        <button style="
                            background: rgba(255,255,255,0.1);
                            color: white;
                            border: 2px solid rgba(255,255,255,0.3);
                            padding: 10px 20px;
                            border-radius: 20px;
                            margin: 5px;
                            cursor: pointer;
                        " onclick="alert('تم تخطي الاشتراك!'); this.closest('div').remove();">
                            تخطي ومتابعة التحميل
                        </button>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            result.style.display = 'block';
            resultText.innerHTML = `
                ✅ تم عرض مودال الاشتراك بنجاح<br>
                🎯 يحتوي على زر الاشتراك وزر التخطي<br>
                🎨 تصميم جذاب ومتجاوب<br>
                📱 يعمل على جميع الأجهزة
            `;
            
            testResults.subscriptionModal = true;
        }

        function testSubscriptionPage() {
            window.open('../subscription-page.html?test=true', '_blank');
        }

        // Test Banners
        function testBanners() {
            const status = document.getElementById('bannerStatus');
            const testArea = document.getElementById('bannerTestArea');
            const result = document.getElementById('bannerResult');
            const resultText = document.getElementById('bannerResultText');
            
            status.className = 'status enabled';
            status.textContent = 'البانرات: مفعلة';
            
            const banners = [
                {
                    title: 'اشتراك مجاني - عرض خاص!',
                    description: 'احصل على تحميل بدون إعلانات',
                    type: 'subscription'
                },
                {
                    title: 'مودات حصرية للمشتركين',
                    description: 'وصول مبكر للمحتوى الجديد',
                    type: 'subscription'
                }
            ];
            
            testArea.innerHTML = '';
            banners.forEach((banner, index) => {
                const bannerHTML = `
                    <div class="banner-test" style="animation-delay: ${index * 0.5}s;">
                        ${banner.type === 'subscription' ? '<div class="subscription-badge">🎁 اشتراك مجاني</div>' : ''}
                        <h4 style="margin-bottom: 10px;">${banner.title}</h4>
                        <p>${banner.description}</p>
                    </div>
                `;
                testArea.insertAdjacentHTML('beforeend', bannerHTML);
            });
            
            result.style.display = 'block';
            resultText.innerHTML = `
                ✅ تم عرض ${banners.length} بانر اختبار<br>
                🏷️ يحتوي على شارات الاشتراك المجاني<br>
                ✨ تأثيرات بصرية جذابة<br>
                🔄 دوران تلقائي (في التطبيق الحقيقي)
            `;
            
            testResults.banners = true;
        }

        function clearBanners() {
            const testArea = document.getElementById('bannerTestArea');
            const status = document.getElementById('bannerStatus');
            
            testArea.innerHTML = '';
            status.className = 'status disabled';
            status.textContent = 'البانرات: غير مفعلة';
        }

        // Test Settings
        function loadCurrentSettings() {
            const result = document.getElementById('settingsResult');
            const resultText = document.getElementById('settingsResultText');
            
            const settings = {
                enableFloatingIcon: localStorage.getItem('enableFloatingIcon') === 'true',
                showBeforeDownload: localStorage.getItem('showSubscriptionBeforeDownload') === 'true',
                enableBanners: localStorage.getItem('enableSubscriptionBanners') === 'true',
                useSubscriptionPage: localStorage.getItem('useSubscriptionPage') === 'true',
                floatingIconImageUrl: localStorage.getItem('floatingIconImageUrl') || 'غير محدد'
            };
            
            result.style.display = 'block';
            resultText.innerHTML = `
                🔵 الأيقونة العائمة: ${settings.enableFloatingIcon ? 'مفعلة' : 'غير مفعلة'}<br>
                📱 عرض قبل التحميل: ${settings.showBeforeDownload ? 'مفعل' : 'غير مفعل'}<br>
                🎯 البانرات: ${settings.enableBanners ? 'مفعلة' : 'غير مفعلة'}<br>
                📄 صفحة الاشتراك: ${settings.useSubscriptionPage ? 'مفعلة' : 'غير مفعلة'}<br>
                🖼️ صورة الأيقونة: ${settings.floatingIconImageUrl}
            `;
            
            testResults.settings = true;
        }

        function testSettingsSave() {
            // Test saving settings
            const testSettings = {
                enableFloatingIcon: 'true',
                showSubscriptionBeforeDownload: 'true',
                enableSubscriptionBanners: 'true',
                useSubscriptionPage: 'false',
                floatingIconImageUrl: 'https://example.com/test-icon.png'
            };
            
            Object.keys(testSettings).forEach(key => {
                localStorage.setItem(key, testSettings[key]);
            });
            
            alert('تم حفظ إعدادات الاختبار بنجاح!');
            loadCurrentSettings();
        }

        function resetTestSettings() {
            const settingsKeys = [
                'enableFloatingIcon', 'showSubscriptionBeforeDownload',
                'enableSubscriptionBanners', 'useSubscriptionPage', 'floatingIconImageUrl'
            ];
            
            settingsKeys.forEach(key => localStorage.removeItem(key));
            alert('تم إعادة تعيين إعدادات الاختبار!');
            loadCurrentSettings();
        }

        // Test Statistics
        function generateTestData() {
            const userId = 'test_user_' + Date.now();
            
            // Generate test subscription data
            localStorage.setItem(`subscription_${userId}`, 'active');
            
            // Generate test banner view data
            for (let i = 1; i <= 5; i++) {
                localStorage.setItem(`banner_view_count_${userId}_banner_${i}`, Math.floor(Math.random() * 10) + 1);
            }
            
            // Generate test campaign completion data
            localStorage.setItem(`campaign_completion_${userId}_campaign_1`, 'completed');
            
            alert('تم إنشاء بيانات اختبار بنجاح!');
        }

        function viewStatistics() {
            const result = document.getElementById('statisticsResult');
            const resultText = document.getElementById('statisticsResultText');
            
            // Count test data
            let subscriptions = 0;
            let bannerViews = 0;
            let completedCampaigns = 0;
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.startsWith('subscription_')) subscriptions++;
                if (key.includes('banner_view_count_')) bannerViews += parseInt(localStorage.getItem(key) || '0');
                if (key.includes('campaign_completion_')) completedCampaigns++;
            }
            
            result.style.display = 'block';
            resultText.innerHTML = `
                👥 المشتركين النشطين: ${subscriptions}<br>
                👁️ مشاهدات البانرات: ${bannerViews}<br>
                ✅ الحملات المكتملة: ${completedCampaigns}<br>
                📊 إجمالي البيانات: ${localStorage.length} عنصر
            `;
            
            testResults.statistics = true;
        }

        function clearTestData() {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (
                    key.startsWith('test_') ||
                    key.includes('banner_view_count_') ||
                    key.includes('campaign_completion_')
                )) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => localStorage.removeItem(key));
            alert(`تم مسح ${keysToRemove.length} عنصر من بيانات الاختبار!`);
        }

        // Run All Tests
        function runAllTests() {
            testFloatingIcon();
            setTimeout(() => testSubscriptionModal(), 1000);
            setTimeout(() => testBanners(), 2000);
            setTimeout(() => loadCurrentSettings(), 3000);
            setTimeout(() => {
                generateTestData();
                viewStatistics();
                generateTestReport();
            }, 4000);
        }

        function generateTestReport() {
            const result = document.getElementById('overallResult');
            const resultText = document.getElementById('overallResultText');
            
            const passedTests = Object.values(testResults).filter(Boolean).length;
            const totalTests = Object.keys(testResults).length;
            const successRate = Math.round((passedTests / totalTests) * 100);
            
            result.style.display = 'block';
            resultText.innerHTML = `
                📊 <strong>تقرير الاختبار الشامل</strong><br><br>
                ✅ الاختبارات الناجحة: ${passedTests}/${totalTests}<br>
                📈 معدل النجاح: ${successRate}%<br><br>
                <strong>تفاصيل الاختبارات:</strong><br>
                🔵 الأيقونة العائمة: ${testResults.floatingIcon ? '✅ نجح' : '❌ فشل'}<br>
                📱 مودال الاشتراك: ${testResults.subscriptionModal ? '✅ نجح' : '❌ فشل'}<br>
                🎯 البانرات: ${testResults.banners ? '✅ نجح' : '❌ فشل'}<br>
                ⚙️ الإعدادات: ${testResults.settings ? '✅ نجح' : '❌ فشل'}<br>
                📊 الإحصائيات: ${testResults.statistics ? '✅ نجح' : '❌ فشل'}<br><br>
                ${successRate === 100 ? '🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي.' : 
                  successRate >= 80 ? '👍 معظم الاختبارات نجحت. النظام يعمل بشكل جيد.' :
                  '⚠️ بعض الاختبارات فشلت. يرجى مراجعة النظام.'}
            `;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('صفحة اختبار نظام الاشتراك المحسن جاهزة');
        });
    </script>
</body>
</html>
