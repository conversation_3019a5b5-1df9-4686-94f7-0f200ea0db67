# 🗄️ نظام النسخ الاحتياطي الاحترافي للمودات
## Professional Backup System for Mods Database

---

## 🌟 المميزات الرئيسية

### ✅ نسخ احتياطي شامل
- نسخ احتياطي كامل لجميع جداول قاعدة البيانات
- نسخ احتياطي للصور والملفات المرتبطة
- ضغط النسخ الاحتياطية لتوفير المساحة
- رفع تلقائي إلى Firebase Storage

### 🔄 نقل البيانات المتقدم
- نقل البيانات بين قواعد بيانات Supabase مختلفة
- إنشاء الجداول المفقودة تلقائياً
- التحقق من سلامة النقل
- **تفعيل قاعدة البيانات الجديدة تلقائياً**

### 🔍 مراقبة مستمرة
- مراقبة حالة قواعد البيانات 24/7
- كشف الأعطال والمشاكل
- التبديل التلقائي عند الأعطال
- إرسال تنبيهات فورية

### 🎛️ إدارة متقدمة
- واجهة إدارة ويب احترافية
- تبديل سريع بين قواعد البيانات
- مقارنة قواعد البيانات
- جدولة النسخ الاحتياطية

---

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام
```bash
# Python 3.8 أو أحدث
python --version

# Git (اختياري)
git --version
```

### 2. تثبيت المتطلبات
```bash
cd backup_system
pip install -r requirements.txt
```

### 3. إعداد Firebase
1. ضع ملف `download-e33a2-firebase-adminsdk-fbsvc-01f65407db.json` في مجلد `app/src/main/assets/admin/`
2. تأكد من صحة إعدادات Firebase في `backup_config.json`

### 4. إعداد قواعد البيانات الاحتياطية
```json
{
  "supabase": {
    "main": {
      "url": "https://ytqxxodyecdeosnqoure.supabase.co",
      "key": "your-main-key"
    },
    "backup1": {
      "url": "https://your-backup1-url.supabase.co",
      "key": "your-backup1-key"
    },
    "backup2": {
      "url": "https://your-backup2-url.supabase.co", 
      "key": "your-backup2-key"
    }
  }
}
```

### 5. تشغيل الإعداد التلقائي
```bash
python quick_start.py
```

---

## 📋 الاستخدام

### 🖥️ واجهة سطر الأوامر

#### إنشاء نسخة احتياطية
```bash
# نسخة احتياطية من القاعدة الرئيسية
python database_backup_manager.py backup

# نسخة احتياطية من قاعدة محددة
python database_backup_manager.py backup backup1
```

#### نقل البيانات
```bash
# نقل عادي
python database_backup_manager.py transfer main backup1

# نقل مع تفعيل قاعدة البيانات الجديدة
python database_backup_manager.py transfer main backup1 --activate
```

#### تبديل قاعدة البيانات
```bash
# تبديل مع نسخة احتياطية
python database_backup_manager.py switch backup1

# تبديل بدون نسخة احتياطية
python database_backup_manager.py switch backup1 --no-backup
```

#### مقارنة قواعد البيانات
```bash
python database_backup_manager.py compare main backup1
```

#### عرض حالة النظام
```bash
python database_backup_manager.py status
```

#### بدء النسخ الاحتياطي التلقائي
```bash
python database_backup_manager.py auto
```

#### بدء المراقبة
```bash
python database_monitor.py start
```

### 🎛️ واجهة الإدارة الرسومية

1. افتح `app/src/main/assets/admin/backup-management.html`
2. أو من لوحة الإدارة الرئيسية → "إدارة النسخ الاحتياطية"

#### الميزات المتاحة:
- **لوحة التحكم**: عرض حالة النظام والإحصائيات
- **النسخ الاحتياطية**: إنشاء وإدارة النسخ الاحتياطية
- **نقل البيانات**: نقل البيانات مع خيار التفعيل
- **المراقبة**: مراقبة مباشرة وسجلات النظام
- **الإعدادات**: تخصيص إعدادات النظام

### 🔧 ملفات التشغيل السريع

#### Windows
```cmd
start_backup_system.bat
```

#### Linux/Mac
```bash
./start_backup_system.sh
```

---

## ⚙️ الإعدادات المتقدمة

### ملف `backup_config.json`

```json
{
  "backup_settings": {
    "auto_backup_interval": 6,
    "max_backup_files": 50,
    "compress_backups": true,
    "include_images": true,
    "verify_transfer": true,
    "create_missing_tables": true,
    "backup_tables": [
      "mods",
      "user_languages",
      "featured_addons",
      "free_addons",
      "banner_ads",
      "suggested_mods",
      "custom_mod_dialogs",
      "custom_dialog_mods",
      "custom_copyright_mods",
      "entry_subscription_ads",
      "custom_sections"
    ],
    "critical_tables": [
      "mods",
      "user_languages",
      "featured_addons"
    ]
  },
  "monitoring": {
    "health_check_interval": 300,
    "max_failed_checks": 3,
    "auto_failover_enabled": true
  }
}
```

### إعدادات المراقبة
- `health_check_interval`: فترة فحص الحالة (ثواني)
- `max_failed_checks`: عدد الفحوصات الفاشلة قبل التبديل
- `auto_failover_enabled`: تفعيل التبديل التلقائي

### إعدادات النسخ الاحتياطي
- `auto_backup_interval`: فترة النسخ التلقائي (ساعات)
- `max_backup_files`: الحد الأقصى لعدد النسخ
- `verify_transfer`: التحقق من سلامة النقل
- `create_missing_tables`: إنشاء الجداول المفقودة

---

## 🔄 سيناريوهات الاستخدام

### 1. إعداد قاعدة بيانات احتياطية جديدة

```bash
# 1. إنشاء نسخة احتياطية من القاعدة الحالية
python database_backup_manager.py backup main

# 2. نقل البيانات إلى القاعدة الجديدة
python database_backup_manager.py transfer main backup1

# 3. التحقق من النقل
python database_backup_manager.py compare main backup1

# 4. تفعيل القاعدة الجديدة (اختياري)
python database_backup_manager.py switch backup1
```

### 2. التعافي من عطل قاعدة البيانات

```bash
# 1. التحقق من حالة قواعد البيانات
python database_backup_manager.py status

# 2. التبديل إلى قاعدة احتياطية
python database_backup_manager.py switch backup1 --no-backup

# 3. استرداد البيانات من نسخة احتياطية
python database_backup_manager.py restore backup_main_20240101 backup2
```

### 3. ترحيل إلى خادم جديد

```bash
# 1. إنشاء نسخة احتياطية شاملة
python database_backup_manager.py backup main

# 2. إعداد قاعدة البيانات الجديدة في backup_config.json
# 3. نقل البيانات مع التفعيل
python database_backup_manager.py transfer main new_server --activate
```

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بـ Firebase
```bash
# تحقق من ملف بيانات الاعتماد
ls app/src/main/assets/admin/download-e33a2-firebase-adminsdk-fbsvc-01f65407db.json

# تحقق من الصلاحيات
python -c "import firebase_admin; print('Firebase OK')"
```

#### خطأ في الاتصال بـ Supabase
```bash
# تحقق من الإعدادات
python -c "from supabase import create_client; print('Supabase OK')"

# اختبار الاتصال
python database_backup_manager.py status
```

#### مشاكل في النقل
- تأكد من وجود الجداول في قاعدة البيانات المستهدفة
- تحقق من الصلاحيات
- استخدم خيار `create_missing_tables: true`

### سجلات النظام
```bash
# عرض سجلات النسخ الاحتياطي
tail -f backup_system.log

# عرض سجلات المراقبة  
tail -f database_monitor.log
```

---

## 📞 الدعم والمساعدة

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الدعم الفني**: متاح 24/7

### الملفات المهمة
- `backup_system.log`: سجلات النسخ الاحتياطي
- `database_monitor.log`: سجلات المراقبة
- `active_database.txt`: قاعدة البيانات النشطة حالياً
- `backup_config.json`: إعدادات النظام

### نصائح للأداء الأمثل
1. قم بتشغيل النسخ الاحتياطي التلقائي
2. راقب حالة قواعد البيانات بانتظام
3. احتفظ بنسخ احتياطية متعددة
4. اختبر عملية الاسترداد دورياً

---

## 🔐 الأمان

- جميع النسخ الاحتياطية مشفرة (اختياري)
- بيانات الاعتماد محمية
- سجلات مفصلة لجميع العمليات
- التحقق من سلامة البيانات

---

**🎉 نظام النسخ الاحتياطي الاحترافي - حماية بياناتك بثقة!**
