# نظام اختيار اللغة - Language Selection System

## نظرة عامة / Overview

تم إنشاء نظام شامل لاختيار اللغة في تطبيق Mod Etaris يدعم اللغتين العربية والإنجليزية مع حفظ إحصائيات المستخدمين.

A comprehensive language selection system has been created for the Mod Etaris app supporting Arabic and English languages with user statistics tracking.

## الملفات المضافة / Added Files

### 1. صفحة اختيار اللغة / Language Selection Page
- **الملف**: `app/src/main/assets/language-selection.html`
- **الوصف**: صفحة جميلة لاختيار اللغة تظهر للمستخدمين الجدد مرة واحدة فقط
- **المميزات**:
  - تصميم جذاب يتماشى مع ستايل التطبيق
  - خيارين: العربية والإنجليزية مع الأعلام
  - تظهر مرة واحدة فقط للمستخدمين الجدد
  - حفظ اختيار المستخدم في localStorage و قاعدة البيانات

### 2. نظام الترجمة / Translation System
- **الملف**: `app/src/main/assets/translations.js`
- **الوصف**: نظام ترجمة شامل يدعم جميع نصوص التطبيق
- **المميزات**:
  - فئة TranslationManager للإدارة المركزية للترجمات
  - دعم RTL للعربية و LTR للإنجليزية
  - دالة مساعدة `t()` للوصول السريع للترجمات
  - نظام fallback للنصوص المفقودة

### 3. قاعدة البيانات / Database Files

#### جدول إحصائيات اللغة
- **الملف**: `database/user_language_stats.sql`
- **الوصف**: جدول لحفظ إحصائيات اختيار اللغة
- **الأعمدة**:
  - `id`: معرف فريد
  - `user_id`: معرف المستخدم
  - `selected_language`: اللغة المختارة (ar/en)
  - `selection_timestamp`: وقت الاختيار
  - `user_agent`: معلومات المتصفح
  - `ip_address`: عنوان IP
  - `device_info`: معلومات الجهاز (JSON)

#### تعديل جدول المودات
- **الملف**: `database/add_arabic_description.sql`
- **الوصف**: إضافة عمود الوصف العربي لجدول المودات
- **التعديلات**:
  - إضافة عمود `description_ar` لحفظ الوصف العربي
  - دوال مساعدة للحصول على الوصف حسب اللغة
  - فهارس للبحث المحسن

## التحديثات على الملفات الموجودة / Updates to Existing Files

### 1. index.html
- إضافة فحص اختيار اللغة قبل تحميل التطبيق
- إعادة توجيه للمستخدمين الجدد لصفحة اختيار اللغة
- تضمين ملف الترجمات

### 2. search.html
- إضافة دعم نظام الترجمة
- تضمين ملف الترجمات

### 3. script.js
- تحديث عرض تفاصيل المودات لدعم الوصف المترجم
- تحديث رسالة تحذير الشيدرز لاستخدام نظام الترجمة
- إضافة دالة `getLocalizedDescription()` للحصول على الوصف المناسب

### 4. supabase-manager.js
- إضافة جدول `user_language_stats` لقائمة الجداول المدارة
- تحديث إنشاء الجداول التلقائي

### 5. style.css
- إضافة أنماط CSS لصفحة اختيار اللغة
- تصميم جذاب مع تأثيرات الحركة
- ألوان متناسقة مع التطبيق

## كيفية العمل / How It Works

### 1. للمستخدمين الجدد / For New Users
1. عند فتح التطبيق لأول مرة، يتم فحص `localStorage` للتأكد من اختيار اللغة
2. إذا لم يتم اختيار لغة، يتم إعادة التوجيه لصفحة `language-selection.html`
3. المستخدم يختار اللغة المفضلة (عربي أو إنجليزي)
4. يتم حفظ الاختيار في `localStorage` وقاعدة البيانات
5. إعادة توجيه للصفحة الرئيسية

### 2. للمستخدمين الحاليين / For Existing Users
1. يتم فحص `localStorage` للغة المحفوظة
2. تحميل التطبيق مباشرة بالغة المحفوظة
3. تطبيق اتجاه النص المناسب (RTL/LTR)

### 3. عرض المحتوى / Content Display
1. **وصف المودات**: يتم عرض `description_ar` للعربية و `description` للإنجليزية
2. **رسائل التحذير**: تستخدم نظام الترجمة الجديد
3. **واجهة المستخدم**: جميع النصوص تستخدم دالة `t()` للترجمة

## الإحصائيات والتحليلات / Statistics and Analytics

### Views متاحة للاستعلام / Available Views
- `language_usage_stats`: إحصائيات استخدام اللغات
- `daily_language_selections`: اختيارات اللغة اليومية
- `monthly_language_trends`: اتجاهات اللغة الشهرية

### Functions مفيدة / Useful Functions
- `get_language_statistics()`: إحصائيات شاملة للغات
- `update_user_language()`: تحديث لغة المستخدم
- `get_mod_description()`: الحصول على وصف المود حسب اللغة

## التثبيت والإعداد / Installation and Setup

### 1. قاعدة البيانات / Database
```sql
-- تشغيل ملفات SQL بالترتيب
-- Run SQL files in order:
\i database/user_language_stats.sql
\i database/add_arabic_description.sql
```

### 2. الملفات / Files
- تأكد من وجود جميع الملفات في المجلدات الصحيحة
- تحديث مسارات الملفات إذا لزم الأمر

### 3. الاختبار / Testing
1. احذف `localStorage` للتطبيق
2. افتح التطبيق - يجب أن تظهر صفحة اختيار اللغة
3. اختر لغة وتأكد من الحفظ الصحيح
4. أعد تحميل التطبيق - يجب أن يفتح مباشرة بالغة المحفوظة

## المميزات الإضافية / Additional Features

### 1. تبديل اللغة / Language Switching
- يمكن إضافة خيار تبديل اللغة في الإعدادات لاحقاً
- استخدام `translationManager.setLanguage('ar')` أو `translationManager.setLanguage('en')`

### 2. الترجمات المخصصة / Custom Translations
- إضافة ترجمات جديدة في ملف `translations.js`
- استخدام `t('key_name')` للوصول للترجمة

### 3. اتجاه النص / Text Direction
- تلقائياً RTL للعربية و LTR للإنجليزية
- تحديث `document.documentElement.dir` عند تغيير اللغة

## الصيانة والتطوير / Maintenance and Development

### إضافة ترجمات جديدة / Adding New Translations
1. أضف المفتاح والقيم في `translations.js`
2. استخدم `t('new_key')` في الكود
3. اختبر في كلا اللغتين

### تحديث قاعدة البيانات / Database Updates
- استخدم الـ Views المتاحة لمراقبة الإحصائيات
- تحديث الوصوفات العربية حسب الحاجة

### الأمان / Security
- جدول `user_language_stats` يحفظ معرف مستخدم مجهول فقط
- لا يتم حفظ معلومات شخصية حساسة

## الدعم / Support

للمساعدة أو الاستفسارات حول نظام اختيار اللغة، يرجى مراجعة:
- ملفات SQL في مجلد `database/`
- ملف الترجمات `translations.js`
- التوثيق في هذا الملف

For help or questions about the language selection system, please refer to:
- SQL files in `database/` folder
- Translation file `translations.js`
- Documentation in this file
