/* ========================================
   لوحة الإدارة الموحدة - التصميم الشامل
   Unified Admin Panel - Comprehensive Styling
   ======================================== */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    min-height: 100vh;
    direction: rtl;
}

/* Container */
.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.admin-header {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 2px solid #ffd700;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
}

.header-content h1 {
    font-size: 2.5rem;
    color: #ffd700;
    margin-bottom: 10px;
    text-align: center;
}

.header-content h1 i {
    margin-left: 15px;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.header-subtitle {
    text-align: center;
    color: #cccccc;
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.header-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    min-width: 120px;
}

.stat-item i {
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 5px;
    display: block;
}

.stat-item span {
    font-size: 1.8rem;
    font-weight: bold;
    color: #ffffff;
    display: block;
}

.stat-item small {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Navigation Tabs */
.admin-tabs {
    display: flex;
    background: rgba(26, 26, 46, 0.8);
    border-radius: 12px;
    padding: 5px;
    margin-bottom: 30px;
    overflow-x: auto;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    background: transparent;
    border: none;
    color: #cccccc;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 1rem;
    white-space: nowrap;
    min-width: 150px;
}

.tab-button:hover {
    background: rgba(255, 215, 0, 0.1);
    color: #ffd700;
}

.tab-button.active {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: #000000;
    font-weight: bold;
}

.tab-button i {
    margin-left: 8px;
}

/* Tab Content */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.section-header h2 {
    color: #ffd700;
    font-size: 1.8rem;
}

.section-header h2 i {
    margin-left: 10px;
}

/* Buttons */
.primary-btn, .secondary-btn, .quick-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.primary-btn {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: #000000;
    font-weight: bold;
}

.primary-btn:hover {
    background: linear-gradient(45deg, #ffcc00, #ffd700);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.secondary-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.secondary-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #ffd700;
}

.quick-btn {
    background: rgba(255, 215, 0, 0.1);
    color: #ffd700;
    border: 1px solid rgba(255, 215, 0, 0.3);
    width: 100%;
    justify-content: center;
    margin-bottom: 10px;
}

.quick-btn:hover {
    background: rgba(255, 215, 0, 0.2);
    transform: translateX(-5px);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.dashboard-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.dashboard-card h3 {
    color: #ffd700;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.dashboard-card h3 i {
    margin-left: 10px;
}

/* Management Grid */
.management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.management-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 215, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.management-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
    border-color: #ffd700;
}

.management-card .card-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.management-card h3 {
    color: #ffd700;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.management-card p {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.card-count {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #ffd700;
    color: #000000;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Banner Types */
.banner-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.banner-type-card, .dialog-type-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.banner-type-card:hover, .dialog-type-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
    border-color: #ffd700;
}

.type-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.banner-type-card h3, .dialog-type-card h3 {
    color: #ffd700;
    margin-bottom: 10px;
}

.banner-type-card p, .dialog-type-card p {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Subscription Stats */
.subscription-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.stat-icon {
    font-size: 2rem;
    color: #ffd700;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #ffffff;
    display: block;
}

.stat-label {
    color: #cccccc;
    font-size: 0.9rem;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.settings-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.settings-card h3 {
    color: #ffd700;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item label {
    color: #cccccc;
}

.status-indicator {
    color: #ffd700;
}

/* Lists */
.banners-list, .campaigns-list, .dialogs-list, .activity-list {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    min-height: 200px;
}

/* System Health */
.system-health {
    space-y: 15px;
}

.health-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.health-label {
    color: #cccccc;
}

.health-status {
    color: #ffd700;
}

/* Footer */
.admin-footer {
    margin-top: 50px;
    padding: 25px;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-links {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.footer-link {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-link:hover {
    color: #ffd700;
}

.footer-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
    color: #cccccc;
    font-size: 0.9rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: #ffffff;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 215, 0, 0.3);
    border-top: 3px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading indicator in lists */
.loading {
    text-align: center;
    color: #cccccc;
    padding: 40px;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        padding: 15px;
    }
    
    .header-stats {
        gap: 15px;
    }
    
    .stat-item {
        min-width: 100px;
        padding: 10px;
    }
    
    .admin-tabs {
        flex-direction: column;
    }
    
    .tab-button {
        min-width: auto;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .dashboard-grid,
    .management-grid,
    .banner-types,
    .subscription-stats,
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-info {
        align-items: center;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover Effects */
.hover-glow:hover {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

/* Success/Error States */
.success {
    color: #4CAF50 !important;
}

.error {
    color: #f44336 !important;
}

.warning {
    color: #ff9800 !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.7);
}

/* Card Styles for Lists */
.banner-card, .campaign-card, .dialog-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.banner-card:hover, .campaign-card:hover, .dialog-card:hover {
    border-color: #ffd700;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
    transform: translateY(-2px);
}

.banner-image {
    width: 120px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.banner-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-info, .campaign-info, .dialog-info {
    flex: 1;
}

.banner-info h4, .campaign-info h4, .dialog-info h4 {
    color: #ffd700;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.banner-info p, .campaign-info p, .dialog-info p {
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 10px;
    line-height: 1.4;
}

.banner-meta, .campaign-meta, .dialog-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.banner-type, .campaign-duration, .banner-status, .campaign-status, .dialog-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.banner-type, .campaign-duration {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
}

.banner-actions, .campaign-actions, .dialog-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.action-btn.edit {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.action-btn.edit:hover {
    background: rgba(34, 197, 94, 0.3);
    transform: scale(1.1);
}

.action-btn.delete {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.action-btn.delete:hover {
    background: rgba(239, 68, 68, 0.3);
    transform: scale(1.1);
}

/* Activity List */
.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.activity-content {
    flex: 1;
}

.activity-description {
    display: block;
    color: #ffffff;
    font-size: 0.95rem;
    margin-bottom: 5px;
}

.activity-time {
    color: #cccccc;
    font-size: 0.8rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    color: #cccccc;
    padding: 60px 20px;
    font-style: italic;
}

.empty-state::before {
    content: "📭";
    display: block;
    font-size: 3rem;
    margin-bottom: 15px;
}

/* Error State */
.error {
    text-align: center;
    color: #ef4444;
    padding: 40px 20px;
    font-weight: bold;
}

.error::before {
    content: "⚠️";
    display: block;
    font-size: 2rem;
    margin-bottom: 10px;
}

/* Quick Actions Grid */
.quick-actions {
    display: grid;
    gap: 10px;
}

/* Dialog Types Grid */
.dialog-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* Additional Responsive Adjustments */
@media (max-width: 600px) {
    .banner-card, .campaign-card, .dialog-card {
        flex-direction: column;
        text-align: center;
    }

    .banner-image {
        width: 100%;
        height: 120px;
    }

    .banner-actions, .campaign-actions, .dialog-actions {
        justify-content: center;
        width: 100%;
    }

    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .header-stats {
        flex-direction: column;
        align-items: center;
    }

    .stat-item {
        width: 100%;
        max-width: 200px;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    animation: slideInRight 0.3s ease-out;
    max-width: 300px;
}

.notification.success {
    background: linear-gradient(45deg, #22c55e, #16a34a);
}

.notification.error {
    background: linear-gradient(45deg, #ef4444, #dc2626);
}

.notification.warning {
    background: linear-gradient(45deg, #f59e0b, #d97706);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    border: 2px solid #ffd700;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-title {
    color: #ffd700;
    font-size: 1.5rem;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #cccccc;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

.modal-body {
    color: #ffffff;
    line-height: 1.6;
    margin-bottom: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    color: #ffd700;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-input, .form-textarea, .form-select {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #ffd700;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    transition: width 0.3s ease;
}

/* Tooltip */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* ========================================
   أنماط منشئ الاشتراك الموحد
   ======================================== */

/* Subscription Methods Grid */
.subscription-methods {
    margin-bottom: 30px;
}

.subscription-methods h3 {
    color: #ffd700;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

.methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.method-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 2px solid transparent;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.method-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.method-card:hover {
    border-color: #ffd700;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.2);
}

.method-card:hover::before {
    opacity: 1;
}

.method-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-card .method-icon { color: #22c55e; }
.advanced-card .method-icon { color: #3b82f6; }
.complete-card .method-icon { color: #f59e0b; }
.manage-card .method-icon { color: #8b5cf6; }
.popup-card .method-icon { color: #06b6d4; }
.dialog-card .method-icon { color: #ec4899; }
.backup-card .method-icon { color: #84cc16; }
.notification-card .method-icon { color: #f97316; }
.smart-card .method-icon { color: #a855f7; }

.method-card h4 {
    color: #ffd700;
    margin: 15px 0 10px 0;
    font-size: 1.1rem;
}

.method-card p {
    color: #ccc;
    margin-bottom: 15px;
    line-height: 1.5;
    font-size: 0.9rem;
}

/* Responsive Design for Methods Grid */
@media (max-width: 768px) {
    .methods-grid {
        grid-template-columns: 1fr;
    }

    .method-card {
        padding: 20px;
    }

    .method-icon {
        font-size: 2rem;
        height: 40px;
    }
}
