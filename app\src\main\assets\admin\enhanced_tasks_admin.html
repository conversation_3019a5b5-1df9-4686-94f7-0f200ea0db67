<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المهام المحسنة - Mod Etaris Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .header h1 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 3rem;
            text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 215, 0, 0.2);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }

        .stat-card .icon {
            font-size: 3rem;
            color: #ffd700;
            margin-bottom: 15px;
        }

        .stat-card .number {
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
        }

        .stat-card .label {
            color: #ccc;
            font-size: 1.1rem;
        }

        .campaigns-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .section-title {
            color: #ffd700;
            font-size: 1.8rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .campaigns-list {
            display: grid;
            gap: 20px;
        }

        .campaign-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            transition: all 0.3s ease;
        }

        .campaign-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.2);
        }

        .campaign-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .campaign-title {
            color: #ffd700;
            font-size: 1.3rem;
            font-weight: bold;
        }

        .campaign-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .status-active {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid #22c55e;
        }

        .status-inactive {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }

        .tasks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .task-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #ffd700;
            transition: all 0.3s ease;
        }

        .task-card:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        .task-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .task-icon {
            font-size: 1.5rem;
            margin-left: 10px;
            width: 30px;
        }

        .task-title {
            color: white;
            font-weight: bold;
            flex: 1;
        }

        .task-type {
            background: rgba(255, 215, 0, 0.2);
            color: #ffd700;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
        }

        .task-details {
            color: #ccc;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 15px;
        }

        .task-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .task-stat {
            text-align: center;
        }

        .task-stat-number {
            color: #ffd700;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .task-stat-label {
            color: #ccc;
            font-size: 0.8rem;
        }

        .task-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-success {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #ffd700;
            font-size: 1.2rem;
        }

        .empty-state {
            text-align: center;
            padding: 50px;
            color: #ccc;
        }

        .empty-state i {
            font-size: 4rem;
            color: #ffd700;
            margin-bottom: 20px;
        }

        .verification-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: bold;
            margin-right: 5px;
        }

        .verification-smart {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .verification-manual {
            background: rgba(249, 115, 22, 0.2);
            color: #f97316;
        }

        .platform-youtube { color: #ff0000; }
        .platform-telegram { color: #0088cc; }
        .platform-discord { color: #5865f2; }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .tasks-grid {
                grid-template-columns: 1fr;
            }
            
            .campaign-header {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .campaign-card, .task-card {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tasks"></i> إدارة المهام المحسنة</h1>
            <p>نظام إدارة المهام الذكي مع التحقق التلقائي للمنصات الثلاث</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon"><i class="fas fa-bullhorn"></i></div>
                <div class="number" id="totalCampaigns">0</div>
                <div class="label">الحملات النشطة</div>
            </div>
            <div class="stat-card">
                <div class="icon"><i class="fas fa-tasks"></i></div>
                <div class="number" id="totalTasks">0</div>
                <div class="label">إجمالي المهام</div>
            </div>
            <div class="stat-card">
                <div class="icon"><i class="fas fa-check-circle"></i></div>
                <div class="number" id="verifiedTasks">0</div>
                <div class="label">المهام المتحققة</div>
            </div>
            <div class="stat-card">
                <div class="icon"><i class="fas fa-percentage"></i></div>
                <div class="number" id="successRate">0%</div>
                <div class="label">معدل النجاح</div>
            </div>
        </div>

        <!-- قسم الحملات والمهام -->
        <div class="campaigns-section">
            <div class="section-title">
                <i class="fas fa-list"></i>
                <span>الحملات والمهام</span>
                <button class="btn btn-primary" onclick="refreshData()" style="margin-right: auto;">
                    <i class="fas fa-sync-alt"></i> تحديث
                </button>
            </div>
            
            <div id="campaignsContainer" class="campaigns-list">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...
                </div>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div style="text-align: center; margin-top: 30px;">
            <a href="subscription_admin.html" class="btn btn-secondary">
                <i class="fas fa-crown"></i> إدارة الحملات
            </a>
            <a href="index.html" class="btn btn-secondary">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </a>
            <a href="../index.html" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للتطبيق
            </a>
        </div>
    </div>

    <script src="enhanced_tasks_admin.js"></script>
</body>
</html>
