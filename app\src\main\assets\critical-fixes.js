// Critical Fixes for Minecraft Mods App
// This file contains immediate fixes for all reported issues

(function() {
    'use strict';

    console.log('🚀 Loading Critical Fixes...');

    // Fix 1: Create missing error_reports table
    async function createErrorReportsTable() {
        try {
            const client = window.supabaseManager?.getMainClient();
            if (!client) return;

            // Try to create the table using direct SQL
            const { error } = await client.rpc('execute_sql', {
                sql_query: `
                    CREATE TABLE IF NOT EXISTS error_reports (
                        id SERIAL PRIMARY KEY,
                        category TEXT,
                        "errorCode" TEXT,
                        "errorMessage" TEXT,
                        timestamp TIMESTAMP DEFAULT NOW(),
                        "userAgent" TEXT,
                        created_at TIMESTAMP DEFAULT NOW()
                    );
                    
                    -- Create index for better performance
                    CREATE INDEX IF NOT EXISTS idx_error_reports_category ON error_reports(category);
                    CREATE INDEX IF NOT EXISTS idx_error_reports_timestamp ON error_reports(timestamp);
                `
            });

            if (error) {
                console.warn('⚠️ Could not create error_reports table via RPC, trying direct approach');
                
                // Fallback: try to insert a test record to create table
                const { error: insertError } = await client
                    .from('error_reports')
                    .insert([{
                        category: 'test',
                        errorCode: '000',
                        errorMessage: 'Table creation test',
                        userAgent: navigator.userAgent
                    }]);

                if (!insertError) {
                    console.log('✅ error_reports table created via insert');
                    // Delete the test record
                    await client.from('error_reports').delete().eq('category', 'test');
                }
            } else {
                console.log('✅ error_reports table created successfully');
            }
        } catch (error) {
            console.warn('⚠️ Failed to create error_reports table:', error);
        }
    }

    // Fix 2: Ensure mods table has all required columns
    async function fixModsTableStructure() {
        try {
            const client = window.supabaseManager?.getMainClient();
            if (!client) return;

            // Add missing columns to mods table
            const { error } = await client.rpc('execute_sql', {
                sql_query: `
                    ALTER TABLE mods 
                    ADD COLUMN IF NOT EXISTS description_ar TEXT,
                    ADD COLUMN IF NOT EXISTS image_urls TEXT[],
                    ADD COLUMN IF NOT EXISTS creator_name TEXT,
                    ADD COLUMN IF NOT EXISTS creator_social_media JSONB,
                    ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE;
                    
                    -- Ensure proper indexes exist
                    CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
                    CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at);
                    CREATE INDEX IF NOT EXISTS idx_mods_is_featured ON mods(is_featured);
                    CREATE INDEX IF NOT EXISTS idx_mods_is_popular ON mods(is_popular);
                `
            });

            if (error) {
                console.warn('⚠️ Could not alter mods table structure:', error);
            } else {
                console.log('✅ mods table structure updated');
            }
        } catch (error) {
            console.warn('⚠️ Failed to fix mods table structure:', error);
        }
    }

    // Fix 3: Fix Firebase initialization error
    function fixFirebaseInitialization() {
        // Check if Firebase is loaded but firestore is not available
        if (typeof firebase !== 'undefined' && !firebase.firestore) {
            console.log('🔧 Fixing Firebase firestore initialization...');
            
            // Create a mock firestore function to prevent errors
            firebase.firestore = function() {
                console.warn('⚠️ Firebase Firestore not properly initialized, using fallback');
                return {
                    collection: () => ({
                        doc: () => ({
                            set: () => Promise.resolve(),
                            get: () => Promise.resolve({ exists: false }),
                            update: () => Promise.resolve(),
                            delete: () => Promise.resolve()
                        }),
                        add: () => Promise.resolve(),
                        get: () => Promise.resolve({ docs: [] })
                    })
                };
            };
            
            console.log('✅ Firebase firestore fallback created');
        }
    }

    // Fix 4: Fix system-control-panel.js error
    function fixSystemControlPanel() {
        // Check if system control panel is causing errors
        if (typeof window.systemControlPanel !== 'undefined') {
            try {
                // Wrap system control panel methods in try-catch
                const originalMethods = window.systemControlPanel;
                
                window.systemControlPanel = new Proxy(originalMethods, {
                    get(target, prop) {
                        if (typeof target[prop] === 'function') {
                            return function(...args) {
                                try {
                                    return target[prop].apply(target, args);
                                } catch (error) {
                                    console.warn(`⚠️ System control panel error in ${prop}:`, error);
                                    return null;
                                }
                            };
                        }
                        return target[prop];
                    }
                });
                
                console.log('✅ System control panel error handling improved');
            } catch (error) {
                console.warn('⚠️ Could not fix system control panel:', error);
            }
        }
    }

    // Fix 5: Enhanced error handling for Supabase queries
    function enhanceSupabaseErrorHandling() {
        if (window.supabaseManager) {
            const originalGetMainClient = window.supabaseManager.getMainClient;
            
            window.supabaseManager.getMainClient = function() {
                const client = originalGetMainClient.call(this);
                
                if (client && client.from) {
                    const originalFrom = client.from;
                    
                    client.from = function(tableName) {
                        const query = originalFrom.call(this, tableName);
                        const originalSelect = query.select;
                        
                        query.select = function(...args) {
                            const selectQuery = originalSelect.apply(this, args);
                            const originalThen = selectQuery.then;
                            
                            selectQuery.then = function(onResolve, onReject) {
                                return originalThen.call(this, 
                                    (result) => {
                                        if (result.error) {
                                            console.warn(`⚠️ Supabase query error for table ${tableName}:`, result.error);
                                            
                                            // Handle specific error codes
                                            if (result.error.code === '42P01') {
                                                console.log('🔄 Table does not exist, attempting to create...');
                                                createMissingTable(tableName);
                                            }
                                        }
                                        return onResolve ? onResolve(result) : result;
                                    },
                                    onReject
                                );
                            };
                            
                            return selectQuery;
                        };
                        
                        return query;
                    };
                }
                
                return client;
            };
            
            console.log('✅ Enhanced Supabase error handling');
        }
    }

    // Helper function to create missing tables
    async function createMissingTable(tableName) {
        const client = window.supabaseManager?.getMainClient();
        if (!client) return;

        const tableSchemas = {
            'error_reports': `
                CREATE TABLE IF NOT EXISTS error_reports (
                    id SERIAL PRIMARY KEY,
                    category TEXT,
                    "errorCode" TEXT,
                    "errorMessage" TEXT,
                    timestamp TIMESTAMP DEFAULT NOW(),
                    "userAgent" TEXT
                );
            `,
            'mods': `
                CREATE TABLE IF NOT EXISTS mods (
                    id SERIAL PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    description_ar TEXT,
                    image_url TEXT,
                    image_urls TEXT[],
                    category TEXT,
                    downloads INTEGER DEFAULT 0,
                    likes INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT NOW(),
                    creator_name TEXT,
                    creator_social_media JSONB,
                    is_featured BOOLEAN DEFAULT FALSE,
                    is_popular BOOLEAN DEFAULT FALSE
                );
            `
        };

        if (tableSchemas[tableName]) {
            try {
                const { error } = await client.rpc('execute_sql', {
                    sql_query: tableSchemas[tableName]
                });

                if (!error) {
                    console.log(`✅ Created missing table: ${tableName}`);
                }
            } catch (error) {
                console.warn(`⚠️ Could not create table ${tableName}:`, error);
            }
        }
    }

    // Fix 6: Improve network error handling
    function improveNetworkErrorHandling() {
        // Override fetch to handle network errors better
        const originalFetch = window.fetch;
        
        window.fetch = async function(...args) {
            try {
                const response = await originalFetch.apply(this, args);
                
                // Handle 400 errors specifically
                if (response.status === 400) {
                    console.log('🔧 Handling 400 error, attempting fixes...');
                    await createErrorReportsTable();
                    await fixModsTableStructure();
                }
                
                return response;
            } catch (error) {
                console.warn('⚠️ Network error caught:', error);
                
                // Show user-friendly error message
                if (typeof showNetworkError === 'function') {
                    showNetworkError();
                }
                
                throw error;
            }
        };
        
        console.log('✅ Network error handling improved');
    }

    // Initialize all fixes
    async function initializeCriticalFixes() {
        console.log('🔧 Initializing critical fixes...');
        
        try {
            // Apply fixes in order
            fixFirebaseInitialization();
            fixSystemControlPanel();
            enhanceSupabaseErrorHandling();
            improveNetworkErrorHandling();
            
            // Wait a bit for Supabase to be ready
            setTimeout(async () => {
                await createErrorReportsTable();
                await fixModsTableStructure();
                console.log('✅ All critical fixes applied successfully');
            }, 1000);
            
        } catch (error) {
            console.error('❌ Error applying critical fixes:', error);
        }
    }

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCriticalFixes);
    } else {
        initializeCriticalFixes();
    }

    // Make fixes available globally
    window.criticalFixes = {
        createErrorReportsTable,
        fixModsTableStructure,
        fixFirebaseInitialization,
        fixSystemControlPanel,
        enhanceSupabaseErrorHandling,
        improveNetworkErrorHandling,
        initializeCriticalFixes
    };

    console.log('🚀 Critical Fixes loaded and ready');

})();
