#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب قواعد البيانات والتبديل التلقائي
Database Monitor and Auto-Failover System

المميزات:
- مراقبة مستمرة لحالة قواعد البيانات
- كشف الأعطال والمشاكل
- التبديل التلقائي عند الحاجة
- إرسال تنبيهات عند المشاكل
"""

import asyncio
import logging
import json
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from database_backup_manager import DatabaseBackupManager

# تكوين نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseMonitor:
    """مراقب قواعد البيانات"""
    
    def __init__(self, config_path: str = "backup_config.json"):
        """تهيئة المراقب"""
        self.backup_manager = DatabaseBackupManager(config_path)
        self.config = self.backup_manager.config
        self.monitoring_config = self.config.get("monitoring", {})
        
        # حالة المراقبة
        self.database_status = {}
        self.failed_checks = {}
        self.last_check_time = {}
        self.current_primary_db = "main"
        self.is_monitoring = False
        
        # إعدادات المراقبة
        self.health_check_interval = self.monitoring_config.get("health_check_interval", 300)
        self.max_failed_checks = self.monitoring_config.get("max_failed_checks", 3)
        self.auto_failover_enabled = self.monitoring_config.get("auto_failover_enabled", True)
    
    async def start_monitoring(self):
        """بدء مراقبة قواعد البيانات"""
        logger.info("🔍 بدء مراقبة قواعد البيانات")
        self.is_monitoring = True
        
        # تهيئة حالة قواعد البيانات
        for db_name in self.backup_manager.supabase_clients.keys():
            self.database_status[db_name] = "unknown"
            self.failed_checks[db_name] = 0
            self.last_check_time[db_name] = None
        
        # بدء حلقة المراقبة
        while self.is_monitoring:
            try:
                await self.check_all_databases()
                await self.evaluate_failover_need()
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"❌ خطأ في حلقة المراقبة: {e}")
                await asyncio.sleep(60)  # انتظار دقيقة قبل المحاولة مرة أخرى
    
    async def check_all_databases(self):
        """فحص جميع قواعد البيانات"""
        logger.info("🔍 فحص حالة جميع قواعد البيانات")
        
        for db_name in self.backup_manager.supabase_clients.keys():
            await self.check_database_health(db_name)
    
    async def check_database_health(self, db_name: str) -> Dict:
        """فحص حالة قاعدة بيانات واحدة"""
        check_result = {
            "database": db_name,
            "timestamp": datetime.now().isoformat(),
            "status": "unknown",
            "response_time": None,
            "error": None,
            "details": {}
        }
        
        try:
            start_time = time.time()
            
            # اختبار الاتصال الأساسي
            is_connected = await self.backup_manager.test_database_connection(db_name)
            
            if is_connected:
                # اختبارات إضافية
                client = self.backup_manager.supabase_clients[db_name]
                
                # اختبار عدد السجلات
                response = client.table('mods').select('count', count='exact', head=True).execute()
                check_result["details"]["mods_count"] = response.count
                
                # اختبار سرعة الاستجابة
                response_time = time.time() - start_time
                check_result["response_time"] = round(response_time, 3)
                
                # تحديد الحالة بناءً على سرعة الاستجابة
                if response_time < 2.0:
                    check_result["status"] = "healthy"
                elif response_time < 5.0:
                    check_result["status"] = "slow"
                else:
                    check_result["status"] = "degraded"
                
                # إعادة تعيين عداد الفشل
                self.failed_checks[db_name] = 0
                
            else:
                check_result["status"] = "failed"
                check_result["error"] = "فشل في الاتصال"
                self.failed_checks[db_name] += 1
            
        except Exception as e:
            check_result["status"] = "error"
            check_result["error"] = str(e)
            self.failed_checks[db_name] += 1
            logger.warning(f"⚠️ خطأ في فحص قاعدة البيانات {db_name}: {e}")
        
        # تحديث الحالة
        self.database_status[db_name] = check_result["status"]
        self.last_check_time[db_name] = datetime.now()
        
        # تسجيل النتيجة
        if check_result["status"] in ["healthy", "slow"]:
            logger.info(f"✅ {db_name}: {check_result['status']} ({check_result['response_time']}s)")
        else:
            logger.warning(f"⚠️ {db_name}: {check_result['status']} - {check_result.get('error', 'غير معروف')}")
        
        return check_result
    
    async def evaluate_failover_need(self):
        """تقييم الحاجة للتبديل التلقائي"""
        if not self.auto_failover_enabled:
            return
        
        current_db_status = self.database_status.get(self.current_primary_db, "unknown")
        current_failed_checks = self.failed_checks.get(self.current_primary_db, 0)
        
        # التحقق من حاجة التبديل
        if (current_db_status in ["failed", "error"] and 
            current_failed_checks >= self.max_failed_checks):
            
            logger.warning(f"🚨 قاعدة البيانات الرئيسية {self.current_primary_db} تحتاج تبديل")
            
            # البحث عن قاعدة بيانات احتياطية صحية
            backup_db = await self.find_healthy_backup_database()
            
            if backup_db:
                await self.perform_failover(self.current_primary_db, backup_db)
            else:
                logger.error("❌ لا توجد قاعدة بيانات احتياطية صحية متاحة")
                await self.send_critical_alert("لا توجد قاعدة بيانات احتياطية متاحة")
    
    async def find_healthy_backup_database(self) -> Optional[str]:
        """البحث عن قاعدة بيانات احتياطية صحية"""
        for db_name, status in self.database_status.items():
            if (db_name != self.current_primary_db and 
                status == "healthy" and 
                self.failed_checks.get(db_name, 0) == 0):
                return db_name
        
        return None
    
    async def perform_failover(self, failed_db: str, backup_db: str):
        """تنفيذ التبديل التلقائي"""
        logger.warning(f"🔄 بدء التبديل التلقائي من {failed_db} إلى {backup_db}")
        
        try:
            # إنشاء نسخة احتياطية طارئة إذا أمكن
            emergency_backup = None
            try:
                if self.database_status.get(failed_db) != "failed":
                    logger.info("💾 إنشاء نسخة احتياطية طارئة...")
                    emergency_backup = await self.backup_manager.create_full_backup(failed_db)
            except Exception as e:
                logger.warning(f"⚠️ فشل في إنشاء النسخة الاحتياطية الطارئة: {e}")
            
            # نقل البيانات إلى قاعدة البيانات الاحتياطية
            if emergency_backup:
                transfer_result = await self.backup_manager.transfer_database(
                    failed_db, backup_db, emergency_backup["backup_id"]
                )
            else:
                # استخدام أحدث نسخة احتياطية متاحة
                latest_backup = await self.backup_manager.get_latest_backup(failed_db)
                if latest_backup:
                    transfer_result = await self.backup_manager.transfer_database(
                        failed_db, backup_db, latest_backup["backup_id"]
                    )
                else:
                    raise Exception("لا توجد نسخة احتياطية متاحة")
            
            # تحديث قاعدة البيانات الرئيسية
            old_primary = self.current_primary_db
            self.current_primary_db = backup_db
            
            # تحديث إعدادات التطبيق
            await self.backup_manager.update_app_config(backup_db)
            
            # إرسال تنبيه نجاح
            await self.send_failover_success_alert(old_primary, backup_db, transfer_result)
            
            logger.info(f"✅ تم التبديل التلقائي بنجاح من {old_primary} إلى {backup_db}")
            
        except Exception as e:
            logger.error(f"❌ فشل في التبديل التلقائي: {e}")
            await self.send_critical_alert(f"فشل في التبديل التلقائي: {e}")
    
    async def send_failover_success_alert(self, old_db: str, new_db: str, transfer_result: Dict):
        """إرسال تنبيه نجاح التبديل"""
        message = f"""
🔄 تم التبديل التلقائي بنجاح

📊 التفاصيل:
• من: {old_db}
• إلى: {new_db}
• السجلات المنقولة: {transfer_result.get('total_transferred', 0)}
• الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✅ التطبيق يعمل الآن على قاعدة البيانات الجديدة
        """
        
        await self.send_notification(message, "success")
    
    async def send_critical_alert(self, message: str):
        """إرسال تنبيه حرج"""
        alert_message = f"""
🚨 تنبيه حرج - نظام قواعد البيانات

⚠️ المشكلة: {message}
🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 يتطلب تدخل فوري

📊 حالة قواعد البيانات:
{self.get_status_summary()}
        """
        
        await self.send_notification(alert_message, "critical")
    
    async def send_notification(self, message: str, level: str = "info"):
        """إرسال تنبيه"""
        try:
            # إرسال عبر Webhook إذا كان متاحاً
            webhook_url = self.monitoring_config.get("notification_webhook")
            if webhook_url:
                payload = {
                    "text": message,
                    "level": level,
                    "timestamp": datetime.now().isoformat()
                }
                
                response = requests.post(webhook_url, json=payload, timeout=10)
                if response.status_code == 200:
                    logger.info("📧 تم إرسال التنبيه بنجاح")
                else:
                    logger.warning(f"⚠️ فشل في إرسال التنبيه: {response.status_code}")
            
            # إرسال عبر البريد الإلكتروني إذا كان مفعلاً
            email_config = self.monitoring_config.get("email_notifications", {})
            if email_config.get("enabled", False):
                await self.send_email_notification(message, level, email_config)
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال التنبيه: {e}")
    
    async def send_email_notification(self, message: str, level: str, email_config: Dict):
        """إرسال تنبيه عبر البريد الإلكتروني"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # إعداد الرسالة
            msg = MIMEMultipart()
            msg['From'] = email_config['username']
            msg['Subject'] = f"تنبيه نظام قواعد البيانات - {level.upper()}"
            
            # إضافة المحتوى
            msg.attach(MIMEText(message, 'plain', 'utf-8'))
            
            # إرسال لجميع المستقبلين
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            
            for recipient in email_config['recipients']:
                msg['To'] = recipient
                server.send_message(msg)
                del msg['To']
            
            server.quit()
            logger.info("📧 تم إرسال التنبيه عبر البريد الإلكتروني")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال البريد الإلكتروني: {e}")
    
    def get_status_summary(self) -> str:
        """الحصول على ملخص حالة قواعد البيانات"""
        summary = []
        for db_name, status in self.database_status.items():
            failed_count = self.failed_checks.get(db_name, 0)
            last_check = self.last_check_time.get(db_name)
            
            status_icon = {
                "healthy": "✅",
                "slow": "🟡", 
                "degraded": "🟠",
                "failed": "❌",
                "error": "💥",
                "unknown": "❓"
            }.get(status, "❓")
            
            line = f"{status_icon} {db_name}: {status}"
            if failed_count > 0:
                line += f" (فشل {failed_count} مرة)"
            if last_check:
                line += f" - آخر فحص: {last_check.strftime('%H:%M:%S')}"
            
            summary.append(line)
        
        return "\n".join(summary)
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        logger.info("⏹️ إيقاف مراقبة قواعد البيانات")
        self.is_monitoring = False
    
    def get_monitoring_report(self) -> Dict:
        """الحصول على تقرير المراقبة"""
        return {
            "current_primary_db": self.current_primary_db,
            "monitoring_active": self.is_monitoring,
            "database_status": self.database_status,
            "failed_checks": self.failed_checks,
            "last_check_times": {
                db: time.isoformat() if time else None 
                for db, time in self.last_check_time.items()
            },
            "auto_failover_enabled": self.auto_failover_enabled,
            "health_check_interval": self.health_check_interval,
            "max_failed_checks": self.max_failed_checks
        }


if __name__ == "__main__":
    import sys
    
    async def main():
        monitor = DatabaseMonitor()
        
        if len(sys.argv) > 1 and sys.argv[1] == "start":
            try:
                await monitor.start_monitoring()
            except KeyboardInterrupt:
                logger.info("⏹️ تم إيقاف المراقبة بواسطة المستخدم")
                monitor.stop_monitoring()
        else:
            # فحص سريع لجميع قواعد البيانات
            await monitor.check_all_databases()
            report = monitor.get_monitoring_report()
            
            print("📊 تقرير حالة قواعد البيانات:")
            print(f"قاعدة البيانات الرئيسية: {report['current_primary_db']}")
            print(f"حالة قواعد البيانات:")
            print(monitor.get_status_summary())
    
    asyncio.run(main())
