/* Enhanced Admin Features Styles */
/* تصميم الميزات المحسنة للأدمن */

/* ========================================
   Interactive Dashboard Styles
   ======================================== */

.dashboard-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.auto-refresh-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
    cursor: pointer;
}

.auto-refresh-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border: 2px solid #ffcc00;
    border-radius: 15px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 204, 0, 0.3);
}

.metric-card.metric-updated {
    animation: metricPulse 1s ease;
}

@keyframes metricPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.metric-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #000;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #ffcc00;
    font-family: 'VT323', monospace;
}

.metric-label {
    color: #ffffff;
    opacity: 0.9;
    font-size: 0.9rem;
}

.dashboard-status {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.status-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #ffffff;
    font-size: 0.9rem;
}

.auto-refresh-status.active {
    color: #22c55e;
}

.auto-refresh-status.inactive {
    color: #ef4444;
}

/* ========================================
   Permissions Management Styles
   ======================================== */

.permissions-dashboard {
    padding: 20px;
}

.current-user-info {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #444;
}

.user-role-display {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 10px;
}

.role-badge {
    background: #ffcc00;
    color: #000;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.permission-denied-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.permission-denied-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
}

.permission-denied-content {
    position: relative;
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border: 2px solid #ef4444;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
}

.permission-denied-icon {
    font-size: 3rem;
    color: #ef4444;
    margin-bottom: 15px;
}

.permission-denied-close {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    cursor: pointer;
    margin-top: 15px;
}

.role-management-modal .role-list {
    max-height: 400px;
    overflow-y: auto;
}

.role-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.role-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.role-name {
    font-weight: bold;
    color: #ffcc00;
}

.role-key {
    color: #888;
    font-size: 0.8rem;
}

.role-permissions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.permission-tag {
    background: rgba(255, 204, 0, 0.2);
    color: #ffcc00;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
}

/* ========================================
   Backup System Styles
   ======================================== */

.backup-dashboard {
    padding: 20px;
}

.backup-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.backup-settings {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #444;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.backup-history {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #444;
}

.backup-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.backup-item.completed {
    border-left: 4px solid #22c55e;
}

.backup-item.failed {
    border-left: 4px solid #ef4444;
}

.backup-info {
    flex: 1;
}

.backup-header {
    display: flex;
    gap: 15px;
    margin-bottom: 5px;
}

.backup-id {
    font-family: monospace;
    color: #ffcc00;
}

.backup-type {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
}

.backup-type.manual {
    background: #3b82f6;
    color: white;
}

.backup-type.automatic {
    background: #10b981;
    color: white;
}

.backup-status.completed {
    color: #22c55e;
}

.backup-status.failed {
    color: #ef4444;
}

.backup-actions {
    display: flex;
    gap: 8px;
}

.backup-action-btn {
    background: transparent;
    border: 1px solid #666;
    border-radius: 6px;
    padding: 6px 10px;
    color: #ffffff;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.backup-action-btn:hover {
    border-color: #ffcc00;
    color: #ffcc00;
}

.backup-action-btn.restore {
    border-color: #22c55e;
    color: #22c55e;
}

.backup-action-btn.download {
    border-color: #3b82f6;
    color: #3b82f6;
}

.backup-action-btn.delete {
    border-color: #ef4444;
    color: #ef4444;
}

/* ========================================
   Performance Monitor Styles
   ======================================== */

.monitor-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.monitoring-toggle {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.monitoring-toggle.start {
    background: #22c55e;
    color: white;
}

.monitoring-toggle.stop {
    background: #ef4444;
    color: white;
}

.monitoring-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.monitoring-status.active {
    background: #22c55e;
    color: white;
}

.monitoring-status.inactive {
    background: #ef4444;
    color: white;
}

.performance-dashboard {
    padding: 20px;
}

.performance-metrics {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #444;
}

.metric-group h3 {
    color: #ffcc00;
    margin-bottom: 15px;
}

.system-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.system-metric {
    display: flex;
    align-items: center;
    gap: 15px;
}

.system-metric label {
    color: #ffffff;
    min-width: 80px;
}

.system-metric-bar {
    flex: 1;
    height: 20px;
    background: #1e1e2e;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #444;
}

.system-metric-bar.good {
    background: linear-gradient(45deg, #22c55e, #16a34a);
}

.system-metric-bar.warning {
    background: linear-gradient(45deg, #f59e0b, #d97706);
}

.system-metric-bar.critical {
    background: linear-gradient(45deg, #ef4444, #dc2626);
}

.system-metric-value {
    color: #ffcc00;
    font-weight: bold;
    min-width: 50px;
    text-align: right;
}

.performance-alerts {
    margin-top: 20px;
}

.performance-alert {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-left: 4px solid #ef4444;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.performance-alert .alert-icon {
    color: #ef4444;
    font-size: 1.5rem;
}

.performance-alert .alert-content h4 {
    color: #ef4444;
    margin: 0 0 5px 0;
}

.performance-alert .alert-content p {
    color: #ffffff;
    margin: 0;
    opacity: 0.9;
}

.performance-alert .alert-close {
    background: transparent;
    border: none;
    color: #888;
    cursor: pointer;
    font-size: 1.2rem;
    margin-right: auto;
}

/* ========================================
   Audit System Styles
   ======================================== */

.audit-dashboard {
    padding: 20px;
}

.audit-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.audit-filters {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #444;
}

.filter-group {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group input,
.filter-group select {
    background: #1e1e2e;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 8px 12px;
    color: #ffffff;
}

.filter-group input:focus,
.filter-group select:focus {
    border-color: #ffcc00;
    outline: none;
}

.audit-logs {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #444;
    max-height: 600px;
    overflow-y: auto;
}

.audit-log-entry {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.audit-log-entry:hover {
    background: rgba(255, 204, 0, 0.1);
}

.audit-log-entry.info {
    border-left-color: #3b82f6;
}

.audit-log-entry.warn {
    border-left-color: #f59e0b;
}

.audit-log-entry.error {
    border-left-color: #ef4444;
}

.audit-log-entry.debug {
    border-left-color: #8b5cf6;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.log-timestamp {
    color: #888;
    font-size: 0.8rem;
}

.log-level {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.log-level.info {
    background: #3b82f6;
    color: white;
}

.log-level.warn {
    background: #f59e0b;
    color: white;
}

.log-level.error {
    background: #ef4444;
    color: white;
}

.log-level.debug {
    background: #8b5cf6;
    color: white;
}

.log-category {
    background: rgba(255, 204, 0, 0.2);
    color: #ffcc00;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
}

.log-description {
    color: #ffffff;
    margin-bottom: 8px;
}

.log-meta {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: #888;
}

.log-error-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 0.8rem;
}

.audit-log-modal .log-detail-grid {
    display: grid;
    gap: 15px;
}

.detail-item {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 10px;
    align-items: start;
}

.detail-item.full-width {
    grid-column: 1 / -1;
    display: block;
}

.detail-item label {
    color: #ffcc00;
    font-weight: bold;
}

.detail-item span {
    color: #ffffff;
}

.small-text {
    font-size: 0.8rem;
    word-break: break-all;
}

.details-json {
    background: #1e1e2e;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    color: #ffffff;
    font-size: 0.8rem;
    overflow-x: auto;
}

/* ========================================
   Advanced Settings Styles
   ======================================== */

.settings-dashboard {
    padding: 20px;
}

.settings-navigation {
    margin-bottom: 30px;
}

.settings-tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    border-bottom: 2px solid #444;
    padding-bottom: 15px;
}

.settings-tab {
    background: transparent;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 10px 15px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-tab:hover {
    border-color: #ffcc00;
    color: #ffcc00;
}

.settings-tab.active {
    background: #ffcc00;
    color: #000;
    border-color: #ffcc00;
}

.settings-forms {
    min-height: 400px;
}

.settings-form {
    display: none;
}

.settings-form.active {
    display: block;
}

.settings-form-header {
    margin-bottom: 30px;
}

.settings-form-header h3 {
    color: #ffcc00;
    margin-bottom: 10px;
}

.settings-form-header p {
    color: #888;
}

.settings-form-body {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.setting-field {
    display: grid;
    grid-template-columns: 1fr 200px;
    gap: 20px;
    align-items: start;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.setting-label label {
    color: #ffcc00;
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.setting-description {
    color: #888;
    font-size: 0.8rem;
}

.setting-control {
    display: flex;
    align-items: center;
}

.setting-input {
    background: #1e1e2e;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 8px 12px;
    color: #ffffff;
    width: 100%;
}

.setting-input:focus {
    border-color: #ffcc00;
    outline: none;
}

.setting-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.setting-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #444;
    transition: 0.4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #ffcc00;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.settings-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #444;
}

/* ========================================
   Media Manager Styles
   ======================================== */

.media-dashboard {
    padding: 20px;
}

.media-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    border: 1px solid #444;
}

.media-actions {
    display: flex;
    gap: 10px;
}

.media-info {
    display: flex;
    gap: 15px;
    color: #ffffff;
    font-size: 0.9rem;
}

.media-navigation {
    margin-bottom: 20px;
}

.folder-navigation {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.folder-btn {
    background: transparent;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 8px 12px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.folder-btn:hover {
    border-color: #ffcc00;
    color: #ffcc00;
}

.folder-btn.active {
    background: #ffcc00;
    color: #000;
    border-color: #ffcc00;
}

.media-content {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #444;
    min-height: 400px;
}

.media-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.media-file-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid transparent;
    border-radius: 15px;
    padding: 15px;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.media-file-card:hover {
    border-color: #ffcc00;
    transform: translateY(-5px);
}

.media-file-card.selected {
    border-color: #ffcc00;
    background: rgba(255, 204, 0, 0.1);
}

.file-thumbnail {
    width: 100%;
    height: 120px;
    background: #1e1e2e;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    overflow: hidden;
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
}

.file-thumbnail i {
    font-size: 2rem;
    color: #666;
}

.file-info {
    margin-bottom: 10px;
}

.file-name {
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #888;
    margin-bottom: 5px;
}

.file-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #888;
}

.file-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.file-action-btn {
    background: transparent;
    border: 1px solid #666;
    border-radius: 6px;
    padding: 6px 8px;
    color: #ffffff;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.file-action-btn:hover {
    border-color: #ffcc00;
    color: #ffcc00;
}

.file-action-btn.danger:hover {
    border-color: #ef4444;
    color: #ef4444;
}

.file-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
}

.file-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.media-drop-zone {
    border: 2px dashed #666;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    margin-top: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.media-drop-zone:hover,
.media-drop-zone.drag-over {
    border-color: #ffcc00;
    background: rgba(255, 204, 0, 0.1);
}

.media-drop-zone i {
    font-size: 3rem;
    color: #ffcc00;
    margin-bottom: 10px;
}

.media-drop-zone p {
    color: #ffffff;
    margin: 0;
}

/* ========================================
   Responsive Design for New Features
   ======================================== */

@media (max-width: 768px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .backup-controls {
        flex-direction: column;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-tabs {
        flex-direction: column;
    }
    
    .setting-field {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .media-toolbar {
        flex-direction: column;
        gap: 15px;
    }
    
    .media-actions {
        width: 100%;
        justify-content: center;
    }
    
    .folder-navigation {
        justify-content: center;
    }
    
    .media-files-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .audit-controls {
        flex-direction: column;
    }
    
    .monitor-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}
