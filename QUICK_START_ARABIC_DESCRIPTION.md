# دليل البدء السريع - إنشاء الوصف العربي
# Quick Start Guide - Arabic Description Generation

## التحضير السريع / Quick Setup

### 1. تحديث قاعدة البيانات / Database Update
```sql
-- تشغيل هذا الأمر في Supabase SQL Editor
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;

-- التحقق من إضافة العمود
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'mods' AND column_name = 'description_ar';
```

### 2. تشغيل الأداة / Run the Tool
```bash
cd "app/src/main/assets/send addons"
python mod_processor.py
```

## الاستخدام السريع / Quick Usage

### 🚀 الطريقة الأولى: استخراج مقال + إنشاء وصف عربي

#### 1. استخراج المقال:
- **الصق رابط المقال** في حقل "رابط صفحة المود"
- **انقر "استخراج المقال"**
- **انتظر** حتى يتم استخراج البيانات

#### 2. إنشاء الوصف العربي:
- **انقر "إنشاء وصف عربي"** في قسم الذكاء الاصطناعي
- **انتظر** (10-30 ثانية) حتى يتم إنشاء الوصف
- **راجع الوصف** في حقل "الوصف العربي"

#### 3. النشر:
- **راجع جميع الحقول** (الاسم، الفئة، الصور، إلخ)
- **انقر "نشر المود الآن"**
- **اختر** ما إذا كنت تريد تفريغ الحقول بعد النشر

### 🖊️ الطريقة الثانية: إدخال يدوي + إنشاء وصف عربي

#### 1. إدخال البيانات يدوياً:
- **اكتب ميزات المود** في حقل "ميزات المود"
- **املأ الحقول الأساسية** (الاسم، الفئة، إلخ)

#### 2. إنشاء الوصف العربي:
- **انقر "إنشاء وصف عربي"**
- **انتظر** حتى يتم إنشاء الوصف
- **عدّل الوصف** حسب الحاجة

#### 3. النشر:
- **تأكد من اكتمال جميع الحقول**
- **انقر "نشر المود الآن"**

## الأزرار الجديدة / New Buttons

### في قسم الذكاء الاصطناعي:
- **🤖 "إنشاء وصف عربي"** - ينشئ وصف عربي جديد
- **📋 "نسخ الوصف العربي"** - ينسخ الوصف المُنشأ

### في قسم النشر:
- **📝 حقل "الوصف العربي"** - مع أزرار (لصق، نسخ، مسح)

## نصائح للاستخدام الأمثل / Best Practices

### ✅ للحصول على أفضل النتائج:

#### 1. **قدم سياق كافي:**
```
مثال جيد لميزات المود:
- يضيف 50+ كتلة جديدة
- يتضمن أدوات سحرية قوية  
- يحتوي على مخلوقات جديدة
- متوافق مع الإصدار 1.20+
- يدعم اللعب الجماعي
```

#### 2. **استخدم أسماء واضحة:**
```
✅ جيد: "Dragon Craft Mod v2.1"
❌ سيء: "mod123"
```

#### 3. **اختر الفئة الصحيحة:**
- Addons - للإضافات العامة
- Shaders - للشيدرز
- Texture Pack - لحزم النسيج
- Maps - للخرائط
- Seeds - للبذور

### ⚠️ تجنب هذه الأخطاء:

#### 1. **لا تترك الحقول فارغة:**
- تأكد من وجود ميزات أو نص مستخرج
- املأ الحقول الأساسية قبل النشر

#### 2. **لا تنقر الأزرار بسرعة:**
- انتظر حتى ينتهي الذكاء الاصطناعي
- لا تنقر "إنشاء وصف عربي" عدة مرات

#### 3. **لا تنس مراجعة الوصف:**
- اقرأ الوصف المُنشأ
- عدّل أي أخطاء أو إضافات مطلوبة

## استكشاف الأخطاء السريع / Quick Troubleshooting

### 🔧 مشاكل شائعة وحلولها:

#### المشكلة: "Gemini Error"
```
الحل:
1. تحقق من اتصال الإنترنت
2. تأكد من تكوين مفاتيح Gemini API
3. أعد تشغيل الأداة
```

#### المشكلة: "Input Required"
```
الحل:
1. أدخل ميزات المود في الحقل المخصص
2. أو استخرج مقال أولاً
3. تأكد من وجود محتوى للعمل عليه
```

#### المشكلة: الوصف لا يظهر
```
الحل:
1. انتظر أكثر (قد يستغرق 30 ثانية)
2. تحقق من رسائل الحالة في الأسفل
3. جرب مرة أخرى مع محتوى أقل
```

#### المشكلة: خطأ في النشر
```
الحل:
1. تحقق من إضافة عمود description_ar في قاعدة البيانات
2. تأكد من اتصال Supabase
3. راجع جميع الحقول المطلوبة
```

## أمثلة عملية / Practical Examples

### 📋 مثال كامل - نشر مود تنانين:

#### 1. البيانات الأساسية:
```
الاسم: Dragon Craft Ultimate
الفئة: Addons
الإصدار: 2.1.0
```

#### 2. الميزات:
```
- يضيف 5 أنواع مختلفة من التنانين
- تنانين قابلة للترويض والركوب
- أسلحة ودروع التنانين الأسطورية
- كهوف التنانين مع كنوز نادرة
- تأثيرات بصرية مذهلة للنار والطيران
- متوافق مع Minecraft PE 1.20+
```

#### 3. الوصف العربي المُنشأ (مثال):
```
استعد لمغامرة أسطورية مع مود Dragon Craft Ultimate الذي يجلب عالم التنانين 
الساحر إلى ماين كرافت! يتيح لك هذا المود ترويض خمسة أنواع مختلفة من التنانين 
القوية والركوب عليها لاستكشاف العالم من السماء. 

اكتشف كهوف التنانين المليئة بالكنوز النادرة، واحصل على أسلحة ودروع التنانين 
الأسطورية التي تمنحك قوى خارقة. مع التأثيرات البصرية المذهلة للنار والطيران، 
ستعيش تجربة لعب لا تُنسى مليئة بالإثارة والمغامرة.
```

### 🎮 مثال - مود شيدرز:

#### البيانات:
```
الاسم: Realistic Water Shaders
الفئة: Shaders
الميزات: تحسين واقعية المياه، انعكاسات ديناميكية، تأثيرات الأمواج
```

#### الوصف المُنشأ:
```
حوّل مياه ماين كرافت إلى تحفة فنية واقعية مع شيدر Realistic Water Shaders! 
يقدم هذا الشيدر انعكاسات ديناميكية مذهلة وتأثيرات أمواج طبيعية تجعل المياه 
تبدو وكأنها حقيقية. استمتع بتجربة بصرية غامرة مع تحسينات الإضاءة والظلال 
التي تضفي جمالاً استثنائياً على عوالمك المائية.
```

## الدعم والمساعدة / Support & Help

### 📞 إذا واجهت مشاكل:

#### 1. **تحقق من ملفات السجل:**
- راقب رسائل الحالة في أسفل الأداة
- ابحث عن رسائل الخطأ باللون الأحمر

#### 2. **جرب خطوات بديلة:**
- استخدم ميزات أقل في المرة الواحدة
- جرب مع مود أبسط أولاً
- أعد تشغيل الأداة

#### 3. **تحقق من المتطلبات:**
- Python مثبت بشكل صحيح
- جميع المكتبات متوفرة
- اتصال إنترنت مستقر

## الخلاصة / Summary

### ✅ ما تم إنجازه:
- **ميزة إنشاء الوصف العربي** جاهزة للاستخدام
- **تكامل كامل** مع أداة النشر الموجودة
- **واجهة سهلة الاستخدام** مع أزرار واضحة
- **جودة عالية** للوصوفات المُنشأة

### 🚀 الخطوات التالية:
1. **جرب الميزة** مع مودات مختلفة
2. **راجع الوصوفات** وعدّل حسب الحاجة
3. **انشر المودات** مع الوصوفات العربية
4. **راقب النتائج** في التطبيق

### 🎯 النتيجة المتوقعة:
- **محتوى عربي عالي الجودة** في التطبيق
- **تجربة أفضل** للمستخدمين العرب
- **توفير الوقت** في كتابة الوصوفات
- **زيادة الوصول** للمحتوى في المنطقة العربية

---

**الميزة جاهزة للاستخدام! ابدأ بإنشاء وصوفات عربية رائعة لمودات ماين كرافت! 🎉**
