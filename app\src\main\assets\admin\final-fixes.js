// Final Fixes for New Features
// إصلاحات نهائية للميزات الجديدة

// Fix for drawer toggle function
document.addEventListener('DOMContentLoaded', function() {
    // Ensure toggleDrawer function is available globally
    if (typeof window.toggleDrawer === 'undefined') {
        window.toggleDrawer = function() {
            console.log("Global toggleDrawer() called");
            const drawer = document.querySelector(".drawer");
            const overlay = document.querySelector(".drawer-overlay");
            if (drawer) drawer.classList.add("active");
            if (overlay) overlay.classList.add("active");
        };
    }
    
    // Fix for backup ads integration
    if (typeof window.backupAds !== 'undefined') {
        console.log('✅ نظام الإعلانات الاحتياطية متاح');
        
        // Auto-initialize backup ads
        window.backupAds.initialize().then(result => {
            if (result) {
                console.log('✅ تم تهيئة نظام الإعلانات الاحتياطية تلقائياً');
            }
        }).catch(error => {
            console.warn('⚠️ فشل في تهيئة نظام الإعلانات الاحتياطية:', error);
        });
    }
    
    // Fix for user settings communication
    if (window.parent && window.parent !== window) {
        // This is running in an iframe (user settings)
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'REQUEST_SETTINGS') {
                // Send current settings to parent
                const settings = {
                    darkMode: localStorage.getItem('darkMode') !== 'false',
                    visualEffects: localStorage.getItem('visualEffects') !== 'false',
                    selectedLanguage: localStorage.getItem('selectedLanguage') || 'ar'
                };
                
                window.parent.postMessage({
                    type: 'SETTINGS_UPDATE',
                    settings: settings
                }, '*');
            }
        });
    }
});

// Enhanced error handling for backup ads
if (typeof window.backupAds !== 'undefined') {
    const originalShow = window.backupAds.show;
    
    window.backupAds.show = async function(modId, modCategory) {
        try {
            return await originalShow.call(this, modId, modCategory);
        } catch (error) {
            console.error('❌ خطأ في عرض الإعلان الاحتياطي:', error);
            
            // Try to show a fallback message
            showFallbackAdMessage();
            return false;
        }
    };
}

// Fallback ad message function
function showFallbackAdMessage() {
    const fallbackHTML = `
        <div id="fallbackAdMessage" style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            z-index: 10001;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 300px;
        ">
            <h3 style="margin: 0 0 10px 0; color: #ffd700;">
                <i class="fas fa-info-circle"></i> إشعار
            </h3>
            <p style="margin: 0 0 15px 0;">
                نعتذر، لا توجد إعلانات متاحة حالياً
            </p>
            <button onclick="document.getElementById('fallbackAdMessage').remove()" style="
                background: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 20px;
                cursor: pointer;
            ">
                حسناً
            </button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', fallbackHTML);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const element = document.getElementById('fallbackAdMessage');
        if (element) element.remove();
    }, 5000);
}

// Fix for supabase client conflicts
if (typeof window.supabaseManager !== 'undefined') {
    // Ensure backup ads use the correct client
    if (typeof window.backupAds !== 'undefined' && window.backupAds.setSupabaseClient) {
        window.backupAds.setSupabaseClient(window.supabaseManager.getMainClient());
    }
}

// Enhanced console logging for debugging
if (localStorage.getItem('debugMode') === 'true') {
    console.log('🔧 وضع التطوير مفعل');
    
    // Log all backup ads events
    if (typeof window.backupAds !== 'undefined') {
        const originalLog = console.log;
        console.log = function(...args) {
            if (args[0] && args[0].includes('backup') || args[0].includes('احتياط')) {
                originalLog.apply(console, ['[BACKUP-ADS]', ...args]);
            } else {
                originalLog.apply(console, args);
            }
        };
    }
}

// Performance monitoring
if (typeof performance !== 'undefined' && performance.mark) {
    performance.mark('final-fixes-start');
    
    window.addEventListener('load', function() {
        performance.mark('final-fixes-end');
        performance.measure('final-fixes-duration', 'final-fixes-start', 'final-fixes-end');
        
        const measure = performance.getEntriesByName('final-fixes-duration')[0];
        if (measure) {
            console.log(`⚡ تم تحميل الإصلاحات النهائية في ${measure.duration.toFixed(2)}ms`);
        }
    });
}

// Auto-fix common issues
function autoFixCommonIssues() {
    // Fix missing Font Awesome icons
    if (!document.querySelector('link[href*="font-awesome"]')) {
        const fontAwesome = document.createElement('link');
        fontAwesome.rel = 'stylesheet';
        fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
        document.head.appendChild(fontAwesome);
        console.log('✅ تم إضافة Font Awesome تلقائياً');
    }
    
    // Fix missing meta viewport
    if (!document.querySelector('meta[name="viewport"]')) {
        const viewport = document.createElement('meta');
        viewport.name = 'viewport';
        viewport.content = 'width=device-width, initial-scale=1.0';
        document.head.appendChild(viewport);
        console.log('✅ تم إضافة meta viewport تلقائياً');
    }
    
    // Fix missing charset
    if (!document.querySelector('meta[charset]')) {
        const charset = document.createElement('meta');
        charset.setAttribute('charset', 'UTF-8');
        document.head.insertBefore(charset, document.head.firstChild);
        console.log('✅ تم إضافة charset تلقائياً');
    }
}

// Run auto-fixes
autoFixCommonIssues();

// Global error handler for backup ads
window.addEventListener('error', function(event) {
    if (event.error && event.error.message && 
        (event.error.message.includes('backup') || event.error.message.includes('احتياط'))) {
        console.error('❌ خطأ في نظام الإعلانات الاحتياطية:', event.error);
        
        // Try to reinitialize backup ads system
        if (typeof window.backupAds !== 'undefined' && window.backupAds.initialize) {
            setTimeout(() => {
                window.backupAds.initialize().catch(err => {
                    console.error('❌ فشل في إعادة تهيئة نظام الإعلانات الاحتياطية:', err);
                });
            }, 1000);
        }
    }
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.message && 
        (event.reason.message.includes('backup') || event.reason.message.includes('احتياط'))) {
        console.error('❌ رفض غير معالج في نظام الإعلانات الاحتياطية:', event.reason);
        event.preventDefault(); // Prevent the default browser behavior
    }
});

// Utility function to check system health
window.checkSystemHealth = function() {
    const health = {
        backupAds: typeof window.backupAds !== 'undefined',
        supabaseManager: typeof window.supabaseManager !== 'undefined',
        toggleDrawer: typeof window.toggleDrawer !== 'undefined',
        fontAwesome: !!document.querySelector('link[href*="font-awesome"]'),
        viewport: !!document.querySelector('meta[name="viewport"]'),
        charset: !!document.querySelector('meta[charset]')
    };
    
    console.table(health);
    
    const issues = Object.entries(health).filter(([key, value]) => !value);
    if (issues.length > 0) {
        console.warn('⚠️ مشاكل محتملة:', issues.map(([key]) => key));
    } else {
        console.log('✅ جميع الأنظمة تعمل بشكل صحيح');
    }
    
    return health;
};

// Export for debugging
window.finalFixes = {
    autoFixCommonIssues,
    showFallbackAdMessage,
    checkSystemHealth: window.checkSystemHealth
};

console.log('🔧 تم تحميل الإصلاحات النهائية بنجاح');
