-- ========================================
-- نظام انتهاء الحملات التلقائي
-- Automatic Campaign Expiry System
-- ========================================

-- 1. دالة لإنهاء الحملات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION expire_old_campaigns()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER := 0;
    campaign_record RECORD;
BEGIN
    -- البحث عن الحملات المنتهية الصلاحية
    FOR campaign_record IN 
        SELECT id, title_ar, end_date 
        FROM free_subscription_campaigns 
        WHERE is_active = true 
          AND end_date IS NOT NULL 
          AND end_date < CURRENT_TIMESTAMP
    LOOP
        -- إنهاء الحملة
        UPDATE free_subscription_campaigns 
        SET is_active = false, updated_at = CURRENT_TIMESTAMP
        WHERE id = campaign_record.id;
        
        -- إنهاء جميع الاشتراكات النشطة لهذه الحملة
        UPDATE user_subscriptions 
        SET status = 'expired', updated_at = CURRENT_TIMESTAMP
        WHERE campaign_id = campaign_record.id 
          AND status = 'active';
        
        expired_count := expired_count + 1;
        
        -- تسجيل في السجل
        RAISE NOTICE 'تم إنهاء الحملة: % (ID: %)', campaign_record.title_ar, campaign_record.id;
    END LOOP;
    
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- 2. دالة لتنظيف البيانات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS JSONB AS $$
DECLARE
    deleted_logs INTEGER := 0;
    deleted_progress INTEGER := 0;
    result JSONB;
BEGIN
    -- حذف سجلات التحقق الأقدم من 30 يوم
    DELETE FROM verification_logs 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    GET DIAGNOSTICS deleted_logs = ROW_COUNT;
    
    -- حذف تقدم المهام للحملات المنتهية الصلاحية (أقدم من 7 أيام)
    DELETE FROM user_task_progress 
    WHERE campaign_id IN (
        SELECT id FROM free_subscription_campaigns 
        WHERE is_active = false 
          AND updated_at < CURRENT_TIMESTAMP - INTERVAL '7 days'
    );
    GET DIAGNOSTICS deleted_progress = ROW_COUNT;
    
    result := jsonb_build_object(
        'deleted_logs', deleted_logs,
        'deleted_progress', deleted_progress,
        'cleanup_date', CURRENT_TIMESTAMP
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 3. دالة لإحصائيات الحملات
CREATE OR REPLACE FUNCTION get_campaigns_stats()
RETURNS JSONB AS $$
DECLARE
    stats JSONB;
BEGIN
    SELECT jsonb_build_object(
        'active_campaigns', (
            SELECT COUNT(*) FROM free_subscription_campaigns 
            WHERE is_active = true
        ),
        'expired_campaigns', (
            SELECT COUNT(*) FROM free_subscription_campaigns 
            WHERE is_active = false
        ),
        'total_subscriptions', (
            SELECT COUNT(*) FROM user_subscriptions 
            WHERE status = 'active'
        ),
        'expired_subscriptions', (
            SELECT COUNT(*) FROM user_subscriptions 
            WHERE status = 'expired'
        ),
        'campaigns_expiring_soon', (
            SELECT COUNT(*) FROM free_subscription_campaigns 
            WHERE is_active = true 
              AND end_date IS NOT NULL 
              AND end_date BETWEEN CURRENT_TIMESTAMP AND CURRENT_TIMESTAMP + INTERVAL '24 hours'
        ),
        'total_tasks_completed', (
            SELECT COUNT(*) FROM user_task_progress 
            WHERE status = 'verified'
        ),
        'last_updated', CURRENT_TIMESTAMP
    ) INTO stats;
    
    RETURN stats;
END;
$$ LANGUAGE plpgsql;

-- 4. دالة لإنشاء حملة سريعة (قالب جاهز)
CREATE OR REPLACE FUNCTION create_quick_campaign(
    p_title_ar TEXT,
    p_title_en TEXT,
    p_duration_days INTEGER DEFAULT 1,
    p_max_users INTEGER DEFAULT 100
)
RETURNS JSONB AS $$
DECLARE
    campaign_id UUID;
    end_date TIMESTAMP WITH TIME ZONE;
    result JSONB;
BEGIN
    -- حساب تاريخ الانتهاء
    end_date := CURRENT_TIMESTAMP + (p_duration_days || ' days')::INTERVAL;
    
    -- إنشاء الحملة
    INSERT INTO free_subscription_campaigns (
        title_ar, title_en,
        description_ar, description_en,
        subscription_duration_days, max_users,
        verification_strictness, auto_verify_enabled,
        is_active, start_date, end_date
    ) VALUES (
        p_title_ar, p_title_en,
        'احصل على اشتراك مجاني من خلال إكمال المهام البسيطة',
        'Get a free subscription by completing simple tasks',
        p_duration_days, p_max_users,
        'medium', true,
        true, CURRENT_TIMESTAMP, end_date
    ) RETURNING id INTO campaign_id;
    
    -- إضافة مهام افتراضية
    INSERT INTO campaign_tasks (
        campaign_id, task_type, title_ar, title_en,
        description_ar, description_en,
        target_url, target_id,
        verification_method, display_order, is_required
    ) VALUES 
    (
        campaign_id, 'youtube_subscribe',
        'اشترك في قناة يوتيوب', 'Subscribe to YouTube Channel',
        'اشترك في قناتنا على يوتيوب', 'Subscribe to our YouTube channel',
        'https://youtube.com/@modetaris', 'UCxxxxxxxxxxxxxxxxxxxxx',
        'smart', 1, true
    ),
    (
        campaign_id, 'telegram_subscribe',
        'انضم لقناة تيليجرام', 'Join Telegram Channel',
        'انضم لقناتنا على تيليجرام', 'Join our Telegram channel',
        'https://t.me/modetaris', '@modetaris',
        'smart', 2, true
    ),
    (
        campaign_id, 'discord_join',
        'انضم لخادم ديسكورد', 'Join Discord Server',
        'انضم لخادمنا على ديسكورد', 'Join our Discord server',
        'https://discord.gg/modetaris', '123456789012345678',
        'smart', 3, true
    );
    
    result := jsonb_build_object(
        'success', true,
        'campaign_id', campaign_id,
        'title', p_title_ar,
        'duration_days', p_duration_days,
        'expires_at', end_date,
        'tasks_created', 3
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 5. دالة لتمديد حملة نشطة
CREATE OR REPLACE FUNCTION extend_campaign(
    p_campaign_id UUID,
    p_additional_days INTEGER
)
RETURNS JSONB AS $$
DECLARE
    campaign_record RECORD;
    new_end_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- التحقق من وجود الحملة
    SELECT * INTO campaign_record
    FROM free_subscription_campaigns
    WHERE id = p_campaign_id AND is_active = true;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Campaign not found or inactive'
        );
    END IF;
    
    -- حساب التاريخ الجديد
    new_end_date := COALESCE(campaign_record.end_date, CURRENT_TIMESTAMP) + (p_additional_days || ' days')::INTERVAL;
    
    -- تحديث الحملة
    UPDATE free_subscription_campaigns
    SET 
        end_date = new_end_date,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_campaign_id;
    
    -- تمديد الاشتراكات النشطة
    UPDATE user_subscriptions
    SET 
        expires_at = expires_at + (p_additional_days || ' days')::INTERVAL,
        updated_at = CURRENT_TIMESTAMP
    WHERE campaign_id = p_campaign_id AND status = 'active';
    
    RETURN jsonb_build_object(
        'success', true,
        'campaign_id', p_campaign_id,
        'new_end_date', new_end_date,
        'extended_days', p_additional_days
    );
END;
$$ LANGUAGE plpgsql;

-- 6. إنشاء جدولة تلقائية لتنظيف البيانات (PostgreSQL Cron Extension)
-- ملاحظة: يتطلب تفعيل pg_cron extension في Supabase

-- تشغيل تنظيف الحملات كل ساعة
-- SELECT cron.schedule('expire-campaigns', '0 * * * *', 'SELECT expire_old_campaigns();');

-- تشغيل تنظيف البيانات القديمة يومياً في الساعة 2 صباحاً
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');

-- 7. منح الصلاحيات
GRANT EXECUTE ON FUNCTION expire_old_campaigns() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_data() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_campaigns_stats() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION create_quick_campaign(TEXT, TEXT, INTEGER, INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION extend_campaign(UUID, INTEGER) TO anon, authenticated;

-- 8. إنشاء view للحملات النشطة
CREATE OR REPLACE VIEW active_campaigns_view AS
SELECT 
    fsc.*,
    COUNT(ct.id) as tasks_count,
    COUNT(us.id) as subscribers_count,
    CASE 
        WHEN fsc.end_date IS NULL THEN 'لا نهاية'
        WHEN fsc.end_date > CURRENT_TIMESTAMP THEN 
            EXTRACT(DAY FROM fsc.end_date - CURRENT_TIMESTAMP) || ' يوم متبقي'
        ELSE 'منتهية'
    END as time_remaining
FROM free_subscription_campaigns fsc
LEFT JOIN campaign_tasks ct ON fsc.id = ct.campaign_id
LEFT JOIN user_subscriptions us ON fsc.id = us.campaign_id AND us.status = 'active'
WHERE fsc.is_active = true
GROUP BY fsc.id
ORDER BY fsc.created_at DESC;

-- 9. إنشاء view لإحصائيات سريعة
CREATE OR REPLACE VIEW quick_stats_view AS
SELECT 
    'active_campaigns' as stat_name,
    COUNT(*)::TEXT as stat_value,
    'الحملات النشطة' as stat_label
FROM free_subscription_campaigns 
WHERE is_active = true

UNION ALL

SELECT 
    'total_subscribers' as stat_name,
    COUNT(*)::TEXT as stat_value,
    'إجمالي المشتركين' as stat_label
FROM user_subscriptions 
WHERE status = 'active'

UNION ALL

SELECT 
    'completed_tasks' as stat_name,
    COUNT(*)::TEXT as stat_value,
    'المهام المكتملة' as stat_label
FROM user_task_progress 
WHERE status = 'verified'

UNION ALL

SELECT 
    'expiring_soon' as stat_name,
    COUNT(*)::TEXT as stat_value,
    'تنتهي قريباً' as stat_label
FROM free_subscription_campaigns 
WHERE is_active = true 
  AND end_date IS NOT NULL 
  AND end_date BETWEEN CURRENT_TIMESTAMP AND CURRENT_TIMESTAMP + INTERVAL '24 hours';

-- 10. رسالة النجاح
SELECT 'تم إنشاء نظام انتهاء الحملات التلقائي بنجاح! ✅' as status,
       'يمكنك الآن استخدام الدوال التلقائية لإدارة الحملات' as message;
