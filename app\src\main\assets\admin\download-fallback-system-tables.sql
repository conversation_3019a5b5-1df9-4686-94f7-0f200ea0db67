-- =====================================================
-- نظام التحميل الاحتياطي الذكي - جداول قاعدة البيانات
-- Smart Download Fallback System - Database Tables
-- =====================================================

-- جدول أخطاء التحميل
-- Download Errors Table
CREATE TABLE IF NOT EXISTS download_errors (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id) ON DELETE CASCADE,
    mod_name TEXT NOT NULL,
    original_url TEXT NOT NULL,
    backup_url TEXT,
    error_type TEXT NOT NULL CHECK (error_type IN (
        'complete_failure',
        'original_failed_backup_success', 
        'timeout',
        'network_error',
        'file_corrupted',
        'url_not_found',
        'permission_denied',
        'server_error',
        'invalid_response'
    )),
    error_message TEXT,
    user_agent TEXT,
    user_ip INET,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by TEXT,
    resolution_notes TEXT,
    retry_count INTEGER DEFAULT 0,
    last_retry_at TIMESTAMP WITH TIME ZONE,
    severity_level TEXT DEFAULT 'medium' CHECK (severity_level IN ('low', 'medium', 'high', 'critical')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لجدول أخطاء التحميل
CREATE INDEX IF NOT EXISTS idx_download_errors_mod_id ON download_errors(mod_id);
CREATE INDEX IF NOT EXISTS idx_download_errors_timestamp ON download_errors(timestamp);
CREATE INDEX IF NOT EXISTS idx_download_errors_error_type ON download_errors(error_type);
CREATE INDEX IF NOT EXISTS idx_download_errors_is_resolved ON download_errors(is_resolved);
CREATE INDEX IF NOT EXISTS idx_download_errors_severity ON download_errors(severity_level);

-- جدول الروابط الاحتياطية للمودات
-- Mod Backup URLs Table
CREATE TABLE IF NOT EXISTS mod_backup_urls (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id) ON DELETE CASCADE,
    backup_url TEXT NOT NULL,
    backup_type TEXT DEFAULT 'external' CHECK (backup_type IN (
        'firebase',
        'external',
        'mirror',
        'cdn',
        'direct'
    )),
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_tested TIMESTAMP WITH TIME ZONE,
    test_result BOOLEAN,
    test_response_time INTEGER, -- in milliseconds
    failure_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    notes TEXT,
    created_by TEXT,
    UNIQUE(mod_id, backup_url)
);

-- فهارس لجدول الروابط الاحتياطية
CREATE INDEX IF NOT EXISTS idx_mod_backup_urls_mod_id ON mod_backup_urls(mod_id);
CREATE INDEX IF NOT EXISTS idx_mod_backup_urls_active ON mod_backup_urls(is_active);
CREATE INDEX IF NOT EXISTS idx_mod_backup_urls_priority ON mod_backup_urls(priority);
CREATE INDEX IF NOT EXISTS idx_mod_backup_urls_type ON mod_backup_urls(backup_type);

-- جدول إحصائيات التحميل اليومية
-- Daily Download Statistics Table
CREATE TABLE IF NOT EXISTS download_statistics (
    id SERIAL PRIMARY KEY,
    date DATE DEFAULT CURRENT_DATE,
    total_attempts INTEGER DEFAULT 0,
    successful_downloads INTEGER DEFAULT 0,
    backup_downloads INTEGER DEFAULT 0,
    failed_downloads INTEGER DEFAULT 0,
    error_types JSONB DEFAULT '{}',
    average_download_time DECIMAL(10,2), -- in seconds
    peak_hour INTEGER, -- 0-23
    unique_users INTEGER DEFAULT 0,
    most_failed_mod_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date)
);

-- فهارس لجدول الإحصائيات
CREATE INDEX IF NOT EXISTS idx_download_statistics_date ON download_statistics(date);

-- جدول تاريخ إصلاح الروابط
-- URL Fix History Table
CREATE TABLE IF NOT EXISTS url_fix_history (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id) ON DELETE CASCADE,
    error_id INTEGER REFERENCES download_errors(id) ON DELETE SET NULL,
    old_url TEXT NOT NULL,
    new_url TEXT NOT NULL,
    backup_url TEXT,
    fix_type TEXT DEFAULT 'url_update' CHECK (fix_type IN (
        'url_update',
        'backup_added',
        'url_redirect',
        'server_migration',
        'manual_fix',
        'automated_fix'
    )),
    fix_description TEXT,
    fixed_by TEXT NOT NULL,
    fixed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    fix_method TEXT DEFAULT 'manual' CHECK (fix_method IN ('manual', 'automated', 'bulk')),
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMP WITH TIME ZONE,
    verification_notes TEXT,
    success_rate_before DECIMAL(5,2),
    success_rate_after DECIMAL(5,2)
);

-- فهارس لجدول تاريخ الإصلاح
CREATE INDEX IF NOT EXISTS idx_url_fix_history_mod_id ON url_fix_history(mod_id);
CREATE INDEX IF NOT EXISTS idx_url_fix_history_fixed_at ON url_fix_history(fixed_at);
CREATE INDEX IF NOT EXISTS idx_url_fix_history_fix_type ON url_fix_history(fix_type);

-- جدول إعدادات نظام التحميل الاحتياطي
-- Download Fallback System Settings Table
CREATE TABLE IF NOT EXISTS download_fallback_settings (
    id SERIAL PRIMARY KEY,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type TEXT DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by TEXT
);

-- إدراج الإعدادات الافتراضية
INSERT INTO download_fallback_settings (setting_key, setting_value, setting_type, description) VALUES
('max_retry_attempts', '3', 'number', 'عدد محاولات إعادة التحميل القصوى'),
('retry_delay_seconds', '5', 'number', 'التأخير بين محاولات إعادة التحميل بالثواني'),
('timeout_seconds', '30', 'number', 'مهلة انتظار التحميل بالثواني'),
('enable_firebase_fallback', 'true', 'boolean', 'تفعيل Firebase كمصدر احتياطي'),
('enable_auto_repair', 'true', 'boolean', 'تفعيل الإصلاح التلقائي للروابط'),
('error_notification_email', '<EMAIL>', 'string', 'بريد إشعارات الأخطاء'),
('cleanup_resolved_errors_days', '30', 'number', 'عدد الأيام لحفظ الأخطاء المحلولة'),
('backup_url_test_interval_hours', '24', 'number', 'فترة اختبار الروابط الاحتياطية بالساعات')
ON CONFLICT (setting_key) DO NOTHING;

-- جدول سجل أحداث النظام
-- System Events Log Table
CREATE TABLE IF NOT EXISTS system_events_log (
    id SERIAL PRIMARY KEY,
    event_type TEXT NOT NULL CHECK (event_type IN (
        'download_attempt',
        'download_success',
        'download_failure',
        'fallback_used',
        'url_fixed',
        'backup_added',
        'system_error',
        'maintenance'
    )),
    event_description TEXT NOT NULL,
    mod_id INTEGER REFERENCES mods(id) ON DELETE SET NULL,
    user_id TEXT,
    ip_address INET,
    user_agent TEXT,
    additional_data JSONB,
    severity TEXT DEFAULT 'info' CHECK (severity IN ('debug', 'info', 'warning', 'error', 'critical')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لجدول سجل الأحداث
CREATE INDEX IF NOT EXISTS idx_system_events_log_event_type ON system_events_log(event_type);
CREATE INDEX IF NOT EXISTS idx_system_events_log_created_at ON system_events_log(created_at);
CREATE INDEX IF NOT EXISTS idx_system_events_log_severity ON system_events_log(severity);
CREATE INDEX IF NOT EXISTS idx_system_events_log_mod_id ON system_events_log(mod_id);

-- دالة تحديث إحصائيات التحميل
-- Function to Update Download Statistics
CREATE OR REPLACE FUNCTION update_download_statistics(
    p_mod_id INTEGER,
    p_success BOOLEAN,
    p_used_backup BOOLEAN,
    p_error_type TEXT DEFAULT NULL,
    p_download_time DECIMAL DEFAULT NULL
) RETURNS VOID AS $$
DECLARE
    current_date DATE := CURRENT_DATE;
    error_types_json JSONB;
BEGIN
    -- إدراج أو تحديث الإحصائيات اليومية
    INSERT INTO download_statistics (date, total_attempts, successful_downloads, backup_downloads, failed_downloads)
    VALUES (current_date, 1, 
            CASE WHEN p_success THEN 1 ELSE 0 END,
            CASE WHEN p_used_backup AND p_success THEN 1 ELSE 0 END,
            CASE WHEN NOT p_success THEN 1 ELSE 0 END)
    ON CONFLICT (date) DO UPDATE SET
        total_attempts = download_statistics.total_attempts + 1,
        successful_downloads = download_statistics.successful_downloads + CASE WHEN p_success THEN 1 ELSE 0 END,
        backup_downloads = download_statistics.backup_downloads + CASE WHEN p_used_backup AND p_success THEN 1 ELSE 0 END,
        failed_downloads = download_statistics.failed_downloads + CASE WHEN NOT p_success THEN 1 ELSE 0 END,
        updated_at = NOW();
    
    -- تحديث أنواع الأخطاء إذا كان هناك خطأ
    IF NOT p_success AND p_error_type IS NOT NULL THEN
        SELECT error_types INTO error_types_json FROM download_statistics WHERE date = current_date;
        
        IF error_types_json IS NULL THEN
            error_types_json := '{}';
        END IF;
        
        error_types_json := jsonb_set(
            error_types_json,
            ARRAY[p_error_type],
            to_jsonb(COALESCE((error_types_json->p_error_type)::INTEGER, 0) + 1)
        );
        
        UPDATE download_statistics 
        SET error_types = error_types_json,
            updated_at = NOW()
        WHERE date = current_date;
    END IF;
    
    -- تسجيل الحدث في سجل الأحداث
    INSERT INTO system_events_log (
        event_type,
        event_description,
        mod_id,
        additional_data,
        severity
    ) VALUES (
        CASE WHEN p_success THEN 'download_success' ELSE 'download_failure' END,
        CASE 
            WHEN p_success AND p_used_backup THEN 'تم التحميل بنجاح من الرابط الاحتياطي'
            WHEN p_success THEN 'تم التحميل بنجاح من الرابط الأصلي'
            ELSE 'فشل في التحميل: ' || COALESCE(p_error_type, 'خطأ غير محدد')
        END,
        p_mod_id,
        jsonb_build_object(
            'used_backup', p_used_backup,
            'error_type', p_error_type,
            'download_time', p_download_time
        ),
        CASE WHEN p_success THEN 'info' ELSE 'warning' END
    );
END;
$$ LANGUAGE plpgsql;

-- دالة تنظيف البيانات القديمة
-- Function to Cleanup Old Data
CREATE OR REPLACE FUNCTION cleanup_old_data() RETURNS VOID AS $$
DECLARE
    cleanup_days INTEGER;
BEGIN
    -- الحصول على عدد أيام التنظيف من الإعدادات
    SELECT setting_value::INTEGER INTO cleanup_days 
    FROM download_fallback_settings 
    WHERE setting_key = 'cleanup_resolved_errors_days';
    
    IF cleanup_days IS NULL THEN
        cleanup_days := 30;
    END IF;
    
    -- حذف الأخطاء المحلولة القديمة
    DELETE FROM download_errors 
    WHERE is_resolved = TRUE 
    AND resolved_at < NOW() - INTERVAL '1 day' * cleanup_days;
    
    -- حذف سجل الأحداث القديم (أكثر من 90 يوم)
    DELETE FROM system_events_log 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- حذف الإحصائيات القديمة (أكثر من سنة)
    DELETE FROM download_statistics 
    WHERE date < CURRENT_DATE - INTERVAL '1 year';
    
    -- تسجيل عملية التنظيف
    INSERT INTO system_events_log (
        event_type,
        event_description,
        severity
    ) VALUES (
        'maintenance',
        'تم تنظيف البيانات القديمة',
        'info'
    );
END;
$$ LANGUAGE plpgsql;

-- دالة اختبار الروابط الاحتياطية
-- Function to Test Backup URLs
CREATE OR REPLACE FUNCTION test_backup_urls() RETURNS TABLE(
    backup_id INTEGER,
    mod_id INTEGER,
    backup_url TEXT,
    test_result BOOLEAN,
    response_time INTEGER
) AS $$
BEGIN
    -- هذه الدالة ستحتاج إلى تنفيذ من جانب التطبيق
    -- لأن PostgreSQL لا يمكنها إجراء HTTP requests مباشرة
    RETURN QUERY
    SELECT 
        mbu.id,
        mbu.mod_id,
        mbu.backup_url,
        mbu.test_result,
        mbu.test_response_time
    FROM mod_backup_urls mbu
    WHERE mbu.is_active = TRUE
    AND (mbu.last_tested IS NULL OR mbu.last_tested < NOW() - INTERVAL '24 hours');
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغلات التحديث التلقائي
-- Create Auto-Update Triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق المشغلات على الجداول
CREATE TRIGGER update_download_errors_updated_at
    BEFORE UPDATE ON download_errors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mod_backup_urls_updated_at
    BEFORE UPDATE ON mod_backup_urls
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_statistics_updated_at
    BEFORE UPDATE ON download_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_fallback_settings_updated_at
    BEFORE UPDATE ON download_fallback_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إنشاء views مفيدة
-- Create Useful Views

-- عرض إحصائيات الأخطاء
CREATE OR REPLACE VIEW error_statistics AS
SELECT 
    error_type,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE is_resolved = FALSE) as unresolved_count,
    COUNT(*) FILTER (WHERE is_resolved = TRUE) as resolved_count,
    ROUND(AVG(EXTRACT(EPOCH FROM (resolved_at - timestamp))/3600), 2) as avg_resolution_time_hours
FROM download_errors
WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY error_type
ORDER BY total_count DESC;

-- عرض أداء الروابط الاحتياطية
CREATE OR REPLACE VIEW backup_url_performance AS
SELECT 
    mbu.id,
    mbu.mod_id,
    m.name as mod_name,
    mbu.backup_url,
    mbu.backup_type,
    mbu.success_count,
    mbu.failure_count,
    CASE 
        WHEN (mbu.success_count + mbu.failure_count) > 0 
        THEN ROUND((mbu.success_count::DECIMAL / (mbu.success_count + mbu.failure_count)) * 100, 2)
        ELSE NULL 
    END as success_rate,
    mbu.test_response_time,
    mbu.last_tested
FROM mod_backup_urls mbu
LEFT JOIN mods m ON mbu.mod_id = m.id
WHERE mbu.is_active = TRUE
ORDER BY success_rate DESC NULLS LAST;

-- منح الصلاحيات (اختياري - حسب إعداد قاعدة البيانات)
-- Grant Permissions (Optional - depends on database setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_app_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO your_app_user;

-- إنهاء الملف
-- End of File
