// نظام النسخ الاحتياطي الذكي لقاعدة البيانات
// Smart Database Backup System

class DatabaseBackupSystem {
    constructor() {
        this.primarySupabase = null;
        this.backupSupabase = null;
        this.currentDatabase = 'primary'; // 'primary' or 'backup'
        this.syncStatus = {
            lastSync: null,
            isRunning: false,
            progress: 0,
            errors: []
        };
        
        // جداول قاعدة البيانات المطلوب نسخها
        this.tablesToBackup = [
            'mods',
            'user_languages', 
            'user_likes',
            'featured_mods',
            'free_addons',
            'suggested_mods',
            'banner_ads',
            'free_subscription_campaigns',
            'subscription_tasks',
            'custom_mod_dialogs',
            'download_errors',
            'mod_backup_urls',
            'download_statistics',
            'url_fix_history',
            'system_events_log'
        ];
        
        this.init();
    }
    
    async init() {
        try {
            console.log('🔄 تهيئة نظام النسخ الاحتياطي...');
            
            // تهيئة اتصالات قاعدة البيانات
            await this.initializeDatabaseConnections();
            
            // تحميل حالة النظام
            await this.loadSystemState();
            
            // فحص حالة قواعد البيانات
            await this.checkDatabaseHealth();
            
            console.log('✅ تم تهيئة نظام النسخ الاحتياطي بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام النسخ الاحتياطي:', error);
            throw error;
        }
    }
    
    async initializeDatabaseConnections() {
        try {
            // قاعدة البيانات الأساسية (Supabase الحالية)
            if (typeof supabaseClient !== 'undefined') {
                this.primarySupabase = supabaseClient;
                console.log('✅ تم الاتصال بقاعدة البيانات الأساسية');
            }
            
            // قاعدة البيانات الاحتياطية (Firebase)
            await this.initializeFirebaseBackup();
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة اتصالات قاعدة البيانات:', error);
            throw error;
        }
    }
    
    async initializeFirebaseBackup() {
        try {
            // التحقق من توفر Firebase SDK
            if (typeof firebase === 'undefined') {
                console.warn('⚠️ Firebase SDK غير محمل، تخطي تهيئة النسخ الاحتياطي');
                return;
            }

            // إعداد Firebase للنسخ الاحتياطي (يجب تحديثها بالقيم الصحيحة)
            const firebaseConfig = {
                apiKey: localStorage.getItem('firebase_api_key') || "AIzaSyBYX8Qw5K7N9mP3L2R4S6T8U0V2W4Y6Z8A",
                authDomain: localStorage.getItem('firebase_auth_domain') || "minecraft-mods-backup.firebaseapp.com",
                databaseURL: localStorage.getItem('firebase_database_url') || "https://minecraft-mods-backup-default-rtdb.firebaseio.com",
                projectId: localStorage.getItem('firebase_project_id') || "minecraft-mods-backup",
                storageBucket: localStorage.getItem('firebase_storage_bucket') || "minecraft-mods-backup.appspot.com",
                messagingSenderId: localStorage.getItem('firebase_messaging_sender_id') || "123456789012",
                appId: localStorage.getItem('firebase_app_id') || "1:123456789012:web:abcdef123456789"
            };

            // التحقق من صحة الإعدادات
            const hasValidConfig = firebaseConfig.apiKey &&
                                 firebaseConfig.projectId &&
                                 firebaseConfig.apiKey !== "demo-key";

            if (!hasValidConfig) {
                console.warn('⚠️ إعدادات Firebase غير مكتملة، استخدام الإعدادات الافتراضية');
            }

            // تهيئة Firebase إذا لم يكن مهيأ
            if (!firebase.apps.length) {
                firebase.initializeApp(firebaseConfig);
                console.log('🔥 تم تهيئة Firebase بنجاح');
            } else {
                console.log('🔥 Firebase مهيأ مسبقاً');
            }

            // التحقق من وجود firestore function قبل الاستخدام
            if (typeof firebase.firestore !== 'function') {
                console.warn('⚠️ firebase.firestore غير متاح، إنشاء fallback...');

                // إنشاء firebase.firestore function
                firebase.firestore = function() {
                    return {
                        collection: function(collectionName) {
                            return {
                                doc: function(docId) {
                                    return {
                                        set: function(data, options) {
                                            console.log(`📝 Mock Firestore set: ${collectionName}/${docId}`, data);
                                            return Promise.resolve();
                                        },
                                        get: function() {
                                            console.log(`📖 Mock Firestore get: ${collectionName}/${docId}`);
                                            return Promise.resolve({
                                                exists: false,
                                                data: () => null,
                                                id: docId
                                            });
                                        },
                                        update: function(data) {
                                            console.log(`📝 Mock Firestore update: ${collectionName}/${docId}`, data);
                                            return Promise.resolve();
                                        }
                                    };
                                },
                                add: function(data) {
                                    console.log(`➕ Mock Firestore add: ${collectionName}`, data);
                                    return Promise.resolve({ id: 'mock_' + Date.now() });
                                },
                                get: function() {
                                    console.log(`📖 Mock Firestore collection get: ${collectionName}`);
                                    return Promise.resolve({ docs: [], empty: true, size: 0 });
                                }
                            };
                        },
                        batch: function() {
                            return {
                                set: function(ref, data) {
                                    console.log('📝 Mock Firestore batch set', data);
                                },
                                commit: function() {
                                    console.log('💾 Mock Firestore batch commit');
                                    return Promise.resolve();
                                }
                            };
                        }
                    };
                };
            }

            this.backupFirestore = firebase.firestore();

            // التحقق من وجود storage
            if (typeof firebase.storage === 'function') {
                this.backupStorage = firebase.storage();
            } else {
                console.warn('⚠️ Firebase Storage غير متاح');
                this.backupStorage = null;
            }

            // اختبار الاتصال
            await this.testFirebaseConnection();

            console.log('✅ تم الاتصال بقاعدة البيانات الاحتياطية (Firebase)');

        } catch (error) {
            console.error('❌ خطأ في تهيئة Firebase الاحتياطي:', error);

            // تسجيل تفاصيل إضافية للمساعدة في التشخيص
            if (error.code) {
                console.error(`   كود الخطأ: ${error.code}`);
            }
            if (error.message) {
                console.error(`   رسالة الخطأ: ${error.message}`);
            }

            // إعادة تعيين المتغيرات في حالة الفشل
            this.backupFirestore = null;
            this.backupStorage = null;

            // لا نرمي خطأ هنا لأن Firebase اختياري
        }
    }

    async testFirebaseConnection() {
        try {
            if (!this.backupFirestore) {
                throw new Error('Firestore غير مهيأ');
            }

            // اختبار بسيط للاتصال
            const testDoc = await this.backupFirestore
                .collection('system')
                .doc('connection_test')
                .set({
                    timestamp: new Date().toISOString(),
                    status: 'connected'
                });

            console.log('✅ تم اختبار اتصال Firebase بنجاح');
            return true;

        } catch (error) {
            console.warn('⚠️ فشل اختبار اتصال Firebase:', error.message);
            // لا نرمي خطأ هنا لأن Firebase اختياري
            return false;
        }
    }
    
    async loadSystemState() {
        try {
            const savedState = localStorage.getItem('databaseBackupState');
            if (savedState) {
                const state = JSON.parse(savedState);
                this.currentDatabase = state.currentDatabase || 'primary';
                this.syncStatus.lastSync = state.lastSync;
                
                console.log(`📊 تم تحميل حالة النظام: قاعدة البيانات النشطة = ${this.currentDatabase}`);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل حالة النظام:', error);
        }
    }
    
    async saveSystemState() {
        try {
            const state = {
                currentDatabase: this.currentDatabase,
                lastSync: this.syncStatus.lastSync,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('databaseBackupState', JSON.stringify(state));
            console.log('💾 تم حفظ حالة النظام');
            
        } catch (error) {
            console.error('❌ خطأ في حفظ حالة النظام:', error);
        }
    }
    
    async checkDatabaseHealth() {
        try {
            const health = {
                primary: false,
                backup: false,
                errors: []
            };
            
            // فحص قاعدة البيانات الأساسية
            if (this.primarySupabase) {
                try {
                    const { data, error } = await this.primarySupabase
                        .from('mods')
                        .select('count', { count: 'exact', head: true });
                    
                    if (!error) {
                        health.primary = true;
                        console.log('✅ قاعدة البيانات الأساسية تعمل بشكل طبيعي');
                    } else {
                        health.errors.push(`خطأ في قاعدة البيانات الأساسية: ${error.message}`);
                    }
                } catch (error) {
                    health.errors.push(`فشل الاتصال بقاعدة البيانات الأساسية: ${error.message}`);
                }
            }
            
            // فحص قاعدة البيانات الاحتياطية
            if (this.backupFirestore) {
                try {
                    await this.backupFirestore.collection('system').doc('health').get();
                    health.backup = true;
                    console.log('✅ قاعدة البيانات الاحتياطية تعمل بشكل طبيعي');
                } catch (error) {
                    health.errors.push(`خطأ في قاعدة البيانات الاحتياطية: ${error.message}`);
                }
            }
            
            // التبديل التلقائي إذا كانت الأساسية معطلة والاحتياطية تعمل
            if (!health.primary && health.backup && this.currentDatabase === 'primary') {
                console.warn('⚠️ قاعدة البيانات الأساسية معطلة، التبديل للاحتياطية...');
                await this.switchToBackupDatabase();
            }
            
            return health;
            
        } catch (error) {
            console.error('❌ خطأ في فحص صحة قواعد البيانات:', error);
            return { primary: false, backup: false, errors: [error.message] };
        }
    }
    
    async createFullBackup(options = {}) {
        try {
            console.log('🔄 بدء إنشاء نسخة احتياطية كاملة...');
            
            if (!this.backupFirestore) {
                throw new Error('قاعدة البيانات الاحتياطية غير متاحة');
            }
            
            this.syncStatus.isRunning = true;
            this.syncStatus.progress = 0;
            this.syncStatus.errors = [];
            
            const backupId = `backup_${Date.now()}`;
            const backupMetadata = {
                id: backupId,
                timestamp: new Date().toISOString(),
                source: 'primary',
                type: 'full',
                status: 'in_progress',
                tables: this.tablesToBackup,
                totalRecords: 0,
                processedRecords: 0
            };
            
            // حفظ معلومات النسخة الاحتياطية
            await this.backupFirestore
                .collection('backup_metadata')
                .doc(backupId)
                .set(backupMetadata);
            
            let totalProcessed = 0;
            const tableCount = this.tablesToBackup.length;
            
            // نسخ كل جدول
            for (let i = 0; i < this.tablesToBackup.length; i++) {
                const tableName = this.tablesToBackup[i];
                
                try {
                    console.log(`📋 نسخ جدول: ${tableName}`);
                    
                    const result = await this.backupTable(tableName, backupId, options);
                    totalProcessed += result.recordCount;
                    
                    this.syncStatus.progress = Math.round(((i + 1) / tableCount) * 100);
                    
                    // تحديث واجهة المستخدم
                    this.updateBackupProgress(this.syncStatus.progress, `تم نسخ ${tableName}`);
                    
                } catch (error) {
                    console.error(`❌ خطأ في نسخ جدول ${tableName}:`, error);
                    this.syncStatus.errors.push(`${tableName}: ${error.message}`);
                }
            }
            
            // تحديث معلومات النسخة الاحتياطية
            await this.backupFirestore
                .collection('backup_metadata')
                .doc(backupId)
                .update({
                    status: this.syncStatus.errors.length > 0 ? 'completed_with_errors' : 'completed',
                    totalRecords: totalProcessed,
                    processedRecords: totalProcessed,
                    errors: this.syncStatus.errors,
                    completedAt: new Date().toISOString()
                });
            
            this.syncStatus.isRunning = false;
            this.syncStatus.lastSync = new Date().toISOString();
            await this.saveSystemState();
            
            console.log(`✅ تم إنشاء النسخة الاحتياطية بنجاح. معرف النسخة: ${backupId}`);
            
            return {
                success: true,
                backupId,
                totalRecords: totalProcessed,
                errors: this.syncStatus.errors
            };
            
        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
            this.syncStatus.isRunning = false;
            throw error;
        }
    }
    
    async backupTable(tableName, backupId, options = {}) {
        try {
            let recordCount = 0;
            let offset = 0;
            const batchSize = options.batchSize || 1000;
            
            // الحصول على آخر تاريخ نسخ لهذا الجدول (للنسخ التزايدي)
            const lastSyncDate = await this.getLastSyncDate(tableName);
            
            while (true) {
                // جلب البيانات من قاعدة البيانات الأساسية
                let query = this.primarySupabase
                    .from(tableName)
                    .select('*')
                    .range(offset, offset + batchSize - 1);
                
                // إضافة فلتر التاريخ للنسخ التزايدي
                if (lastSyncDate && options.incremental) {
                    query = query.gt('updated_at', lastSyncDate);
                }
                
                const { data, error } = await query;
                
                if (error) {
                    throw new Error(`خطأ في جلب البيانات من ${tableName}: ${error.message}`);
                }
                
                if (!data || data.length === 0) {
                    break; // انتهت البيانات
                }
                
                // حفظ البيانات في قاعدة البيانات الاحتياطية
                const batch = this.backupFirestore.batch();
                
                data.forEach(record => {
                    const docRef = this.backupFirestore
                        .collection(`backup_${backupId}`)
                        .doc(tableName)
                        .collection('data')
                        .doc(record.id?.toString() || `record_${recordCount}`);
                    
                    batch.set(docRef, {
                        ...record,
                        _backup_timestamp: new Date().toISOString(),
                        _source_table: tableName
                    });
                });
                
                await batch.commit();
                
                recordCount += data.length;
                offset += batchSize;
                
                // تحديث التقدم
                console.log(`📊 تم نسخ ${recordCount} سجل من جدول ${tableName}`);
                
                // توقف قصير لتجنب إرهاق الخادم
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // تحديث تاريخ آخر نسخ
            await this.updateLastSyncDate(tableName);
            
            return { recordCount };
            
        } catch (error) {
            console.error(`❌ خطأ في نسخ جدول ${tableName}:`, error);
            throw error;
        }
    }
    
    async getLastSyncDate(tableName) {
        try {
            const doc = await this.backupFirestore
                .collection('sync_metadata')
                .doc(tableName)
                .get();
            
            if (doc.exists) {
                return doc.data().lastSync;
            }
            
            return null;
        } catch (error) {
            console.error(`خطأ في جلب تاريخ آخر نسخ لجدول ${tableName}:`, error);
            return null;
        }
    }
    
    async updateLastSyncDate(tableName) {
        try {
            await this.backupFirestore
                .collection('sync_metadata')
                .doc(tableName)
                .set({
                    tableName,
                    lastSync: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }, { merge: true });
        } catch (error) {
            console.error(`خطأ في تحديث تاريخ آخر نسخ لجدول ${tableName}:`, error);
        }
    }
    
    async switchToBackupDatabase() {
        try {
            console.log('🔄 التبديل إلى قاعدة البيانات الاحتياطية...');
            
            if (!this.backupFirestore) {
                throw new Error('قاعدة البيانات الاحتياطية غير متاحة');
            }
            
            // تحديث العميل النشط
            this.currentDatabase = 'backup';
            
            // إنشاء wrapper للتوافق مع Supabase API
            window.supabaseClient = this.createFirebaseWrapper();
            
            await this.saveSystemState();
            
            console.log('✅ تم التبديل إلى قاعدة البيانات الاحتياطية');
            
            // إشعار المستخدم
            this.showDatabaseSwitchNotification('backup');
            
            return true;
            
        } catch (error) {
            console.error('❌ خطأ في التبديل إلى قاعدة البيانات الاحتياطية:', error);
            throw error;
        }
    }
    
    async switchToPrimaryDatabase() {
        try {
            console.log('🔄 التبديل إلى قاعدة البيانات الأساسية...');
            
            if (!this.primarySupabase) {
                throw new Error('قاعدة البيانات الأساسية غير متاحة');
            }
            
            // فحص صحة قاعدة البيانات الأساسية أولاً
            const { data, error } = await this.primarySupabase
                .from('mods')
                .select('count', { count: 'exact', head: true });
            
            if (error) {
                throw new Error(`قاعدة البيانات الأساسية لا تعمل: ${error.message}`);
            }
            
            // تحديث العميل النشط
            this.currentDatabase = 'primary';
            window.supabaseClient = this.primarySupabase;
            
            await this.saveSystemState();
            
            console.log('✅ تم التبديل إلى قاعدة البيانات الأساسية');
            
            // إشعار المستخدم
            this.showDatabaseSwitchNotification('primary');
            
            return true;
            
        } catch (error) {
            console.error('❌ خطأ في التبديل إلى قاعدة البيانات الأساسية:', error);
            throw error;
        }
    }
    
    createFirebaseWrapper() {
        // إنشاء wrapper للتوافق مع Supabase API
        const wrapper = {
            from: (tableName) => {
                return {
                    select: (columns = '*', options = {}) => {
                        return {
                            eq: (column, value) => this.firebaseQuery(tableName, 'eq', column, value, columns, options),
                            neq: (column, value) => this.firebaseQuery(tableName, 'neq', column, value, columns, options),
                            gt: (column, value) => this.firebaseQuery(tableName, 'gt', column, value, columns, options),
                            gte: (column, value) => this.firebaseQuery(tableName, 'gte', column, value, columns, options),
                            lt: (column, value) => this.firebaseQuery(tableName, 'lt', column, value, columns, options),
                            lte: (column, value) => this.firebaseQuery(tableName, 'lte', column, value, columns, options),
                            order: (column, options = {}) => this.firebaseOrder(tableName, column, options, columns),
                            limit: (count) => this.firebaseLimit(tableName, count, columns),
                            range: (from, to) => this.firebaseRange(tableName, from, to, columns)
                        };
                    },
                    insert: (data) => this.firebaseInsert(tableName, data),
                    update: (data) => this.firebaseUpdate(tableName, data),
                    delete: () => this.firebaseDelete(tableName)
                };
            },
            rpc: (functionName, params) => this.firebaseRPC(functionName, params)
        };
        
        return wrapper;
    }
    
    async firebaseQuery(tableName, operation, column, value, columns, options) {
        try {
            // تنفيذ استعلام Firebase بناءً على العملية
            let query = this.backupFirestore.collection(tableName);
            
            switch (operation) {
                case 'eq':
                    query = query.where(column, '==', value);
                    break;
                case 'neq':
                    query = query.where(column, '!=', value);
                    break;
                case 'gt':
                    query = query.where(column, '>', value);
                    break;
                case 'gte':
                    query = query.where(column, '>=', value);
                    break;
                case 'lt':
                    query = query.where(column, '<', value);
                    break;
                case 'lte':
                    query = query.where(column, '<=', value);
                    break;
            }
            
            const snapshot = await query.get();
            const data = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            
            return { data, error: null, count: options.count === 'exact' ? data.length : null };
            
        } catch (error) {
            console.error(`خطأ في استعلام Firebase ${tableName}:`, error);
            return { data: null, error: { message: error.message } };
        }
    }
    
    async firebaseInsert(tableName, data) {
        try {
            const docRef = await this.backupFirestore.collection(tableName).add({
                ...data,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            });
            
            return { data: { id: docRef.id, ...data }, error: null };
            
        } catch (error) {
            console.error(`خطأ في إدراج Firebase ${tableName}:`, error);
            return { data: null, error: { message: error.message } };
        }
    }
    
    updateBackupProgress(progress, message) {
        // تحديث واجهة المستخدم
        const progressBar = document.getElementById('backupProgress');
        const progressText = document.getElementById('backupProgressText');
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }
        
        if (progressText) {
            progressText.textContent = message;
        }
        
        // إرسال حدث مخصص
        window.dispatchEvent(new CustomEvent('backupProgress', {
            detail: { progress, message }
        }));
    }
    
    showDatabaseSwitchNotification(databaseType) {
        const message = databaseType === 'backup' 
            ? 'تم التبديل إلى قاعدة البيانات الاحتياطية (Firebase)'
            : 'تم التبديل إلى قاعدة البيانات الأساسية (Supabase)';
        
        const alertClass = databaseType === 'backup' ? 'alert-warning' : 'alert-success';
        
        this.showAlert(message, alertClass, 'fas fa-database');
    }
    
    showAlert(message, alertClass = 'alert-info', icon = 'fas fa-info-circle') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px; border-radius: 10px;';
        alertDiv.innerHTML = `
            <i class="${icon}" style="margin-left: 8px;"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
    
    // وظائف إضافية للإدارة
    async getBackupList() {
        try {
            const snapshot = await this.backupFirestore
                .collection('backup_metadata')
                .orderBy('timestamp', 'desc')
                .get();
            
            return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
            
        } catch (error) {
            console.error('خطأ في جلب قائمة النسخ الاحتياطية:', error);
            return [];
        }
    }
    
    async deleteBackup(backupId) {
        try {
            // حذف بيانات النسخة الاحتياطية
            const batch = this.backupFirestore.batch();
            
            // حذف معلومات النسخة
            batch.delete(this.backupFirestore.collection('backup_metadata').doc(backupId));
            
            // حذف البيانات (يتطلب حذف كل مجموعة فرعية)
            const backupCollection = this.backupFirestore.collection(`backup_${backupId}`);
            const snapshot = await backupCollection.get();
            
            snapshot.docs.forEach(doc => {
                batch.delete(doc.ref);
            });
            
            await batch.commit();
            
            console.log(`✅ تم حذف النسخة الاحتياطية: ${backupId}`);
            return true;
            
        } catch (error) {
            console.error(`خطأ في حذف النسخة الاحتياطية ${backupId}:`, error);
            throw error;
        }
    }
    
    getCurrentDatabaseInfo() {
        return {
            current: this.currentDatabase,
            primaryAvailable: !!this.primarySupabase,
            backupAvailable: !!this.backupFirestore,
            lastSync: this.syncStatus.lastSync,
            isRunning: this.syncStatus.isRunning
        };
    }

    async createIncrementalBackup() {
        try {
            console.log('🔄 بدء إنشاء نسخة احتياطية تزايدية...');

            return await this.createFullBackup({ incremental: true, batchSize: 500 });

        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية التزايدية:', error);
            throw error;
        }
    }

    async restoreFromBackup(backupId, options = {}) {
        try {
            console.log(`🔄 بدء استعادة البيانات من النسخة: ${backupId}`);

            if (!this.backupFirestore) {
                throw new Error('قاعدة البيانات الاحتياطية غير متاحة');
            }

            // التحقق من وجود النسخة الاحتياطية
            const backupDoc = await this.backupFirestore
                .collection('backup_metadata')
                .doc(backupId)
                .get();

            if (!backupDoc.exists) {
                throw new Error('النسخة الاحتياطية غير موجودة');
            }

            const backupData = backupDoc.data();

            if (backupData.status !== 'completed') {
                throw new Error('النسخة الاحتياطية غير مكتملة');
            }

            this.syncStatus.isRunning = true;
            this.syncStatus.progress = 0;
            this.syncStatus.errors = [];

            let totalProcessed = 0;
            const tableCount = backupData.tables.length;

            // استعادة كل جدول
            for (let i = 0; i < backupData.tables.length; i++) {
                const tableName = backupData.tables[i];

                try {
                    console.log(`📋 استعادة جدول: ${tableName}`);

                    const result = await this.restoreTable(tableName, backupId, options);
                    totalProcessed += result.recordCount;

                    this.syncStatus.progress = Math.round(((i + 1) / tableCount) * 100);

                    // تحديث واجهة المستخدم
                    this.updateBackupProgress(this.syncStatus.progress, `تم استعادة ${tableName}`);

                } catch (error) {
                    console.error(`❌ خطأ في استعادة جدول ${tableName}:`, error);
                    this.syncStatus.errors.push(`${tableName}: ${error.message}`);
                }
            }

            this.syncStatus.isRunning = false;

            console.log(`✅ تم استعادة البيانات بنجاح. إجمالي السجلات: ${totalProcessed}`);

            return {
                success: true,
                totalRecords: totalProcessed,
                errors: this.syncStatus.errors
            };

        } catch (error) {
            console.error('❌ خطأ في استعادة البيانات:', error);
            this.syncStatus.isRunning = false;
            throw error;
        }
    }

    async restoreTable(tableName, backupId, options = {}) {
        try {
            let recordCount = 0;

            // جلب البيانات من النسخة الاحتياطية
            const snapshot = await this.backupFirestore
                .collection(`backup_${backupId}`)
                .doc(tableName)
                .collection('data')
                .get();

            if (snapshot.empty) {
                console.log(`⚠️ لا توجد بيانات لاستعادة جدول ${tableName}`);
                return { recordCount: 0 };
            }

            // تنظيف الجدول الحالي إذا كان مطلوباً
            if (options.clearTable) {
                await this.clearTable(tableName);
            }

            // استعادة البيانات بدفعات
            const batchSize = options.batchSize || 1000;
            const docs = snapshot.docs;

            for (let i = 0; i < docs.length; i += batchSize) {
                const batch = docs.slice(i, i + batchSize);
                const dataToInsert = batch.map(doc => {
                    const data = doc.data();
                    // إزالة الحقول الخاصة بالنسخ الاحتياطي
                    delete data._backup_timestamp;
                    delete data._source_table;
                    return data;
                });

                // إدراج البيانات في قاعدة البيانات الأساسية
                const { error } = await this.primarySupabase
                    .from(tableName)
                    .insert(dataToInsert);

                if (error) {
                    console.error(`خطأ في إدراج البيانات في ${tableName}:`, error);
                    // محاولة إدراج السجلات واحداً تلو الآخر
                    for (const record of dataToInsert) {
                        try {
                            await this.primarySupabase.from(tableName).insert(record);
                            recordCount++;
                        } catch (singleError) {
                            console.error(`خطأ في إدراج سجل في ${tableName}:`, singleError);
                        }
                    }
                } else {
                    recordCount += dataToInsert.length;
                }

                console.log(`📊 تم استعادة ${recordCount} سجل من جدول ${tableName}`);

                // توقف قصير لتجنب إرهاق الخادم
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            return { recordCount };

        } catch (error) {
            console.error(`❌ خطأ في استعادة جدول ${tableName}:`, error);
            throw error;
        }
    }

    async clearTable(tableName) {
        try {
            console.log(`🗑️ تنظيف جدول ${tableName}...`);

            // حذف جميع السجلات من الجدول
            const { error } = await this.primarySupabase
                .from(tableName)
                .delete()
                .neq('id', 0); // حذف جميع السجلات

            if (error) {
                console.error(`خطأ في تنظيف جدول ${tableName}:`, error);
                throw error;
            }

            console.log(`✅ تم تنظيف جدول ${tableName}`);

        } catch (error) {
            console.error(`❌ خطأ في تنظيف جدول ${tableName}:`, error);
            throw error;
        }
    }

    async syncDatabases() {
        try {
            console.log('🔄 بدء مزامنة قواعد البيانات...');

            if (!this.primarySupabase || !this.backupFirestore) {
                throw new Error('إحدى قواعد البيانات غير متاحة');
            }

            // إنشاء نسخة احتياطية تزايدية
            const result = await this.createIncrementalBackup();

            console.log('✅ تم مزامنة قواعد البيانات بنجاح');
            return result;

        } catch (error) {
            console.error('❌ خطأ في مزامنة قواعد البيانات:', error);
            throw error;
        }
    }

    async cleanupOldBackups(daysToKeep = 30, keepSuccessfulOnly = false) {
        try {
            console.log(`🧹 بدء تنظيف النسخ الاحتياطية الأقدم من ${daysToKeep} يوم...`);

            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            let query = this.backupFirestore
                .collection('backup_metadata')
                .where('timestamp', '<', cutoffDate.toISOString());

            if (keepSuccessfulOnly) {
                query = query.where('status', '!=', 'completed');
            }

            const snapshot = await query.get();

            let deletedCount = 0;

            for (const doc of snapshot.docs) {
                try {
                    await this.deleteBackup(doc.id);
                    deletedCount++;
                } catch (error) {
                    console.error(`خطأ في حذف النسخة ${doc.id}:`, error);
                }
            }

            console.log(`✅ تم حذف ${deletedCount} نسخة احتياطية قديمة`);

            return { deletedCount };

        } catch (error) {
            console.error('❌ خطأ في تنظيف النسخ الاحتياطية:', error);
            throw error;
        }
    }
}

// إنشاء مثيل عام للنظام
window.databaseBackupSystem = new DatabaseBackupSystem();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseBackupSystem;
}
