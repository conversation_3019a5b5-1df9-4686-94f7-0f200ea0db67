CREATE TABLE IF NOT EXISTS custom_mod_dialogs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100) DEFAULT 'تم',
    show_dont_show_again B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS custom_dialog_mods (
    id SERIAL PRIMARY KEY,
    dialog_id INTEGER NOT NULL,
    mod_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_dialog_mods_dialog_id FOREIGN KEY (dialog_id) REFERENCES custom_mod_dialogs(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_dialog_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_dialog_mod UNIQUE (dialog_id, mod_id)
);

CREATE TABLE IF NOT EXISTS custom_copyright_mods (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_copyright_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_copyright_mod UNIQUE (mod_id)
);
