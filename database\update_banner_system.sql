-- ========================================
-- تحديث نظام البانرات لدعم ربط المودات
-- Update Banner System to Support Mod Linking
-- ========================================

-- 1. إضافة حقول جديدة لجدول banner_ads
-- Add new fields to banner_ads table
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS target_mod_id UUID;
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS action_type VARCHAR(20) DEFAULT 'url';

-- إضافة قيود المفاتيح الخارجية
-- Add foreign key constraints
DO $$
BEGIN
    -- التحقق من وجود جدول mods قبل إضافة القيد
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mods') THEN
        -- إضافة قيد المفتاح الخارجي للمود
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_banner_ads_target_mod_id'
        ) THEN
            ALTER TABLE banner_ads 
            ADD CONSTRAINT fk_banner_ads_target_mod_id 
            FOREIGN KEY (target_mod_id) REFERENCES mods(id) ON DELETE SET NULL;
        END IF;
    END IF;
END $$;

-- 2. إضافة فهارس للأداء
-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_banner_ads_target_mod_id ON banner_ads(target_mod_id);
CREATE INDEX IF NOT EXISTS idx_banner_ads_action_type ON banner_ads(action_type);
CREATE INDEX IF NOT EXISTS idx_banner_ads_banner_type_active ON banner_ads(banner_type, is_active);

-- 3. تحديث البيانات الموجودة
-- Update existing data
UPDATE banner_ads 
SET action_type = CASE 
    WHEN banner_type = 'subscription' THEN 'subscription'
    WHEN banner_type = 'mod' THEN 'mod'
    ELSE 'url'
END
WHERE action_type = 'url' OR action_type IS NULL;

-- 4. إنشاء دالة للحصول على معلومات البانر الكاملة
-- Create function to get complete banner information
CREATE OR REPLACE FUNCTION get_banner_with_details(banner_id INTEGER)
RETURNS JSONB AS $$
DECLARE
    banner_record RECORD;
    mod_record RECORD;
    campaign_record RECORD;
    result JSONB;
BEGIN
    -- الحصول على معلومات البانر الأساسية
    SELECT * INTO banner_record
    FROM banner_ads
    WHERE id = banner_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('error', 'Banner not found');
    END IF;
    
    -- بناء النتيجة الأساسية
    result := to_jsonb(banner_record);
    
    -- إضافة معلومات المود إذا كان البانر من نوع مود
    IF banner_record.banner_type = 'mod' AND banner_record.target_mod_id IS NOT NULL THEN
        SELECT id, title, description, description_ar, image_url, category, creator_name
        INTO mod_record
        FROM mods
        WHERE id = banner_record.target_mod_id;
        
        IF FOUND THEN
            result := result || jsonb_build_object('target_mod', to_jsonb(mod_record));
        END IF;
    END IF;
    
    -- إضافة معلومات الحملة إذا كان البانر من نوع اشتراك
    IF banner_record.banner_type = 'subscription' AND banner_record.campaign_id IS NOT NULL THEN
        SELECT id, title_ar, title_en, description_ar, description_en, subscription_duration_days, is_active
        INTO campaign_record
        FROM free_subscription_campaigns
        WHERE id = banner_record.campaign_id;
        
        IF FOUND THEN
            result := result || jsonb_build_object('target_campaign', to_jsonb(campaign_record));
        END IF;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 5. إنشاء دالة للحصول على جميع البانرات النشطة مع التفاصيل
-- Create function to get all active banners with details
CREATE OR REPLACE FUNCTION get_active_banners_with_details()
RETURNS JSONB AS $$
DECLARE
    banner_record RECORD;
    banners_array JSONB := '[]'::jsonb;
    banner_details JSONB;
BEGIN
    FOR banner_record IN 
        SELECT * FROM banner_ads 
        WHERE is_active = true 
        ORDER BY display_order ASC, created_at DESC
    LOOP
        banner_details := get_banner_with_details(banner_record.id);
        banners_array := banners_array || banner_details;
    END LOOP;
    
    RETURN jsonb_build_object('banners', banners_array, 'count', jsonb_array_length(banners_array));
END;
$$ LANGUAGE plpgsql;

-- 6. إنشاء دالة لإحصائيات البانرات
-- Create function for banner statistics
CREATE OR REPLACE FUNCTION get_banner_statistics()
RETURNS JSONB AS $$
DECLARE
    total_banners INTEGER;
    active_banners INTEGER;
    regular_banners INTEGER;
    mod_banners INTEGER;
    subscription_banners INTEGER;
BEGIN
    -- إجمالي البانرات
    SELECT COUNT(*) INTO total_banners FROM banner_ads;
    
    -- البانرات النشطة
    SELECT COUNT(*) INTO active_banners FROM banner_ads WHERE is_active = true;
    
    -- البانرات العادية
    SELECT COUNT(*) INTO regular_banners FROM banner_ads WHERE banner_type = 'regular' OR banner_type IS NULL;
    
    -- بانرات المودات
    SELECT COUNT(*) INTO mod_banners FROM banner_ads WHERE banner_type = 'mod';
    
    -- بانرات الاشتراك
    SELECT COUNT(*) INTO subscription_banners FROM banner_ads WHERE banner_type = 'subscription';
    
    RETURN jsonb_build_object(
        'total_banners', total_banners,
        'active_banners', active_banners,
        'inactive_banners', total_banners - active_banners,
        'regular_banners', regular_banners,
        'mod_banners', mod_banners,
        'subscription_banners', subscription_banners
    );
END;
$$ LANGUAGE plpgsql;

-- 7. إنشاء دالة للتحقق من صحة البانر
-- Create function to validate banner data
CREATE OR REPLACE FUNCTION validate_banner_data(
    p_banner_type VARCHAR(20),
    p_target_mod_id UUID,
    p_campaign_id UUID,
    p_click_url TEXT
)
RETURNS JSONB AS $$
DECLARE
    validation_result JSONB := jsonb_build_object('valid', true, 'errors', '[]'::jsonb);
    errors JSONB := '[]'::jsonb;
BEGIN
    -- التحقق من نوع البانر
    IF p_banner_type NOT IN ('regular', 'mod', 'subscription') THEN
        errors := errors || jsonb_build_array('نوع البانر غير صالح');
    END IF;
    
    -- التحقق من بانر المود
    IF p_banner_type = 'mod' THEN
        IF p_target_mod_id IS NULL THEN
            errors := errors || jsonb_build_array('يجب تحديد مود للبانر من نوع مود');
        ELSE
            -- التحقق من وجود المود
            IF NOT EXISTS (SELECT 1 FROM mods WHERE id = p_target_mod_id) THEN
                errors := errors || jsonb_build_array('المود المحدد غير موجود');
            END IF;
        END IF;
    END IF;
    
    -- التحقق من بانر الاشتراك
    IF p_banner_type = 'subscription' THEN
        IF p_campaign_id IS NULL THEN
            errors := errors || jsonb_build_array('يجب تحديد حملة للبانر من نوع اشتراك');
        ELSE
            -- التحقق من وجود الحملة
            IF NOT EXISTS (SELECT 1 FROM free_subscription_campaigns WHERE id = p_campaign_id) THEN
                errors := errors || jsonb_build_array('الحملة المحددة غير موجودة');
            END IF;
        END IF;
    END IF;
    
    -- التحقق من البانر العادي
    IF p_banner_type = 'regular' THEN
        IF p_click_url IS NULL OR p_click_url = '' THEN
            errors := errors || jsonb_build_array('يجب تحديد رابط للبانر العادي');
        END IF;
    END IF;
    
    -- تحديث النتيجة
    IF jsonb_array_length(errors) > 0 THEN
        validation_result := jsonb_build_object('valid', false, 'errors', errors);
    END IF;
    
    RETURN validation_result;
END;
$$ LANGUAGE plpgsql;

-- 8. إنشاء trigger لتحديث updated_at تلقائياً
-- Create trigger for automatic updated_at update
CREATE OR REPLACE FUNCTION update_banner_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger إذا لم يكن موجوداً
DROP TRIGGER IF EXISTS trigger_update_banner_updated_at ON banner_ads;
CREATE TRIGGER trigger_update_banner_updated_at
    BEFORE UPDATE ON banner_ads
    FOR EACH ROW
    EXECUTE FUNCTION update_banner_updated_at();

-- 9. منح الصلاحيات للدوال الجديدة
-- Grant permissions for new functions
GRANT EXECUTE ON FUNCTION get_banner_with_details(INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_active_banners_with_details() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_banner_statistics() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION validate_banner_data(VARCHAR, UUID, UUID, TEXT) TO anon, authenticated;

-- 10. إضافة سياسات RLS محدثة
-- Add updated RLS policies
DO $$
BEGIN
    -- التحقق من تفعيل RLS
    IF NOT EXISTS (
        SELECT 1 FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = 'banner_ads' AND c.relrowsecurity = true
    ) THEN
        ALTER TABLE banner_ads ENABLE ROW LEVEL SECURITY;
    END IF;
    
    -- حذف السياسات القديمة إذا كانت موجودة
    DROP POLICY IF EXISTS "Allow public read access to active banners" ON banner_ads;
    DROP POLICY IF EXISTS "Allow admin full access to banners" ON banner_ads;
    
    -- إضافة سياسات جديدة
    CREATE POLICY "Allow public read access to active banners"
        ON banner_ads FOR SELECT
        USING (is_active = true);
    
    CREATE POLICY "Allow admin full access to banners"
        ON banner_ads FOR ALL
        USING (true);
END $$;

-- 11. إضافة تعليقات توضيحية
-- Add documentation comments
COMMENT ON COLUMN banner_ads.target_mod_id IS 'معرف المود المستهدف للبانرات من نوع مود';
COMMENT ON COLUMN banner_ads.action_type IS 'نوع الإجراء عند النقر على البانر (url, mod, subscription)';
COMMENT ON FUNCTION get_banner_with_details(INTEGER) IS 'الحصول على معلومات البانر مع التفاصيل الكاملة';
COMMENT ON FUNCTION get_active_banners_with_details() IS 'الحصول على جميع البانرات النشطة مع التفاصيل';
COMMENT ON FUNCTION get_banner_statistics() IS 'الحصول على إحصائيات البانرات';
COMMENT ON FUNCTION validate_banner_data(VARCHAR, UUID, UUID, TEXT) IS 'التحقق من صحة بيانات البانر';

-- 12. إنشاء view للبانرات مع التفاصيل
-- Create view for banners with details
CREATE OR REPLACE VIEW banner_ads_with_details AS
SELECT 
    ba.*,
    m.title as mod_title,
    m.description as mod_description,
    m.image_url as mod_image_url,
    m.category as mod_category,
    fsc.title_ar as campaign_title_ar,
    fsc.title_en as campaign_title_en,
    fsc.description_ar as campaign_description_ar,
    fsc.subscription_duration_days as campaign_duration
FROM banner_ads ba
LEFT JOIN mods m ON ba.target_mod_id = m.id
LEFT JOIN free_subscription_campaigns fsc ON ba.campaign_id = fsc.id;

-- منح صلاحيات القراءة للـ view
GRANT SELECT ON banner_ads_with_details TO anon, authenticated;

-- 13. إنشاء دالة لتنظيف البانرات القديمة
-- Create function to cleanup old banners
CREATE OR REPLACE FUNCTION cleanup_old_banners(days_old INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- حذف البانرات غير النشطة والقديمة
    DELETE FROM banner_ads 
    WHERE is_active = false 
    AND created_at < CURRENT_DATE - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION cleanup_old_banners(INTEGER) TO authenticated;

-- 14. إضافة بيانات تجريبية (اختياري)
-- Add sample data (optional)
DO $$
BEGIN
    -- إضافة بانر تجريبي فقط إذا لم توجد بانرات
    IF NOT EXISTS (SELECT 1 FROM banner_ads LIMIT 1) THEN
        INSERT INTO banner_ads (
            title, 
            description, 
            image_url, 
            banner_type, 
            action_type,
            click_url,
            display_order, 
            is_active
        ) VALUES (
            'بانر ترحيبي', 
            'بانر ترحيبي للمستخدمين الجدد', 
            'https://via.placeholder.com/800x200/FFD700/000000?text=Welcome+Banner',
            'regular',
            'url',
            'https://example.com',
            1, 
            true
        );
    END IF;
END $$;

-- 15. التحقق من نجاح التحديث
-- Verify successful update
SELECT 
    'تم تحديث نظام البانرات بنجاح!' as message,
    COUNT(*) as total_banners,
    COUNT(*) FILTER (WHERE is_active = true) as active_banners,
    COUNT(*) FILTER (WHERE banner_type = 'mod') as mod_banners,
    COUNT(*) FILTER (WHERE banner_type = 'subscription') as subscription_banners
FROM banner_ads;

-- عرض هيكل الجدول المحدث
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'banner_ads' 
ORDER BY ordinal_position;
