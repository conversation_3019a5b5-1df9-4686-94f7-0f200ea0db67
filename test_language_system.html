<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Language System - Mod Etaris</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #21221f;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #333;
            border-radius: 10px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #444;
            border-radius: 8px;
        }
        .test-button {
            background: linear-gradient(45deg, #ffcc00, #ff9800);
            color: black;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background-color: #555;
            border-radius: 5px;
            font-family: monospace;
        }
        .language-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #ffcc00;
            color: black;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .mock-mod-card {
            background-color: #222;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 2px solid #ffcc00;
        }
        .mock-mod-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .mock-mod-description {
            color: #ccc;
            line-height: 1.5;
        }
        .mock-mod-tags {
            margin-top: 10px;
        }
        .mock-tag {
            display: inline-block;
            background: #ffcc00;
            color: black;
            padding: 3px 8px;
            margin: 2px;
            border-radius: 3px;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="language-indicator" id="languageIndicator">
        Loading...
    </div>

    <div class="test-container">
        <h1>🧪 Language System Test Page</h1>
        <p>This page tests the language selection and translation system for Mod Etaris.</p>

        <!-- Language Control Section -->
        <div class="test-section">
            <h2>🌍 Language Control</h2>
            <button class="test-button" onclick="switchToArabic()">Switch to Arabic (العربية)</button>
            <button class="test-button" onclick="switchToEnglish()">Switch to English</button>
            <button class="test-button" onclick="clearLanguageData()">Clear Language Data</button>
            <div class="test-result" id="languageResult"></div>
        </div>

        <!-- Translation Test Section -->
        <div class="test-section">
            <h2>📝 Translation Test</h2>
            <button class="test-button" onclick="testTranslations()">Test All Translations</button>
            <div class="test-result" id="translationResult"></div>
        </div>

        <!-- Mock Mod Display Section -->
        <div class="test-section">
            <h2>🎮 Mock Mod Display</h2>
            <button class="test-button" onclick="displayMockMods()">Display Mock Mods</button>
            <div id="mockModsContainer"></div>
        </div>

        <!-- Local Storage Test Section -->
        <div class="test-section">
            <h2>💾 Local Storage Test</h2>
            <button class="test-button" onclick="testLocalStorage()">Test Local Storage</button>
            <div class="test-result" id="localStorageResult"></div>
        </div>

        <!-- Direction Test Section -->
        <div class="test-section">
            <h2>↔️ Text Direction Test</h2>
            <button class="test-button" onclick="testTextDirection()">Test Text Direction</button>
            <div class="test-result" id="directionResult"></div>
        </div>
    </div>

    <!-- Include the translation system -->
    <script src="app/src/main/assets/translations.js"></script>

    <script>
        // Mock translation manager if not loaded
        if (typeof window.translationManager === 'undefined') {
            console.warn('Translation manager not loaded, creating mock...');
            window.translationManager = {
                currentLanguage: 'en',
                setLanguage: function(lang) {
                    this.currentLanguage = lang;
                    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
                    document.documentElement.lang = lang;
                    updateLanguageIndicator();
                },
                getCurrentLanguage: function() {
                    return this.currentLanguage;
                },
                t: function(key) {
                    const translations = {
                        ar: {
                            'app_name': 'مود إيتاريس',
                            'description': 'الوصف',
                            'size': 'الحجم',
                            'version': 'الإصدار',
                            'category': 'الفئة',
                            'no_description': 'لا يوجد وصف متاح.',
                            'shader_warning_title': '⚠️ تحذير مهم',
                            'shader_warning_description': 'جميع الشيدرز تعمل فقط مع إصدار ماين كرافت المُعدّل'
                        },
                        en: {
                            'app_name': 'Mod Etaris',
                            'description': 'Description',
                            'size': 'Size',
                            'version': 'Version',
                            'category': 'Category',
                            'no_description': 'No description available.',
                            'shader_warning_title': '⚠️ Important Warning',
                            'shader_warning_description': 'All shaders only work with Minecraft Patched version'
                        }
                    };
                    return translations[this.currentLanguage]?.[key] || key;
                }
            };
            window.t = (key) => window.translationManager.t(key);
        }

        // Update language indicator
        function updateLanguageIndicator() {
            const indicator = document.getElementById('languageIndicator');
            const lang = window.translationManager.getCurrentLanguage();
            indicator.textContent = lang === 'ar' ? 'العربية' : 'English';
        }

        // Language switching functions
        function switchToArabic() {
            window.translationManager.setLanguage('ar');
            localStorage.setItem('selectedLanguage', 'ar');
            document.getElementById('languageResult').textContent = 'Switched to Arabic (تم التبديل للعربية)';
            updateLanguageIndicator();
        }

        function switchToEnglish() {
            window.translationManager.setLanguage('en');
            localStorage.setItem('selectedLanguage', 'en');
            document.getElementById('languageResult').textContent = 'Switched to English';
            updateLanguageIndicator();
        }

        function clearLanguageData() {
            localStorage.removeItem('selectedLanguage');
            localStorage.removeItem('languageSelected');
            document.getElementById('languageResult').textContent = 'Language data cleared. Refresh page to see language selection.';
        }

        // Test translations
        function testTranslations() {
            const testKeys = ['app_name', 'description', 'size', 'version', 'category', 'no_description'];
            let result = 'Translation Test Results:\n\n';
            
            testKeys.forEach(key => {
                const translation = t(key);
                result += `${key}: ${translation}\n`;
            });
            
            document.getElementById('translationResult').textContent = result;
        }

        // Display mock mods
        function displayMockMods() {
            const mockMods = [
                {
                    name: 'Super Shader Pack',
                    description: 'Amazing shader that enhances graphics',
                    description_ar: 'شيدر رائع يحسن من جودة الرسوميات',
                    size: '15MB',
                    version: '1.2.0',
                    category: 'Shaders'
                },
                {
                    name: 'Cool Addon',
                    description: 'Adds new features to the game',
                    description_ar: 'يضيف ميزات جديدة للعبة',
                    size: '8MB',
                    version: '2.1.0',
                    category: 'Addons'
                }
            ];

            const container = document.getElementById('mockModsContainer');
            container.innerHTML = '';

            mockMods.forEach(mod => {
                const modCard = document.createElement('div');
                modCard.className = 'mock-mod-card';
                
                const description = getLocalizedDescription(mod);
                
                modCard.innerHTML = `
                    <div class="mock-mod-title">${mod.name}</div>
                    <div class="mock-mod-description">${description}</div>
                    <div class="mock-mod-tags">
                        <span class="mock-tag">${t('size')}: ${mod.size}</span>
                        <span class="mock-tag">${t('version')}: ${mod.version}</span>
                        <span class="mock-tag">${t('category')}: ${mod.category}</span>
                    </div>
                `;
                
                container.appendChild(modCard);
            });
        }

        // Mock getLocalizedDescription function
        function getLocalizedDescription(item) {
            const currentLanguage = window.translationManager.getCurrentLanguage();
            
            if (currentLanguage === 'ar') {
                return item.description_ar || item.description || t('no_description');
            } else {
                return item.description || t('no_description');
            }
        }

        // Test local storage
        function testLocalStorage() {
            const selectedLanguage = localStorage.getItem('selectedLanguage');
            const languageSelected = localStorage.getItem('languageSelected');
            
            let result = 'Local Storage Status:\n\n';
            result += `selectedLanguage: ${selectedLanguage || 'Not set'}\n`;
            result += `languageSelected: ${languageSelected || 'Not set'}\n`;
            result += `Current Language: ${window.translationManager.getCurrentLanguage()}\n`;
            
            document.getElementById('localStorageResult').textContent = result;
        }

        // Test text direction
        function testTextDirection() {
            const currentDir = document.documentElement.dir;
            const currentLang = document.documentElement.lang;
            
            let result = 'Text Direction Status:\n\n';
            result += `Document Direction: ${currentDir || 'Not set'}\n`;
            result += `Document Language: ${currentLang || 'Not set'}\n`;
            result += `Expected for Arabic: dir="rtl", lang="ar"\n`;
            result += `Expected for English: dir="ltr", lang="en"\n`;
            
            document.getElementById('directionResult').textContent = result;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial language from localStorage if available
            const savedLanguage = localStorage.getItem('selectedLanguage');
            if (savedLanguage && window.translationManager) {
                window.translationManager.setLanguage(savedLanguage);
            }
            updateLanguageIndicator();
            
            console.log('Language System Test Page Loaded');
            console.log('Translation Manager:', window.translationManager);
        });
    </script>
</body>
</html>
