<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار منشئ الاشتراك الموحد</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid #ffd700;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #ffd700;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .test-header p {
            color: #ccc;
            font-size: 1.1rem;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .test-section h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .test-button {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: #000;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 10px;
            display: none;
        }

        .test-result.success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid #22c55e;
            color: #22c55e;
        }

        .test-result.error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
        }

        .test-info {
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid #3b82f6;
            color: #3b82f6;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-success { background: #22c55e; }
        .status-error { background: #ef4444; }
        .status-pending { background: #f59e0b; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> اختبار منشئ الاشتراك الموحد</h1>
            <p>اختبار جميع وظائف النظام الموحد لإنشاء الاشتراكات المجانية</p>
        </div>

        <div class="test-info">
            <i class="fas fa-info-circle"></i>
            <strong>ملاحظة:</strong> هذه الصفحة لاختبار وظائف المنشئ الموحد. تأكد من وجود جميع الملفات المطلوبة.
        </div>

        <!-- اختبار النافذة الموحدة -->
        <div class="test-section">
            <h3><i class="fas fa-window-maximize"></i> اختبار النافذة الموحدة</h3>
            <p>اختبار فتح النافذة الموحدة وعرض الخيارات</p>
            <button class="test-button" onclick="testUnifiedModal()">
                <i class="fas fa-play"></i>
                اختبار النافذة الموحدة
            </button>
            <div class="test-result" id="modal-result"></div>
        </div>

        <!-- اختبار الطرق المختلفة -->
        <div class="test-section">
            <h3><i class="fas fa-route"></i> اختبار طرق الإنشاء</h3>
            <p>اختبار فتح كل طريقة من طرق إنشاء الاشتراك</p>
            
            <button class="test-button" onclick="testMethod('quick')">
                <i class="fas fa-bolt"></i>
                اختبار الإنشاء السريع
            </button>
            
            <button class="test-button" onclick="testMethod('advanced')">
                <i class="fas fa-cogs"></i>
                اختبار الإنشاء المتقدم
            </button>
            
            <button class="test-button" onclick="testMethod('complete')">
                <i class="fas fa-rocket"></i>
                اختبار الإنشاء الشامل
            </button>
            
            <button class="test-button" onclick="testMethod('manage')">
                <i class="fas fa-chart-bar"></i>
                اختبار إدارة الحملات
            </button>

            <button class="test-button" onclick="testMethod('popup')">
                <i class="fas fa-window-maximize"></i>
                اختبار النوافذ المنبثقة
            </button>

            <button class="test-button" onclick="testMethod('dialog')">
                <i class="fas fa-comment-dots"></i>
                اختبار المربعات المخصصة
            </button>

            <button class="test-button" onclick="testMethod('backup')">
                <i class="fas fa-shield-alt"></i>
                اختبار الإعلانات الاحتياطية
            </button>

            <button class="test-button" onclick="testMethod('notification')">
                <i class="fas fa-bell"></i>
                اختبار إشعارات التطبيق
            </button>

            <div class="test-result" id="methods-result"></div>
        </div>

        <!-- اختبار الملفات -->
        <div class="test-section">
            <h3><i class="fas fa-file-check"></i> فحص الملفات المطلوبة</h3>
            <p>التحقق من وجود جميع الملفات المطلوبة للنظام</p>
            <button class="test-button" onclick="checkFiles()">
                <i class="fas fa-search"></i>
                فحص الملفات
            </button>
            <div class="test-result" id="files-result"></div>
        </div>

        <!-- حالة النظام -->
        <div class="test-section">
            <h3><i class="fas fa-heartbeat"></i> حالة النظام</h3>
            <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-top: 15px;">
                <div>
                    <span class="status-indicator status-pending" id="unified-status"></span>
                    النافذة الموحدة
                </div>
                <div>
                    <span class="status-indicator status-pending" id="quick-status"></span>
                    الإنشاء السريع
                </div>
                <div>
                    <span class="status-indicator status-pending" id="advanced-status"></span>
                    الإنشاء المتقدم
                </div>
                <div>
                    <span class="status-indicator status-pending" id="complete-status"></span>
                    الإنشاء الشامل
                </div>
                <div>
                    <span class="status-indicator status-pending" id="manage-status"></span>
                    إدارة الحملات
                </div>
                <div>
                    <span class="status-indicator status-pending" id="popup-status"></span>
                    النوافذ المنبثقة
                </div>
                <div>
                    <span class="status-indicator status-pending" id="dialog-status"></span>
                    المربعات المخصصة
                </div>
                <div>
                    <span class="status-indicator status-pending" id="backup-status"></span>
                    الإعلانات الاحتياطية
                </div>
                <div>
                    <span class="status-indicator status-pending" id="notification-status"></span>
                    إشعارات التطبيق
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين الملفات المطلوبة -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="unified-admin.js"></script>

    <script>
        // اختبار النافذة الموحدة
        function testUnifiedModal() {
            const result = document.getElementById('modal-result');
            result.style.display = 'block';
            
            try {
                if (typeof showUnifiedSubscriptionCreator === 'function') {
                    showUnifiedSubscriptionCreator();
                    result.className = 'test-result success';
                    result.innerHTML = '<i class="fas fa-check"></i> تم فتح النافذة الموحدة بنجاح!';
                    updateStatus('unified-status', 'success');
                } else {
                    throw new Error('دالة showUnifiedSubscriptionCreator غير موجودة');
                }
            } catch (error) {
                result.className = 'test-result error';
                result.innerHTML = '<i class="fas fa-times"></i> خطأ: ' + error.message;
                updateStatus('unified-status', 'error');
            }
        }

        // اختبار طرق الإنشاء
        function testMethod(method) {
            const result = document.getElementById('methods-result');
            result.style.display = 'block';
            
            try {
                if (typeof selectCreationMethod === 'function') {
                    selectCreationMethod(method);
                    result.className = 'test-result success';
                    result.innerHTML = '<i class="fas fa-check"></i> تم اختبار طريقة ' + getMethodName(method) + ' بنجاح!';
                    updateStatus(method + '-status', 'success');
                } else {
                    throw new Error('دالة selectCreationMethod غير موجودة');
                }
            } catch (error) {
                result.className = 'test-result error';
                result.innerHTML = '<i class="fas fa-times"></i> خطأ في اختبار ' + getMethodName(method) + ': ' + error.message;
                updateStatus(method + '-status', 'error');
            }
        }

        // فحص الملفات
        async function checkFiles() {
            const result = document.getElementById('files-result');
            result.style.display = 'block';
            result.className = 'test-result';
            result.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري فحص الملفات...';

            const files = [
                'unified-admin.js',
                'easy_campaign_creator.html',
                'subscription_admin.html',
                'unified-subscription-banner.html',
                'enhanced_tasks_admin.html',
                'banner_admin.html',
                'custom_dialogs.html',
                'backup-ads-manager.html'
            ];

            let allFilesExist = true;
            let fileStatus = '';

            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        fileStatus += `<div style="color: #22c55e;"><i class="fas fa-check"></i> ${file}</div>`;
                    } else {
                        fileStatus += `<div style="color: #ef4444;"><i class="fas fa-times"></i> ${file} (غير موجود)</div>`;
                        allFilesExist = false;
                    }
                } catch (error) {
                    fileStatus += `<div style="color: #ef4444;"><i class="fas fa-times"></i> ${file} (خطأ في الوصول)</div>`;
                    allFilesExist = false;
                }
            }

            if (allFilesExist) {
                result.className = 'test-result success';
                result.innerHTML = '<i class="fas fa-check"></i> جميع الملفات موجودة!<br>' + fileStatus;
            } else {
                result.className = 'test-result error';
                result.innerHTML = '<i class="fas fa-exclamation-triangle"></i> بعض الملفات مفقودة:<br>' + fileStatus;
            }
        }

        // تحديث حالة المؤشر
        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = 'status-indicator status-' + status;
            }
        }

        // الحصول على اسم الطريقة
        function getMethodName(method) {
            const names = {
                quick: 'الإنشاء السريع',
                advanced: 'الإنشاء المتقدم',
                complete: 'الإنشاء الشامل',
                manage: 'إدارة الحملات',
                popup: 'النوافذ المنبثقة',
                dialog: 'المربعات المخصصة',
                backup: 'الإعلانات الاحتياطية',
                notification: 'إشعارات التطبيق'
            };
            return names[method] || method;
        }

        // تشغيل فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkFiles();
            }, 1000);
        });
    </script>
</body>
</html>
