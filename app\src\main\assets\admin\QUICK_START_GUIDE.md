# دليل البدء السريع - Quick Start Guide

## 🚀 البدء السريع للميزات الجديدة

### 1. إعداد نظام الإعلانات الاحتياطية

#### الخطوة 1: إنشاء قاعدة البيانات
```sql
-- تشغيل هذا الملف في Supabase SQL Editor
-- Run this file in Supabase SQL Editor
\i backup_ads_table.sql
```

#### الخطوة 2: إنشاء Storage Bucket
1. اذهب إلى Supabase Dashboard
2. اختر Storage من القائمة الجانبية
3. أنشئ bucket جديد باسم `backup-ads`
4. اجعل الـ bucket عام (Public)

#### الخطوة 3: تعيين الصلاحيات
```sql
-- إعطاء صلاحيات للجداول الجديدة
-- Grant permissions for new tables
GRANT ALL ON backup_ads TO anon, authenticated;
GRANT ALL ON backup_ads_stats TO anon, authenticated;
GRANT ALL ON backup_ads_summary TO anon, authenticated;
```

### 2. إنشاء أول إعلان احتياطي

#### الطريقة السهلة:
1. اذهب إلى لوحة الإدارة
2. اختر "الإعلانات الاحتياطية"
3. انقر "إنشاء إعلان احتياطي جديد"
4. املأ البيانات المطلوبة:
   - العنوان: "إعلان تجريبي"
   - النوع: صورة
   - الأولوية: 5
   - المدة: 5 ثوان
5. ارفع صورة (أقل من 2MB)
6. احفظ الإعلان

#### الطريقة المباشرة (SQL):
```sql
INSERT INTO backup_ads (
    title, 
    description, 
    ad_type, 
    media_url, 
    priority, 
    duration, 
    click_action
) VALUES (
    'إعلان تجريبي', 
    'هذا إعلان تجريبي للاختبار', 
    'image', 
    'https://via.placeholder.com/600x400/4CAF50/white?text=إعلان+تجريبي', 
    5, 
    5, 
    'none'
);
```

### 3. إنشاء حملة موحدة (اشتراك + بانر)

1. اذهب إلى لوحة الإدارة
2. اختر "منشئ موحد" من قسم البانرات
3. اتبع الخطوات الأربع:

#### الخطوة 1: معلومات الحملة
- العنوان بالعربية: "اشتراك مجاني لمدة شهر"
- العنوان بالإنجليزية: "Free 1-Month Subscription"
- الوصف بكلا اللغتين

#### الخطوة 2: إعدادات الاشتراك
- المدة: 30 يوم
- الحد الأقصى للمستخدمين: (اختياري)
- تاريخ الانتهاء: (اختياري)

#### الخطوة 3: تصميم البانر
- عنوان البانر: "اشتراك مجاني!"
- ارفع صورة البانر (1200x400 مفضل)
- ارفع صورة النافذة المنبثقة (اختياري)

#### الخطوة 4: المراجعة والنشر
- راجع جميع البيانات
- فعّل الحملة والبانر
- انقر "إنشاء الحملة والبانر"

### 4. اختبار النظام

#### اختبار الإعلانات الاحتياطية:
1. اذهب إلى `admin/test-backup-ads.html`
2. انقر "فحص حالة النظام"
3. جرب "اختبار عرض الإعلان"
4. تأكد من ظهور الإعلان

#### اختبار التكامل مع التطبيق:
```javascript
// في console المتصفح
// In browser console
window.backupAds.show('test_mod_id', 'Addons');
```

### 5. تحسين إعدادات المستخدم

#### التحقق من التطبيق الفوري:
1. اذهب إلى صفحة إعدادات المستخدم
2. غيّر أي إعداد (مثل الوضع المظلم)
3. تأكد من تطبيق التغيير فوراً على التطبيق الرئيسي

## 🔧 استكشاف الأخطاء الشائعة

### مشكلة: "supabaseClient already declared"
**الحل:**
```javascript
// تأكد من عدم تعريف supabaseClient في أكثر من ملف
// Make sure supabaseClient is not defined in multiple files
// استخدم backupAdsSupabaseClient بدلاً من supabaseClient في backup-ads-integration.js
```

### مشكلة: "toggleDrawer is not defined"
**الحل:**
- تأكد من تحميل `script.js` في النهاية
- أو استخدم الدالة الاحتياطية المضافة في `index.html`

### مشكلة: عدم ظهور الإعلانات الاحتياطية
**التحقق:**
1. تأكد من وجود إعلانات نشطة في قاعدة البيانات:
```sql
SELECT * FROM backup_ads WHERE is_active = true;
```

2. تأكد من تحميل ملف التكامل:
```html
<script src="backup-ads-integration.js"></script>
```

3. فحص console للأخطاء:
```javascript
console.log(window.backupAds);
```

### مشكلة: فشل رفع الصور
**التحقق:**
1. تأكد من إنشاء bucket `backup-ads` في Supabase Storage
2. تأكد من أن الـ bucket عام (Public)
3. تأكد من صلاحيات الرفع:
```sql
-- في Supabase SQL Editor
-- In Supabase SQL Editor
INSERT INTO storage.buckets (id, name, public) VALUES ('backup-ads', 'backup-ads', true);
```

## 📊 مراقبة الأداء

### إحصائيات الإعلانات الاحتياطية:
```sql
-- عرض إحصائيات سريعة
-- Quick statistics view
SELECT * FROM backup_ads_summary;

-- إحصائيات مفصلة
-- Detailed statistics
SELECT 
    ba.title,
    COUNT(bas.id) as total_events,
    COUNT(CASE WHEN bas.event_type = 'view' THEN 1 END) as views,
    COUNT(CASE WHEN bas.event_type = 'click' THEN 1 END) as clicks
FROM backup_ads ba
LEFT JOIN backup_ads_stats bas ON ba.id = bas.ad_id
GROUP BY ba.id, ba.title
ORDER BY views DESC;
```

### مراقبة الأخطاء:
```sql
-- عرض الأخطاء الأخيرة
-- Show recent errors
SELECT * FROM backup_ads_stats 
WHERE event_type = 'error' 
ORDER BY timestamp DESC 
LIMIT 10;
```

## 🎯 نصائح للاستخدام الأمثل

### 1. أولوية الإعلانات:
- استخدم أولوية 8-10 للإعلانات المهمة
- استخدم أولوية 5-7 للإعلانات العادية
- استخدم أولوية 1-4 للإعلانات التجريبية

### 2. مدة العرض:
- 3-5 ثوان للإعلانات البسيطة
- 5-10 ثوان للإعلانات التفاعلية
- 10-15 ثانية للفيديوهات القصيرة

### 3. استهداف الفئات:
- اتركها فارغة للعرض على جميع الفئات
- حدد فئات معينة للإعلانات المتخصصة

### 4. تحسين الصور:
- استخدم تنسيق WebP للحصول على أفضل ضغط
- الأبعاد المفضلة: 1200x400 للبانرات
- احرص على أن يكون حجم الملف أقل من 2MB

## 📞 الحصول على المساعدة

### سجلات النظام:
```javascript
// تفعيل السجلات المفصلة
// Enable detailed logging
localStorage.setItem('debugBackupAds', 'true');

// عرض حالة النظام
// Show system status
console.log('Backup Ads Status:', window.backupAds);
```

### تصدير البيانات للدعم:
1. اذهب إلى صفحة الاختبار: `admin/test-backup-ads.html`
2. شغّل "تشغيل جميع الاختبارات"
3. انقر "تصدير النتائج"
4. أرسل الملف المُصدّر مع طلب الدعم

---

**تاريخ آخر تحديث:** يناير 2025  
**الإصدار:** 2.1.0  
**للمساعدة:** راجع ملف `NEW_FEATURES_README.md` للتفاصيل الكاملة
