plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}

android {
    signingConfigs {
        create("customDebugConfig") {
            storeFile = file("../modetaris.jks") // Corrected path to project root
            storePassword = "GGSIDIVALL35"    // New store password
            keyAlias = "key0"                 // New alias
            keyPassword = "GGSIDIVALL35"        // New key password (same as store)
        }
      
    }

    namespace = "com.sidimohamed.modetaris"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.sidimohamed.modetaris"
        minSdk = 24
        targetSdk = 35
        versionCode = 21
        versionName = "2.1"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        
        // دعم جميع معماريات المعالج
        ndk {
            abiFilters.addAll(listOf("armeabi-v7a", "arm64-v8a", "x86", "x86_64"))
        }
    }

    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("customDebugConfig")

            // تحسينات إضافية للإصدار النهائي
            isDebuggable = false
            isJniDebuggable = false
            isRenderscriptDebuggable = false
            isPseudoLocalesEnabled = false

            // تحسين حجم APK
            ndk {
                debugSymbolLevel = "NONE"
            }
        }
        getByName("debug") {
            signingConfig = signingConfigs.getByName("customDebugConfig")

            // تحسينات للتطوير
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true

            // تسريع البناء في وضع التطوير
            ndk {
                debugSymbolLevel = "SYMBOL_TABLE"
            }
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
        // تحسينات Kotlin
        freeCompilerArgs += listOf(
            "-opt-in=kotlin.RequiresOptIn",
            "-Xjvm-default=all"
        )
    }
    buildFeatures {
        compose = true
        // تعطيل الميزات غير المستخدمة لتسريع البناء
        buildConfig = false
        aidl = false
        renderScript = false
        resValues = false
        shaders = false
    }

    // تحسينات إضافية
    packagingOptions {
        resources {
            excludes += setOf(
                "META-INF/DEPENDENCIES",
                "META-INF/LICENSE",
                "META-INF/LICENSE.txt",
                "META-INF/NOTICE",
                "META-INF/NOTICE.txt",
                "META-INF/*.kotlin_module"
            )
        }
    }
    
    // إضافة تكوين لتوافق الأجهزة
    bundle {
        abi {
            enableSplit = false // تعطيل تقسيم ABIs للتأكد من توافق جميع الأجهزة
        }
        language {
            enableSplit = true
        }
        density {
            enableSplit = true
        }
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.playServicesAds) // Use camelCase alias for AdMob
    implementation(libs.androidx.ui)
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0") // Added SwipeRefreshLayout dependency
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.google.material) // Added Google Material dependency for Snackbar
    implementation("androidx.localbroadcastmanager:localbroadcastmanager:1.1.0") // Add LocalBroadcastManager dependency directly
    implementation(libs.androidx.core.splashscreen) // Add splash screen dependency

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}
