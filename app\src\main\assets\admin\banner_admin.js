// Banner Admin Panel JavaScript
// إدارة البانر ونظام الاشتراك المجاني

// Supabase configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// متطلبات الصورة
const IMAGE_REQUIREMENTS = {
    banner: {
        maxWidth: 800,
        maxHeight: 200,
        minWidth: 300,
        minHeight: 100,
        maxSize: 2 * 1024 * 1024 // 2MB
    },
    popup: {
        maxWidth: 600,
        maxHeight: 400,
        minWidth: 200,
        minHeight: 200,
        maxSize: 2 * 1024 * 1024 // 2MB
    }
};

// Global variables
let currentBannerImage = null;
let currentPopupImage = null;
let currentCampaignId = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Banner Admin Panel loaded');
    loadExistingBanners();
});

// تبديل التبويبات
function switchTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // إظهار التبويب المحدد
    document.getElementById(tabName + '-tab').classList.add('active');
    event.target.classList.add('active');
}

// تبديل حقول الحملة
function toggleCampaignFields() {
    const bannerType = document.getElementById('bannerType').value;
    const campaignFields = document.getElementById('campaignFields');
    
    if (bannerType === 'subscription') {
        campaignFields.style.display = 'block';
    } else {
        campaignFields.style.display = 'none';
    }
}

// معالجة رفع الصور
function handleImageUpload(input, type) {
    const file = input.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        showError('يرجى اختيار ملف صورة صالح');
        return;
    }

    // التحقق من حجم الملف
    if (file.size > IMAGE_REQUIREMENTS[type].maxSize) {
        showError(`حجم الصورة كبير جداً. الحد الأقصى ${IMAGE_REQUIREMENTS[type].maxSize / (1024 * 1024)}MB`);
        return;
    }

    // قراءة الصورة للتحقق من الأبعاد
    const reader = new FileReader();
    reader.onload = function(e) {
        const img = new Image();
        img.onload = function() {
            const requirements = IMAGE_REQUIREMENTS[type];
            
            // التحقق من الأبعاد
            if (img.width > requirements.maxWidth || img.height > requirements.maxHeight) {
                showError(`أبعاد الصورة كبيرة جداً. الحد الأقصى ${requirements.maxWidth}x${requirements.maxHeight} بكسل`);
                return;
            }
            
            if (img.width < requirements.minWidth || img.height < requirements.minHeight) {
                showError(`أبعاد الصورة صغيرة جداً. الحد الأدنى ${requirements.minWidth}x${requirements.minHeight} بكسل`);
                return;
            }

            // إظهار معاينة الصورة
            const previewId = type === 'banner' ? 'bannerPreview' : 'popupPreview';
            const preview = document.getElementById(previewId);
            preview.src = e.target.result;
            preview.style.display = 'block';

            // حفظ الصورة في المتغير العام
            if (type === 'banner') {
                currentBannerImage = file;
            } else {
                currentPopupImage = file;
            }

            showSuccess(`تم رفع صورة ${type === 'banner' ? 'البانر' : 'النافذة المنبثقة'} بنجاح`);
        };
        img.src = e.target.result;
    };
    reader.readAsDataURL(file);
}

// رفع الصورة إلى Supabase Storage
async function uploadImageToSupabase(file, folder) {
    try {
        const fileName = `${folder}/${Date.now()}_${file.name}`;
        
        const { data, error } = await supabaseClient.storage
            .from('banner-images')
            .upload(fileName, file);

        if (error) {
            console.error('Error uploading image:', error);
            throw error;
        }

        // الحصول على الرابط العام
        const { data: urlData } = supabaseClient.storage
            .from('banner-images')
            .getPublicUrl(fileName);

        return urlData.publicUrl;
    } catch (error) {
        console.error('Error in uploadImageToSupabase:', error);
        throw error;
    }
}

// حفظ البانر
async function saveBanner() {
    try {
        showLoading('جاري حفظ البانر...');

        const bannerType = document.getElementById('bannerType').value;
        const title = document.getElementById('bannerTitle').value;
        const description = document.getElementById('bannerDescription').value;
        const url = document.getElementById('bannerUrl').value;
        const isActive = document.getElementById('bannerActive').checked;

        if (!title.trim()) {
            showError('يرجى إدخال عنوان البانر');
            return;
        }

        if (!currentBannerImage) {
            showError('يرجى رفع صورة البانر');
            return;
        }

        // رفع صورة البانر
        const bannerImageUrl = await uploadImageToSupabase(currentBannerImage, 'banners');
        
        let popupImageUrl = null;
        let campaignId = null;

        // إذا كان بانر اشتراك مجاني
        if (bannerType === 'subscription') {
            const campaignData = await createSubscriptionCampaign();
            campaignId = campaignData.id;
            
            if (currentPopupImage) {
                popupImageUrl = await uploadImageToSupabase(currentPopupImage, 'popups');
            }
        }

        // حفظ البانر في قاعدة البيانات
        const bannerData = {
            title,
            description,
            image_url: bannerImageUrl,
            click_url: url,
            is_active: isActive,
            banner_type: bannerType,
            campaign_id: campaignId,
            image_width: currentBannerImage.width || null,
            image_height: currentBannerImage.height || null
        };

        const { data, error } = await supabaseClient
            .from('banner_ads')
            .insert([bannerData]);

        if (error) {
            console.error('Error saving banner:', error);
            throw error;
        }

        showSuccess('تم حفظ البانر بنجاح!');
        clearForm();
        loadExistingBanners();

    } catch (error) {
        console.error('Error in saveBanner:', error);
        showError('حدث خطأ أثناء حفظ البانر: ' + error.message);
    } finally {
        hideLoading();
    }
}

// إنشاء حملة اشتراك مجاني
async function createSubscriptionCampaign() {
    const titleAr = document.getElementById('campaignTitleAr').value;
    const titleEn = document.getElementById('campaignTitleEn').value;
    const descAr = document.getElementById('campaignDescAr').value;
    const descEn = document.getElementById('campaignDescEn').value;
    const duration = parseInt(document.getElementById('subscriptionDuration').value);
    const maxUsers = document.getElementById('maxUsers').value;
    const endDate = document.getElementById('campaignEndDate').value;

    if (!titleAr || !titleEn || !descAr || !descEn) {
        throw new Error('يرجى ملء جميع حقول الحملة المطلوبة');
    }

    let popupImageUrl = null;
    if (currentPopupImage) {
        popupImageUrl = await uploadImageToSupabase(currentPopupImage, 'popups');
    }

    const campaignData = {
        title_ar: titleAr,
        title_en: titleEn,
        description_ar: descAr,
        description_en: descEn,
        banner_image_url: '', // سيتم تحديثه لاحقاً
        popup_image_url: popupImageUrl,
        subscription_duration_days: duration,
        max_users: maxUsers ? parseInt(maxUsers) : null,
        end_date: endDate ? new Date(endDate).toISOString() : null,
        is_active: true
    };

    const { data, error } = await supabaseClient
        .from('free_subscription_campaigns')
        .insert([campaignData])
        .select();

    if (error) {
        console.error('Error creating campaign:', error);
        throw error;
    }

    return data[0];
}

// معاينة البانر
function previewBanner() {
    const title = document.getElementById('bannerTitle').value;
    const description = document.getElementById('bannerDescription').value;
    const bannerType = document.getElementById('bannerType').value;
    
    if (!currentBannerImage) {
        showError('يرجى رفع صورة البانر أولاً');
        return;
    }

    const previewContainer = document.getElementById('previewContainer');
    const bannerPreview = document.getElementById('bannerPreview').src;

    let previewHTML = `
        <div style="max-width: 600px; margin: 0 auto;">
            <h3 style="color: #ffd700; margin-bottom: 20px;">معاينة البانر</h3>
            
            <!-- معاينة البانر -->
            <div style="background: #000; border-radius: 12px; overflow: hidden; margin-bottom: 20px; border: 2px solid #ffd700;">
                <img src="${bannerPreview}" style="width: 100%; height: auto; display: block;">
            </div>
            
            <div style="text-align: center; color: white;">
                <h4>${title}</h4>
                <p style="color: #ccc;">${description}</p>
                <p style="color: #ffd700; font-size: 14px;">نوع البانر: ${bannerType === 'subscription' ? 'اشتراك مجاني' : 'عادي'}</p>
            </div>
    `;

    // إذا كان بانر اشتراك مجاني، أضف معاينة النافذة المنبثقة
    if (bannerType === 'subscription') {
        const campaignTitleAr = document.getElementById('campaignTitleAr').value;
        const campaignDescAr = document.getElementById('campaignDescAr').value;
        const duration = document.getElementById('subscriptionDuration').value;
        
        previewHTML += `
            <div style="margin-top: 30px;">
                <h3 style="color: #ffd700; margin-bottom: 20px;">معاينة النافذة المنبثقة</h3>
                
                <div style="background: linear-gradient(135deg, #1a1a2e, #16213e); border-radius: 15px; padding: 30px; border: 2px solid #ffd700; max-width: 400px; margin: 0 auto; text-align: center;">
                    ${currentPopupImage ? `<img src="${document.getElementById('popupPreview').src}" style="width: 100%; max-width: 200px; border-radius: 10px; margin-bottom: 20px;">` : ''}
                    
                    <h2 style="color: #ffd700; margin-bottom: 15px;">${campaignTitleAr || 'احصل على اشتراك مجاني'}</h2>
                    
                    <p style="color: white; margin-bottom: 20px; line-height: 1.6;">${campaignDescAr || 'احصل على ميزة مجانية تمكنك من تحميل المودات بدون إعلانات'}</p>
                    
                    <div style="background: rgba(255, 215, 0, 0.1); border-radius: 10px; padding: 15px; margin-bottom: 20px;">
                        <p style="color: #ffd700; font-weight: bold;">مدة الاشتراك: ${duration} يوم</p>
                    </div>
                    
                    <button style="background: linear-gradient(45deg, #ffd700, #ffcc00); color: black; border: none; padding: 12px 30px; border-radius: 25px; font-weight: bold; cursor: pointer;">
                        ابدأ المهام
                    </button>
                </div>
            </div>
        `;
    }

    previewHTML += '</div>';
    previewContainer.innerHTML = previewHTML;
    document.getElementById('previewModal').style.display = 'flex';
}

// إغلاق المعاينة
function closePreview() {
    document.getElementById('previewModal').style.display = 'none';
}

// مسح النموذج
function clearForm() {
    document.getElementById('bannerTitle').value = '';
    document.getElementById('bannerDescription').value = '';
    document.getElementById('bannerUrl').value = '';
    document.getElementById('bannerActive').checked = false;
    document.getElementById('bannerType').value = 'regular';
    
    // مسح حقول الحملة
    document.getElementById('campaignTitleAr').value = '';
    document.getElementById('campaignTitleEn').value = '';
    document.getElementById('campaignDescAr').value = '';
    document.getElementById('campaignDescEn').value = '';
    document.getElementById('subscriptionDuration').value = '30';
    document.getElementById('maxUsers').value = '';
    document.getElementById('campaignEndDate').value = '';
    
    // إخفاء المعاينات
    document.getElementById('bannerPreview').style.display = 'none';
    document.getElementById('popupPreview').style.display = 'none';
    
    // إخفاء حقول الحملة
    toggleCampaignFields();
    
    // مسح المتغيرات العامة
    currentBannerImage = null;
    currentPopupImage = null;
    currentCampaignId = null;
}

// تحميل البانرات الموجودة
async function loadExistingBanners() {
    try {
        const { data, error } = await supabaseClient
            .from('banner_ads')
            .select('*')
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error loading banners:', error);
            return;
        }

        console.log('Loaded banners:', data);
        // يمكن إضافة عرض البانرات الموجودة هنا
    } catch (error) {
        console.error('Error in loadExistingBanners:', error);
    }
}

// دوال المساعدة للرسائل
function showError(message) {
    // إزالة الرسائل السابقة
    removeMessages();
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    
    const container = document.querySelector('.tab-content.active');
    container.insertBefore(errorDiv, container.firstChild);
    
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

function showSuccess(message) {
    removeMessages();
    
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    
    const container = document.querySelector('.tab-content.active');
    container.insertBefore(successDiv, container.firstChild);
    
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 5000);
}

function showLoading(message) {
    removeMessages();
    
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'success-message';
    loadingDiv.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${message}`;
    loadingDiv.id = 'loadingMessage';
    
    const container = document.querySelector('.tab-content.active');
    container.insertBefore(loadingDiv, container.firstChild);
}

function hideLoading() {
    const loadingDiv = document.getElementById('loadingMessage');
    if (loadingDiv && loadingDiv.parentNode) {
        loadingDiv.parentNode.removeChild(loadingDiv);
    }
}

function removeMessages() {
    const messages = document.querySelectorAll('.error-message, .success-message');
    messages.forEach(msg => {
        if (msg.parentNode) {
            msg.parentNode.removeChild(msg);
        }
    });
}
