# 🚨 تعليمات الإصلاح العاجل

## المشكلة الحالية
التطبيق يواجه أخطاء 400 لأن الأعمدة المطلوبة غير موجودة في جدول `mods` في قاعدة البيانات.

## ✅ الحل السريع (خطوتان فقط)

### الخطوة 1: تشغيل SQL مباشرة في Supabase
1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. اختر مشروعك: `ytqxxodyecdeosnqoure`
3. اذهب إلى **SQL Editor** من القائمة الجانبية
4. انسخ والصق المحتوى من ملف `app/src/main/assets/admin/direct-database-fix.sql`
5. اضغط **Run** لتشغيل السكريبت

### الخطوة 2: إعادة تحميل التطبيق
1. أعد تحميل صفحة التطبيق (F5)
2. تحقق من Console - يجب أن ترى:
   ```
   🚨 Emergency Fix activated
   ✅ Emergency fixes completed
   ```

---

## 🔧 ما يفعله الإصلاح

### في قاعدة البيانات:
- ✅ إضافة جميع الأعمدة المفقودة لجدول `mods`
- ✅ إنشاء جدول `error_reports`
- ✅ إنشاء الفهارس للأداء
- ✅ إنشاء دوال RPC مطلوبة
- ✅ إدراج بيانات تجريبية

### في التطبيق:
- ✅ إصلاح طارئ يعمل تلقائ<|im_start|>
- ✅ بيانات احتياطية في حالة فشل الاستعلامات
- ✅ تجاوز الدوال المشكلة

---

## 📋 السكريبت SQL للنسخ

```sql
-- إضافة الأعمدة المفقودة
ALTER TABLE mods 
ADD COLUMN IF NOT EXISTS description_ar TEXT,
ADD COLUMN IF NOT EXISTS image_urls TEXT[],
ADD COLUMN IF NOT EXISTS creator_name TEXT,
ADD COLUMN IF NOT EXISTS creator_social_media JSONB,
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE;

-- إنشاء جدول error_reports
CREATE TABLE IF NOT EXISTS error_reports (
    id SERIAL PRIMARY KEY,
    category TEXT,
    "errorCode" TEXT,
    "errorMessage" TEXT,
    timestamp TIMESTAMP DEFAULT NOW(),
    "userAgent" TEXT
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_is_featured ON mods(is_featured);
CREATE INDEX IF NOT EXISTS idx_mods_is_popular ON mods(is_popular);

-- إنشاء دالة execute_sql
CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS TEXT AS $$
BEGIN
    EXECUTE sql_query;
    RETURN 'Success';
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Error: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إدراج بيانات تجريبية
INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 'Sample Addon', 'Sample addon', 'إضافة تجريبية', '/app/src/main/assets/image/icon_Addons.png', 'Addons', 100, 50, false, false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Addons' LIMIT 1);

INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 'Sample Shader', 'Sample shader', 'شيدر تجريبي', '/app/src/main/assets/image/icon_shaders.png', 'Shaders', 80, 40, false, false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Shaders' LIMIT 1);

INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 'Sample Texture', 'Sample texture', 'نسيج تجريبي', '/app/src/main/assets/image/texter.png', 'Texture', 120, 60, false, false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Texture' LIMIT 1);

INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 'Sample Map', 'Sample map', 'خريطة تجريبية', '/app/src/main/assets/image/icon_Addons.png', 'Maps', 90, 45, false, false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Maps' LIMIT 1);

INSERT INTO mods (name, description, description_ar, image_url, category, downloads, likes, is_featured, is_popular)
SELECT 'Sample Seed', 'Sample seed', 'بذرة تجريبية', '/app/src/main/assets/image/icon_Addons.png', 'Seeds', 70, 35, false, false
WHERE NOT EXISTS (SELECT 1 FROM mods WHERE category = 'Seeds' LIMIT 1);

-- منح الصلاحيات
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon;

-- رسالة تأكيد
SELECT 'Database fix completed!' as status, COUNT(*) as total_mods FROM mods;
```

---

## 🎯 النتائج المتوقعة

بعد تشغيل الإصلاح:

### ✅ ستختفي هذه الأخطاء:
- `Failed to load resource: the server responded with a status of 400`
- `column "is_featured" does not exist`
- `Error executing SQL: cannot change return type of existing function`

### ✅ ستظهر هذه الرسائل:
- `🚨 Emergency Fix activated`
- `✅ Emergency fixes completed`
- `Database fix completed!`

### ✅ ستعمل هذه الوظائف:
- تحميل المودات من جميع الفئات
- عرض البيانات بشكل صحيح
- عدم ظهور أخطاء في Console

---

## 🔍 للتحقق من نجاح الإصلاح

### في Supabase:
1. اذهب إلى **Table Editor**
2. اختر جدول `mods`
3. تأكد من وجود الأعمدة الجديدة:
   - `description_ar`
   - `is_featured`
   - `is_popular`
   - `creator_name`

### في التطبيق:
1. أعد تحميل الصفحة
2. تحقق من Console (F12)
3. يجب ألا ترى أخطاء 400
4. يجب أن تظهر المودات في جميع الأقسام

---

## 📞 إذا لم يعمل الإصلاح

### تحقق من:
1. **الاتصال بـ Supabase:** تأكد من صحة URL و API Key
2. **الصلاحيات:** تأكد من أن المستخدم `anon` له صلاحيات كافية
3. **الجداول:** تأكد من وجود جدول `mods` في قاعدة البيانات

### خطوات إضافية:
1. جرب تشغيل السكريبت SQL مرة أخرى
2. تحقق من **Logs** في Supabase Dashboard
3. أعد تحميل التطبيق عدة مرات

---

## 🎉 خلاصة

هذا الإصلاح سيحل جميع المشاكل الحالية ويجعل التطبيق يعمل بشكل طبيعي. الإصلاح بسيط ومباشر ولا يحتاج خبرة تقنية عالية.

**🚀 بعد تطبيق الإصلاح، التطبيق سيعمل بدون أخطاء! 🚀**
