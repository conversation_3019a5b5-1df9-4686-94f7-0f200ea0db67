document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('searchInput');
    const sectionsContainer = document.getElementById('sectionsContainer');
    const loadingIndicator = document.getElementById('loadingIndicator');
    let allMods = []; // To store all mods data once fetched

    // --- Function to fetch mod data from Supabase ---
    async function fetchMods() {
        loadingIndicator.style.display = 'flex'; // Show loading
        try {
            // Check if supabaseClient is available (from script.js)
            if (typeof supabaseClient === 'undefined') {
                throw new Error("Supabase client is not initialized. Make sure script.js is loaded first.");
            }

            // Fetch all mods from the 'mods' table
            const { data: items, error } = await supabaseClient
                .from('mods') // Ensure this is the correct table name
                .select('*') // Select all columns
                .order('created_at', { ascending: false }); // Optional: order by newest first

            if (error) {
                console.error("Error fetching mods from Supabase:", error);
                throw error; // Re-throw the error to be caught by the outer catch block
            }

            allMods = items || []; // Store fetched mods, default to empty array if null/undefined
            console.log("Fetched mods from Supabase:", allMods); // Log fetched data

            // Initial display (show "Type to search..." message)
            filterAndDisplayMods(''); 

        } catch (error) {
            console.error("Error in fetchMods:", error);
            sectionsContainer.innerHTML = '<p style="color: red; text-align: center;">Error loading mods. Please try again later.</p>';
        } finally {
            loadingIndicator.style.display = 'none'; // Hide loading
        }
    }

    // --- Function to filter and display mods ---
    function filterAndDisplayMods(searchTerm) {
        const lowerCaseSearchTerm = searchTerm.toLowerCase().trim();
        sectionsContainer.innerHTML = ''; // Clear previous results
        loadingIndicator.style.display = 'flex'; // Show loading during filtering/rendering

        // Ensure allMods is an array before filtering
        if (!Array.isArray(allMods)) {
            console.error("allMods is not an array:", allMods);
            sectionsContainer.innerHTML = '<p style="color: orange; text-align: center;">Could not load mod data correctly.</p>';
            loadingIndicator.style.display = 'none';
            return; 
        }

        const filteredMods = allMods.filter(mod => {
            // Check if mod and mod.name exist and are strings before calling toLowerCase
            const modName = mod && mod.name && typeof mod.name === 'string' ? mod.name.toLowerCase() : '';
            const modDescription = mod && mod.description && typeof mod.description === 'string' ? mod.description.toLowerCase() : '';
            const modCategory = mod && mod.category && typeof mod.category === 'string' ? mod.category.toLowerCase() : '';
            
            return modName.includes(lowerCaseSearchTerm) || 
                   modDescription.includes(lowerCaseSearchTerm) ||
                   modCategory.includes(lowerCaseSearchTerm);
        });

        if (filteredMods.length === 0 && lowerCaseSearchTerm !== '') {
            sectionsContainer.innerHTML = '<p style="color: white; text-align: center;">No mods found matching your search.</p>';
        } else if (filteredMods.length === 0 && lowerCaseSearchTerm === '') {
             sectionsContainer.innerHTML = '<p style="color: white; text-align: center;">Type to search for mods.</p>'; // Initial state or empty search
        } else {
            // Use the same createModCard function as in script.js (ensure it's available)
            // You might need to copy/paste the createModCard function here or load script.js
            // *before* search.js in search.html, or create a shared utility file.
            // Use the createModElement function from script.js for vertical layout ('mod-card')
             if (typeof createModElement === 'function') {
                 filteredMods.forEach(mod => {
                     // Use 'mod-card' for the vertical style consistent with categories
                     const modElement = createModElement(mod, 'mod-card');
                     sectionsContainer.appendChild(modElement);
                 });
                 // Initialize lazy loading for the newly added images
                 if (typeof initializeLazyLoading === 'function') {
                     initializeLazyLoading('#sectionsContainer .lazy-load');
                 } else {
                      console.error("initializeLazyLoading function is not defined. Make sure script.js is loaded before search.js.");
                 }
             } else {
                 console.error("createModElement function is not defined. Make sure script.js is loaded before search.js.");
                 sectionsContainer.innerHTML = '<p style="color: red; text-align: center;">Error displaying mods: UI function missing.</p>';
             }
        }
         loadingIndicator.style.display = 'none'; // Hide loading
    }

    // --- Event Listener for Search Input ---
    let debounceTimer;
    searchInput.addEventListener('input', (e) => {
        clearTimeout(debounceTimer);
        const searchTerm = e.target.value;
        // Debounce the search to avoid filtering on every keystroke
        debounceTimer = setTimeout(() => {
            filterAndDisplayMods(searchTerm);
        }, 300); // Adjust debounce time (in ms) as needed
    });

    // --- Initial Load ---
     fetchMods();

     // --- Event Delegation for Mod Clicks ---
     sectionsContainer.addEventListener('click', (event) => {
         // Find the closest parent element that is a mod card
         const modCard = event.target.closest('.mod-card');

         if (modCard) {
             const modId = modCard.getAttribute('data-id');
             console.log(`Delegated click detected on mod card with ID: ${modId}`);

             // Find the corresponding mod data from the allMods array
             const clickedModData = allMods.find(mod => mod.id === modId);

             if (clickedModData) {
                 // Call showModal (defined in script.js) with the found data
                 if (typeof showModal === 'function') {
                     showModal(clickedModData);
                 } else {
                     console.error("showModal function is not defined. Ensure script.js is loaded before search.js.");
                 }
             } else {
                 console.error(`Could not find mod data for ID: ${modId} in allMods array.`);
             }
         }
         // If the click wasn't on a mod card (or inside one), do nothing
     });
 });

 // IMPORTANT: Ensure the `createModCard` function (likely from script.js)
 // is accessible in this scope. You might need to:
// 1. Load script.js *before* search.js in search.html.
// 2. Copy the createModCard function into this file.
// 3. Create a separate utility JS file with shared functions and load it first.
// Also ensure that functions called by createModCard (like openModal, toggleLike) are available.
