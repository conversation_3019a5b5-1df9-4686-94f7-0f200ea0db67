<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل الصور</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        .fix-button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            width: 100%;
            max-width: 300px;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .status {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        .button-container {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ إصلاح مشاكل الصور</h1>
        
        <div class="button-container">
            <button class="fix-button" onclick="fixAllImages()">إصلاح جميع مشاكل الصور</button>
            <button class="fix-button" onclick="createPlaceholder()">إنشاء صورة بديلة</button>
            <button class="fix-button" onclick="testImages()">اختبار الصور</button>
            <button class="fix-button" onclick="clearCache()">مسح ذاكرة التخزين المؤقت</button>
        </div>

        <div id="status" class="status">
            <div class="info">جاهز لإصلاح مشاكل الصور...</div>
        </div>

        <div class="button-container">
            <button class="fix-button" onclick="goBack()">العودة للتطبيق</button>
        </div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${timestamp}] ${message}`;
            statusDiv.appendChild(div);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        // Default placeholder image
        const DEFAULT_PLACEHOLDER = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZmE1MDA7c3RvcC1vcGFjaXR5OjEiIC8+PHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZmY2YjAwO3N0b3Atb3BhY2l0eToxIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSI4MCIgcj0iMzAiIGZpbGw9IiNmZmYiIG9wYWNpdHk9IjAuOCIvPjxwb2x5Z29uIHBvaW50cz0iNjAsOTAgMTQwLDkwIDEyMCwxMzAgODAsMTMwIiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjgiLz48dGV4dCB4PSI1MCUiIHk9IjE2MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+Tm8gSW1hZ2U8L3RleHQ+PC9zdmc+';

        function fixAllImages() {
            log('🔧 بدء إصلاح جميع مشاكل الصور...', 'info');
            
            // Store placeholder globally
            localStorage.setItem('DEFAULT_PLACEHOLDER_IMAGE', DEFAULT_PLACEHOLDER);
            log('✅ تم حفظ الصورة البديلة الافتراضية', 'success');

            // Fix image loading functions
            const fixScript = `
                // Enhanced image error handler
                window.handleImageError = function(img, retryCount = 0) {
                    const maxRetries = 2;
                    const originalSrc = img.getAttribute('data-original-src') || img.src;
                    
                    console.warn('🖼️ Image failed to load:', img.src);

                    if (retryCount < maxRetries) {
                        setTimeout(() => {
                            if (retryCount === 0 && originalSrc.includes('../image/')) {
                                img.src = originalSrc.replace('../image/', 'image/');
                                img.setAttribute('data-original-src', originalSrc);
                            } else if (retryCount === 1 && originalSrc.includes('image/')) {
                                img.src = originalSrc.replace('image/', '../image/');
                            } else {
                                img.src = localStorage.getItem('DEFAULT_PLACEHOLDER_IMAGE') || '${DEFAULT_PLACEHOLDER}';
                                img.alt = 'صورة غير متاحة';
                                img.classList.add('placeholder-image');
                            }
                            
                            img.onerror = () => window.handleImageError(img, retryCount + 1);
                        }, 500 * (retryCount + 1));
                    } else {
                        img.src = localStorage.getItem('DEFAULT_PLACEHOLDER_IMAGE') || '${DEFAULT_PLACEHOLDER}';
                        img.alt = 'صورة غير متاحة';
                        img.classList.add('placeholder-image');
                        img.style.opacity = '0.8';
                    }
                };

                // Enhanced getMainImage function
                window.getMainImageSafe = function(imageUrls, fallbackPath = null) {
                    if (!imageUrls) {
                        return fallbackPath || localStorage.getItem('DEFAULT_PLACEHOLDER_IMAGE') || '${DEFAULT_PLACEHOLDER}';
                    }

                    try {
                        let urls = [];
                        
                        if (Array.isArray(imageUrls)) {
                            urls = imageUrls;
                        } else if (typeof imageUrls === 'string') {
                            if (imageUrls.startsWith('http') || imageUrls.startsWith('data:')) {
                                urls = [imageUrls];
                            } else {
                                try {
                                    const parsed = JSON.parse(imageUrls);
                                    urls = Array.isArray(parsed) ? parsed : [parsed];
                                } catch (e) {
                                    if (imageUrls.includes('http') || imageUrls.includes('image/')) {
                                        return imageUrls;
                                    }
                                    return fallbackPath || localStorage.getItem('DEFAULT_PLACEHOLDER_IMAGE') || '${DEFAULT_PLACEHOLDER}';
                                }
                            }
                        }

                        const validUrls = urls.filter(url => {
                            if (typeof url !== 'string') return false;
                            return url.startsWith('http') || 
                                   url.startsWith('data:') || 
                                   url.includes('image/') ||
                                   url.startsWith('/');
                        });

                        if (validUrls.length > 0) {
                            return validUrls[0];
                        }

                    } catch (error) {
                        console.error('❌ Error processing image URLs:', error);
                    }

                    return fallbackPath || localStorage.getItem('DEFAULT_PLACEHOLDER_IMAGE') || '${DEFAULT_PLACEHOLDER}';
                };

                // Override existing functions
                if (typeof window.getMainImage === 'undefined') {
                    window.getMainImage = window.getMainImageSafe;
                }
            `;

            localStorage.setItem('imageFixScript', fixScript);
            log('✅ تم حفظ سكريبت إصلاح الصور', 'success');

            // Apply fixes to current page if possible
            try {
                eval(fixScript);
                log('✅ تم تطبيق الإصلاحات على الصفحة الحالية', 'success');
            } catch (error) {
                log('⚠️ لم يتم تطبيق الإصلاحات على الصفحة الحالية: ' + error.message, 'warning');
            }

            log('✅ تم إصلاح جميع مشاكل الصور بنجاح!', 'success');
            log('💡 يرجى إعادة تشغيل التطبيق لرؤية التحسينات', 'info');
        }

        function createPlaceholder() {
            log('🎨 إنشاء صورة بديلة...', 'info');
            
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');

            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, 200, 200);
            gradient.addColorStop(0, '#ffa500');
            gradient.addColorStop(1, '#ff6b00');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 200, 200);

            // Draw image icon
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(100, 80, 30, 0, 2 * Math.PI);
            ctx.fill();

            // Draw mountain shape
            ctx.beginPath();
            ctx.moveTo(60, 90);
            ctx.lineTo(140, 90);
            ctx.lineTo(120, 130);
            ctx.lineTo(80, 130);
            ctx.closePath();
            ctx.fill();

            // Add text
            ctx.fillStyle = '#ffffff';
            ctx.font = '14px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('No Image', 100, 160);

            // Convert to base64 and save
            const dataURL = canvas.toDataURL('image/png');
            localStorage.setItem('CUSTOM_PLACEHOLDER_IMAGE', dataURL);
            
            log('✅ تم إنشاء صورة بديلة مخصصة', 'success');
            log('💾 تم حفظ الصورة في التخزين المحلي', 'info');
        }

        function testImages() {
            log('🧪 اختبار الصور...', 'info');
            
            const testUrls = [
                'image/placeholder.png',
                '../image/placeholder.png',
                'image/placeholder.svg',
                DEFAULT_PLACEHOLDER
            ];

            testUrls.forEach((url, index) => {
                const img = new Image();
                img.onload = () => {
                    log(`✅ نجح تحميل: ${url}`, 'success');
                };
                img.onerror = () => {
                    log(`❌ فشل تحميل: ${url}`, 'error');
                };
                img.src = url;
            });
        }

        function clearCache() {
            log('🗑️ مسح ذاكرة التخزين المؤقت...', 'info');
            
            localStorage.removeItem('DEFAULT_PLACEHOLDER_IMAGE');
            localStorage.removeItem('CUSTOM_PLACEHOLDER_IMAGE');
            localStorage.removeItem('imageFixScript');
            
            log('✅ تم مسح ذاكرة التخزين المؤقت', 'success');
        }

        function goBack() {
            window.location.href = 'index.html';
        }

        // Auto-run basic fixes on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🚀 تشغيل الإصلاحات التلقائية...', 'info');
                fixAllImages();
            }, 1000);
        });
    </script>
</body>
</html>
