// نظام إدارة النسخ الاحتياطية - JavaScript
// Professional Backup Management System

class BackupManager {
    constructor() {
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.logUpdateInterval = null;
        this.backupInProgress = false;
        this.transferInProgress = false;
        
        // إعدادات افتراضية
        this.config = {
            autoBackupInterval: 6,
            maxBackupFiles: 50,
            healthCheckInterval: 300,
            maxFailedChecks: 3,
            databases: {
                main: {
                    name: "الرئيسية",
                    url: "https://ytqxxodyecdeosnqoure.supabase.co",
                    status: "unknown"
                },
                backup1: {
                    name: "الاحتياطية 1",
                    url: "",
                    status: "unknown"
                },
                backup2: {
                    name: "الاحتياطية 2", 
                    url: "",
                    status: "unknown"
                }
            }
        };
        
        this.init();
    }
    
    async init() {
        console.log('🚀 تهيئة نظام إدارة النسخ الاحتياطية');
        
        // تحميل البيانات الأولية
        await this.loadDashboardData();
        await this.loadBackupList();
        await this.loadDatabaseConfig();
        
        // بدء تحديث البيانات التلقائي
        this.startAutoRefresh();
        
        console.log('✅ تم تهيئة النظام بنجاح');
    }
    
    async loadDashboardData() {
        try {
            // محاكاة تحميل بيانات لوحة التحكم
            const dashboardData = await this.fetchDashboardData();
            
            document.getElementById('totalBackups').textContent = dashboardData.totalBackups || '0';
            document.getElementById('lastBackupTime').textContent = dashboardData.lastBackupTime || 'غير متاح';
            document.getElementById('totalSize').textContent = dashboardData.totalSize || '0';
            document.getElementById('systemStatus').textContent = dashboardData.systemStatus || 'غير معروف';
            
            // تحديث حالة قواعد البيانات
            this.updateDatabaseStatus(dashboardData.databaseStatus || {});
            
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات لوحة التحكم:', error);
            this.showAlert('خطأ في تحميل بيانات لوحة التحكم', 'danger');
        }
    }
    
    async fetchDashboardData() {
        // محاكاة استدعاء API
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    totalBackups: Math.floor(Math.random() * 50) + 10,
                    lastBackupTime: new Date().toLocaleString('ar-SA'),
                    totalSize: (Math.random() * 1000).toFixed(2),
                    systemStatus: 'نشط',
                    databaseStatus: {
                        main: { status: 'healthy', responseTime: 0.5 },
                        backup1: { status: 'unknown', responseTime: null },
                        backup2: { status: 'unknown', responseTime: null }
                    }
                });
            }, 1000);
        });
    }
    
    updateDatabaseStatus(statusData) {
        const container = document.getElementById('databaseStatusContainer');
        let html = '';
        
        for (const [dbName, data] of Object.entries(statusData)) {
            const dbConfig = this.config.databases[dbName];
            const statusClass = this.getStatusClass(data.status);
            const statusIcon = this.getStatusIcon(data.status);
            
            html += `
                <div class="database-status ${statusClass}">
                    ${statusIcon} ${dbConfig?.name || dbName}
                    ${data.responseTime ? `(${data.responseTime}s)` : ''}
                </div>
            `;
        }
        
        container.innerHTML = html || '<p class="text-muted">لا توجد بيانات متاحة</p>';
    }
    
    getStatusClass(status) {
        const classes = {
            'healthy': 'db-healthy',
            'slow': 'db-warning',
            'degraded': 'db-warning',
            'failed': 'db-danger',
            'error': 'db-danger',
            'unknown': 'db-unknown'
        };
        return classes[status] || 'db-unknown';
    }
    
    getStatusIcon(status) {
        const icons = {
            'healthy': '<i class="fas fa-check-circle"></i>',
            'slow': '<i class="fas fa-exclamation-triangle"></i>',
            'degraded': '<i class="fas fa-exclamation-triangle"></i>',
            'failed': '<i class="fas fa-times-circle"></i>',
            'error': '<i class="fas fa-times-circle"></i>',
            'unknown': '<i class="fas fa-question-circle"></i>'
        };
        return icons[status] || '<i class="fas fa-question-circle"></i>';
    }
    
    async loadBackupList() {
        try {
            const backups = await this.fetchBackupList();
            const container = document.getElementById('backupList');
            
            if (backups.length === 0) {
                container.innerHTML = '<p class="text-muted">لا توجد نسخ احتياطية</p>';
                return;
            }
            
            let html = '';
            backups.forEach(backup => {
                html += `
                    <div class="backup-item">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <strong>${backup.id}</strong><br>
                                <small class="text-muted">${backup.timestamp}</small>
                            </div>
                            <div class="col-md-3">
                                <span class="badge bg-info">${backup.size}</span>
                                <span class="badge bg-secondary">${backup.type}</span>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-sm btn-outline-primary" onclick="backupManager.restoreBackup('${backup.id}')">
                                    <i class="fas fa-undo"></i> استرداد
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="backupManager.deleteBackup('${backup.id}')">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // تحديث قائمة النسخ الاحتياطية في تبويب النقل
            this.updateBackupSelectOptions(backups);
            
        } catch (error) {
            console.error('❌ خطأ في تحميل قائمة النسخ الاحتياطية:', error);
            document.getElementById('backupList').innerHTML = '<p class="text-danger">خطأ في تحميل القائمة</p>';
        }
    }
    
    async fetchBackupList() {
        // محاكاة استدعاء API
        return new Promise((resolve) => {
            setTimeout(() => {
                const backups = [];
                for (let i = 0; i < 5; i++) {
                    backups.push({
                        id: `backup_main_${Date.now() - i * 86400000}`,
                        timestamp: new Date(Date.now() - i * 86400000).toLocaleString('ar-SA'),
                        size: `${(Math.random() * 100).toFixed(1)} MB`,
                        type: i === 0 ? 'تلقائي' : 'يدوي',
                        source: 'main'
                    });
                }
                resolve(backups);
            }, 800);
        });
    }
    
    updateBackupSelectOptions(backups) {
        const select = document.getElementById('backupToRestore');
        select.innerHTML = '<option value="">اختر نسخة احتياطية (اختياري)</option>';
        
        backups.forEach(backup => {
            const option = document.createElement('option');
            option.value = backup.id;
            option.textContent = `${backup.id} - ${backup.timestamp}`;
            select.appendChild(option);
        });
    }
    
    async createBackup() {
        if (this.backupInProgress) {
            this.showAlert('يوجد نسخ احتياطي قيد التنفيذ بالفعل', 'warning');
            return;
        }
        
        const sourceDb = document.getElementById('sourceDatabase').value;
        const includeImages = document.getElementById('includeImages').checked;
        const compress = document.getElementById('compressBackup').checked;
        
        this.backupInProgress = true;
        this.showBackupProgress(true);
        
        try {
            console.log(`🔄 بدء إنشاء نسخة احتياطية من ${sourceDb}`);
            
            // محاكاة عملية النسخ الاحتياطي
            await this.simulateBackupProcess();
            
            this.showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            await this.loadBackupList();
            await this.loadDashboardData();
            
        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
            this.showAlert('فشل في إنشاء النسخة الاحتياطية', 'danger');
        } finally {
            this.backupInProgress = false;
            this.showBackupProgress(false);
        }
    }
    
    async simulateBackupProcess() {
        const steps = [
            'تهيئة عملية النسخ الاحتياطي...',
            'نسخ جدول المودات...',
            'نسخ بيانات المستخدمين...',
            'نسخ الإعدادات...',
            'نسخ الصور...',
            'ضغط البيانات...',
            'رفع إلى Firebase Storage...',
            'التحقق من سلامة البيانات...',
            'اكتمال النسخ الاحتياطي'
        ];
        
        for (let i = 0; i < steps.length; i++) {
            const progress = ((i + 1) / steps.length) * 100;
            this.updateBackupProgress(progress, steps[i]);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    showBackupProgress(show) {
        const container = document.getElementById('backupProgress');
        container.style.display = show ? 'block' : 'none';
        
        if (!show) {
            this.updateBackupProgress(0, 'بدء العملية...');
        }
    }
    
    updateBackupProgress(percentage, status) {
        const progressBar = document.getElementById('backupProgressBar');
        const statusElement = document.getElementById('backupStatus');
        
        progressBar.style.width = `${percentage}%`;
        statusElement.textContent = status;
    }
    
    async transferData() {
        if (this.transferInProgress) {
            this.showAlert('يوجد عملية نقل قيد التنفيذ بالفعل', 'warning');
            return;
        }

        const sourceDb = document.getElementById('transferSource').value;
        const targetDb = document.getElementById('transferTarget').value;
        const backupId = document.getElementById('backupToRestore').value;
        const activateNewDb = document.getElementById('activateNewDb')?.checked || false;

        if (sourceDb === targetDb) {
            this.showAlert('لا يمكن النقل إلى نفس قاعدة البيانات', 'warning');
            return;
        }

        // تأكيد العملية
        let confirmMessage = `هل أنت متأكد من نقل البيانات من ${sourceDb} إلى ${targetDb}؟\nسيتم استبدال جميع البيانات الموجودة.`;
        if (activateNewDb) {
            confirmMessage += '\n\n⚠️ سيتم تفعيل قاعدة البيانات الجديدة كقاعدة رئيسية للتطبيق!';
        }

        if (!confirm(confirmMessage)) {
            return;
        }

        this.transferInProgress = true;
        this.showTransferProgress(true);

        try {
            console.log(`🔄 بدء نقل البيانات من ${sourceDb} إلى ${targetDb}`);

            // محاكاة عملية النقل المحسنة
            const transferResult = await this.simulateEnhancedTransferProcess(sourceDb, targetDb, activateNewDb);

            // عرض نتائج مفصلة
            this.showTransferResults(transferResult);
            await this.loadDashboardData();

        } catch (error) {
            console.error('❌ خطأ في نقل البيانات:', error);
            this.showAlert('فشل في نقل البيانات', 'danger');
        } finally {
            this.transferInProgress = false;
            this.showTransferProgress(false);
        }
    }
    
    async simulateEnhancedTransferProcess(sourceDb, targetDb, activate) {
        const steps = [
            'تهيئة عملية النقل...',
            'إنشاء نسخة احتياطية من المصدر...',
            'التحقق من الجداول المطلوبة...',
            'إنشاء الجداول المفقودة...',
            'نقل جدول المودات (mods)...',
            'نقل بيانات المستخدمين (user_languages)...',
            'نقل الإعدادات والتخصيصات...',
            'نقل بيانات الإعلانات...',
            'التحقق من سلامة البيانات...',
            activate ? 'تفعيل قاعدة البيانات الجديدة...' : 'إنهاء عملية النقل...'
        ];

        const result = {
            sourceDb: sourceDb,
            targetDb: targetDb,
            transferredTables: {},
            createdTables: [],
            totalTransferred: 0,
            verificationPassed: false,
            activated: false,
            errors: []
        };

        for (let i = 0; i < steps.length; i++) {
            const progress = ((i + 1) / steps.length) * 100;
            this.updateTransferProgress(progress, steps[i]);

            // محاكاة العمليات
            await new Promise(resolve => setTimeout(resolve, 1200));

            // محاكاة نتائج كل خطوة
            if (steps[i].includes('نقل جدول')) {
                const tableName = steps[i].match(/\(([^)]+)\)/)?.[1] || 'unknown';
                const recordCount = Math.floor(Math.random() * 1000) + 100;
                result.transferredTables[tableName] = recordCount;
                result.totalTransferred += recordCount;
            } else if (steps[i].includes('إنشاء الجداول')) {
                result.createdTables = ['custom_sections', 'backup_ads'];
            } else if (steps[i].includes('التحقق من سلامة')) {
                result.verificationPassed = Math.random() > 0.1; // 90% نجاح
            } else if (steps[i].includes('تفعيل قاعدة البيانات') && activate) {
                result.activated = true;
            }
        }

        return result;
    }

    showTransferResults(result) {
        let message = `✅ تم نقل البيانات بنجاح!\n\n`;
        message += `📊 إجمالي السجلات المنقولة: ${result.totalTransferred}\n`;
        message += `📋 الجداول المنقولة: ${Object.keys(result.transferredTables).length}\n`;

        if (result.createdTables.length > 0) {
            message += `🔧 الجداول المُنشأة: ${result.createdTables.join(', ')}\n`;
        }

        message += `✅ التحقق من السلامة: ${result.verificationPassed ? 'نجح' : 'فشل'}\n`;

        if (result.activated) {
            message += `\n🔄 تم تفعيل قاعدة البيانات ${result.targetDb} كقاعدة رئيسية للتطبيق!`;
        }

        this.showAlert(message, result.verificationPassed ? 'success' : 'warning');
    }

    async simulateTransferProcess() {
        // الاحتفاظ بالوظيفة القديمة للتوافق
        return await this.simulateEnhancedTransferProcess('main', 'backup1', false);
    }
    
    showTransferProgress(show) {
        const container = document.getElementById('transferProgress');
        container.style.display = show ? 'block' : 'none';
        
        if (!show) {
            this.updateTransferProgress(0, 'بدء عملية النقل...');
        }
    }
    
    updateTransferProgress(percentage, status) {
        const progressBar = document.getElementById('transferProgressBar');
        const statusElement = document.getElementById('transferStatus');
        
        progressBar.style.width = `${percentage}%`;
        statusElement.textContent = status;
    }
    
    toggleMonitoring() {
        const button = document.getElementById('monitorToggle');
        
        if (this.isMonitoring) {
            this.stopMonitoring();
            button.innerHTML = '<i class="fas fa-play"></i> بدء المراقبة';
            button.classList.remove('btn-danger');
            button.classList.add('btn-success');
        } else {
            this.startMonitoring();
            button.innerHTML = '<i class="fas fa-stop"></i> إيقاف المراقبة';
            button.classList.remove('btn-success');
            button.classList.add('btn-danger');
        }
    }
    
    startMonitoring() {
        this.isMonitoring = true;
        console.log('🔍 بدء مراقبة قواعد البيانات');
        
        // بدء مراقبة دورية
        this.monitoringInterval = setInterval(() => {
            this.checkDatabaseHealth();
        }, this.config.healthCheckInterval * 1000);
        
        // بدء تحديث السجلات
        this.startLogUpdates();
        
        this.updateMonitoringStatus('المراقبة نشطة - فحص دوري كل ' + this.config.healthCheckInterval + ' ثانية');
    }
    
    stopMonitoring() {
        this.isMonitoring = false;
        console.log('⏹️ إيقاف مراقبة قواعد البيانات');
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        this.stopLogUpdates();
        this.updateMonitoringStatus('المراقبة متوقفة');
    }
    
    async checkDatabaseHealth() {
        console.log('🔍 فحص حالة قواعد البيانات');
        
        try {
            const healthData = await this.fetchDatabaseHealth();
            this.updateDatabaseStatus(healthData);
            
            // إضافة سجل للمراقبة
            this.addLogEntry('info', 'تم فحص حالة قواعد البيانات بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في فحص قواعد البيانات:', error);
            this.addLogEntry('error', 'خطأ في فحص قواعد البيانات: ' + error.message);
        }
    }
    
    async fetchDatabaseHealth() {
        // محاكاة فحص حالة قواعد البيانات
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    main: { 
                        status: Math.random() > 0.1 ? 'healthy' : 'slow', 
                        responseTime: (Math.random() * 2).toFixed(3) 
                    },
                    backup1: { 
                        status: 'unknown', 
                        responseTime: null 
                    },
                    backup2: { 
                        status: 'unknown', 
                        responseTime: null 
                    }
                });
            }, 500);
        });
    }
    
    updateMonitoringStatus(status) {
        document.getElementById('monitoringStatus').innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> ${status}
            </div>
        `;
    }
    
    startLogUpdates() {
        this.logUpdateInterval = setInterval(() => {
            this.addLogEntry('info', 'فحص دوري - ' + new Date().toLocaleTimeString('ar-SA'));
        }, 30000); // كل 30 ثانية
    }
    
    stopLogUpdates() {
        if (this.logUpdateInterval) {
            clearInterval(this.logUpdateInterval);
            this.logUpdateInterval = null;
        }
    }
    
    addLogEntry(level, message) {
        const logsContainer = document.getElementById('liveLogs');
        const timestamp = new Date().toLocaleTimeString('ar-SA');
        const levelIcon = level === 'error' ? '❌' : level === 'warning' ? '⚠️' : 'ℹ️';
        
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `[${timestamp}] ${levelIcon} ${message}`;
        
        logsContainer.appendChild(logEntry);
        
        // الحفاظ على آخر 50 سجل فقط
        while (logsContainer.children.length > 50) {
            logsContainer.removeChild(logsContainer.firstChild);
        }
        
        // التمرير إلى الأسفل
        logsContainer.scrollTop = logsContainer.scrollHeight;
    }
    
    clearLogs() {
        document.getElementById('liveLogs').innerHTML = '';
        this.addLogEntry('info', 'تم مسح السجلات');
    }
    
    startAutoRefresh() {
        // تحديث لوحة التحكم كل دقيقة
        setInterval(() => {
            if (!this.backupInProgress && !this.transferInProgress) {
                this.loadDashboardData();
            }
        }, 60000);
    }
    
    async switchDatabase() {
        const targetDb = prompt('أدخل اسم قاعدة البيانات المستهدفة (main, backup1, backup2):');
        if (!targetDb) return;

        const createBackup = confirm('هل تريد إنشاء نسخة احتياطية قبل التبديل؟');

        if (!confirm(`هل أنت متأكد من التبديل إلى قاعدة البيانات ${targetDb}؟\nسيتم تحديث جميع ملفات التطبيق!`)) {
            return;
        }

        try {
            this.showAlert('🔄 جاري تبديل قاعدة البيانات...', 'info');

            // محاكاة عملية التبديل
            await new Promise(resolve => setTimeout(resolve, 3000));

            const result = {
                previousDb: 'main',
                newDb: targetDb,
                backupCreated: createBackup,
                backupId: createBackup ? `backup_${Date.now()}` : null,
                success: true
            };

            let message = `✅ تم تبديل قاعدة البيانات بنجاح!\n\n`;
            message += `من: ${result.previousDb}\n`;
            message += `إلى: ${result.newDb}\n`;

            if (result.backupCreated) {
                message += `💾 تم إنشاء نسخة احتياطية: ${result.backupId}`;
            }

            this.showAlert(message, 'success');
            await this.loadDashboardData();

        } catch (error) {
            console.error('❌ خطأ في تبديل قاعدة البيانات:', error);
            this.showAlert('فشل في تبديل قاعدة البيانات', 'danger');
        }
    }

    async compareDatabases() {
        const db1 = prompt('أدخل اسم قاعدة البيانات الأولى:');
        if (!db1) return;

        const db2 = prompt('أدخل اسم قاعدة البيانات الثانية:');
        if (!db2) return;

        if (db1 === db2) {
            this.showAlert('لا يمكن مقارنة قاعدة البيانات مع نفسها', 'warning');
            return;
        }

        try {
            this.showAlert('🔍 جاري مقارنة قواعد البيانات...', 'info');

            // محاكاة عملية المقارنة
            await new Promise(resolve => setTimeout(resolve, 2000));

            const comparisonResult = {
                database1: db1,
                database2: db2,
                tableComparison: {
                    'mods': { count_db1: 1250, count_db2: 1248, match: false },
                    'user_languages': { count_db1: 500, count_db2: 500, match: true },
                    'featured_addons': { count_db1: 20, count_db2: 18, match: false },
                    'banner_ads': { count_db1: 5, count_db2: 5, match: true }
                },
                totalRecordsDb1: 1775,
                totalRecordsDb2: 1771,
                differencesFound: true
            };

            this.showComparisonResults(comparisonResult);

        } catch (error) {
            console.error('❌ خطأ في مقارنة قواعد البيانات:', error);
            this.showAlert('فشل في مقارنة قواعد البيانات', 'danger');
        }
    }

    showComparisonResults(result) {
        let message = `📊 نتائج مقارنة قواعد البيانات:\n\n`;
        message += `قاعدة البيانات 1 (${result.database1}): ${result.totalRecordsDb1} سجل\n`;
        message += `قاعدة البيانات 2 (${result.database2}): ${result.totalRecordsDb2} سجل\n\n`;

        message += `الجداول:\n`;
        for (const [table, data] of Object.entries(result.tableComparison)) {
            const status = data.match ? '✅' : '❌';
            message += `${status} ${table}: ${data.count_db1} vs ${data.count_db2}\n`;
        }

        if (result.differencesFound) {
            message += `\n⚠️ تم العثور على اختلافات بين قواعد البيانات`;
        } else {
            message += `\n✅ قواعد البيانات متطابقة`;
        }

        this.showAlert(message, result.differencesFound ? 'warning' : 'success');
    }

    showAlert(message, type = 'info') {
        // إنشاء تنبيه مؤقت
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message.replace(/\n/g, '<br>')}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // إزالة التنبيه تلقائياً بعد 8 ثوان للرسائل الطويلة
        const timeout = message.length > 200 ? 10000 : 5000;
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, timeout);
    }
    
    async loadDatabaseConfig() {
        // تحميل إعدادات قواعد البيانات
        const container = document.getElementById('databaseConfig');
        let html = '';
        
        for (const [key, db] of Object.entries(this.config.databases)) {
            html += `
                <div class="mb-3 p-3 border rounded">
                    <h6>${db.name} (${key})</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">رابط قاعدة البيانات:</label>
                            <input type="url" class="form-control" value="${db.url}" 
                                   onchange="backupManager.updateDatabaseConfig('${key}', 'url', this.value)">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">الحالة:</label>
                            <span class="form-control-plaintext ${this.getStatusClass(db.status)}">
                                ${this.getStatusIcon(db.status)} ${db.status}
                            </span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
    
    updateDatabaseConfig(dbKey, field, value) {
        this.config.databases[dbKey][field] = value;
        console.log(`تم تحديث ${field} لقاعدة البيانات ${dbKey}: ${value}`);
    }
}

// الوظائف العامة
async function createQuickBackup() {
    await backupManager.createBackup();
}

async function checkAllDatabases() {
    await backupManager.checkDatabaseHealth();
    backupManager.showAlert('تم فحص جميع قواعد البيانات', 'info');
}

function startMonitoring() {
    if (!backupManager.isMonitoring) {
        backupManager.toggleMonitoring();
    }
}

function downloadLogs() {
    const logs = document.getElementById('liveLogs').textContent;
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup_logs_${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    
    URL.revokeObjectURL(url);
}

function createBackup() {
    backupManager.createBackup();
}

function transferData() {
    backupManager.transferData();
}

function toggleMonitoring() {
    backupManager.toggleMonitoring();
}

function refreshMonitoringData() {
    backupManager.checkDatabaseHealth();
    backupManager.showAlert('تم تحديث بيانات المراقبة', 'success');
}

function clearLogs() {
    backupManager.clearLogs();
}

function saveSettings() {
    // حفظ الإعدادات
    backupManager.config.autoBackupInterval = parseInt(document.getElementById('autoBackupInterval').value);
    backupManager.config.maxBackupFiles = parseInt(document.getElementById('maxBackupFiles').value);
    backupManager.config.healthCheckInterval = parseInt(document.getElementById('healthCheckInterval').value);
    backupManager.config.maxFailedChecks = parseInt(document.getElementById('maxFailedChecks').value);
    
    backupManager.showAlert('تم حفظ الإعدادات بنجاح', 'success');
    console.log('تم حفظ الإعدادات:', backupManager.config);
}

function addNewDatabase() {
    const name = prompt('اسم قاعدة البيانات الجديدة:');
    const url = prompt('رابط قاعدة البيانات:');
    
    if (name && url) {
        const key = name.toLowerCase().replace(/\s+/g, '_');
        backupManager.config.databases[key] = {
            name: name,
            url: url,
            status: 'unknown'
        };
        
        backupManager.loadDatabaseConfig();
        backupManager.showAlert('تم إضافة قاعدة البيانات الجديدة', 'success');
    }
}

// تهيئة النظام عند تحميل الصفحة
let backupManager;

document.addEventListener('DOMContentLoaded', function() {
    backupManager = new BackupManager();
});
