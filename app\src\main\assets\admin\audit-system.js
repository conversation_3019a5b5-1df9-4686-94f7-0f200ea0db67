// Advanced Audit and Logging System
// نظام السجلات والتدقيق المتقدم

// Global variables
let auditLogs = [];
let auditSettings = {
    enableLogging: true,
    logLevel: 'info', // debug, info, warn, error
    retentionDays: 90,
    autoExport: false,
    realTimeAlerts: true
};

let logFilters = {
    dateRange: null,
    logLevel: 'all',
    userId: '',
    action: '',
    category: 'all'
};

// Initialize audit system
document.addEventListener('DOMContentLoaded', function() {
    console.log('Audit System loaded');
    initializeAuditSystem();
});

// Initialize audit system
async function initializeAuditSystem() {
    try {
        // Load audit settings
        loadAuditSettings();
        
        // Load audit logs
        await loadAuditLogs();
        
        // Setup audit UI
        setupAuditUI();
        
        // Start real-time monitoring
        if (auditSettings.realTimeAlerts) {
            startRealTimeMonitoring();
        }
        
        console.log('✅ Audit system initialized');
    } catch (error) {
        console.error('Error initializing audit system:', error);
    }
}

// Load audit settings
function loadAuditSettings() {
    try {
        const savedSettings = localStorage.getItem('auditSettings');
        if (savedSettings) {
            auditSettings = { ...auditSettings, ...JSON.parse(savedSettings) };
        }
    } catch (error) {
        console.error('Error loading audit settings:', error);
    }
}

// Save audit settings
function saveAuditSettings() {
    try {
        localStorage.setItem('auditSettings', JSON.stringify(auditSettings));
    } catch (error) {
        console.error('Error saving audit settings:', error);
    }
}

// Load audit logs
async function loadAuditLogs() {
    try {
        // Load from localStorage (in real app, this would be from database)
        const savedLogs = localStorage.getItem('auditLogs');
        if (savedLogs) {
            auditLogs = JSON.parse(savedLogs);
        }
        
        // Generate sample logs if empty
        if (auditLogs.length === 0) {
            auditLogs = generateSampleAuditLogs();
            saveAuditLogs();
        }
        
        // Apply filters and update UI
        updateAuditLogsDisplay();
        
    } catch (error) {
        console.error('Error loading audit logs:', error);
    }
}

// Save audit logs
function saveAuditLogs() {
    try {
        localStorage.setItem('auditLogs', JSON.stringify(auditLogs));
    } catch (error) {
        console.error('Error saving audit logs:', error);
    }
}

// Generate sample audit logs
function generateSampleAuditLogs() {
    const logs = [];
    const actions = [
        'user_login', 'user_logout', 'mod_upload', 'mod_delete', 'mod_approve',
        'notification_send', 'backup_create', 'settings_change', 'user_ban', 'content_edit'
    ];
    const categories = ['authentication', 'content', 'system', 'user_management', 'notifications'];
    const levels = ['info', 'warn', 'error', 'debug'];
    const users = ['admin_001', 'moderator_002', 'editor_003', 'system'];
    
    for (let i = 0; i < 50; i++) {
        const timestamp = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
        const action = actions[Math.floor(Math.random() * actions.length)];
        const category = categories[Math.floor(Math.random() * categories.length)];
        const level = levels[Math.floor(Math.random() * levels.length)];
        const userId = users[Math.floor(Math.random() * users.length)];
        
        logs.push({
            id: `audit_${Date.now()}_${i}`,
            timestamp: timestamp.toISOString(),
            userId: userId,
            action: action,
            category: category,
            level: level,
            description: generateLogDescription(action, userId),
            ipAddress: generateRandomIP(),
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            details: {
                targetId: Math.random() > 0.5 ? `target_${Math.floor(Math.random() * 1000)}` : null,
                oldValue: Math.random() > 0.7 ? 'old_value' : null,
                newValue: Math.random() > 0.7 ? 'new_value' : null,
                success: Math.random() > 0.1
            }
        });
    }
    
    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

// Generate log description
function generateLogDescription(action, userId) {
    const descriptions = {
        'user_login': `تسجيل دخول المستخدم ${userId}`,
        'user_logout': `تسجيل خروج المستخدم ${userId}`,
        'mod_upload': `رفع مود جديد بواسطة ${userId}`,
        'mod_delete': `حذف مود بواسطة ${userId}`,
        'mod_approve': `الموافقة على مود بواسطة ${userId}`,
        'notification_send': `إرسال إشعار بواسطة ${userId}`,
        'backup_create': `إنشاء نسخة احتياطية بواسطة ${userId}`,
        'settings_change': `تغيير إعدادات النظام بواسطة ${userId}`,
        'user_ban': `حظر مستخدم بواسطة ${userId}`,
        'content_edit': `تعديل محتوى بواسطة ${userId}`
    };
    
    return descriptions[action] || `إجراء ${action} بواسطة ${userId}`;
}

// Generate random IP
function generateRandomIP() {
    return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
}

// Log audit event
function logAuditEvent(action, category, level, description, details = {}) {
    if (!auditSettings.enableLogging) return;
    
    const logEntry = {
        id: `audit_${Date.now()}_${Math.random().toString(36).substring(2)}`,
        timestamp: new Date().toISOString(),
        userId: getCurrentUserId(),
        action: action,
        category: category,
        level: level,
        description: description,
        ipAddress: 'unknown', // Would get real IP in production
        userAgent: navigator.userAgent,
        details: details
    };
    
    // Add to logs array
    auditLogs.unshift(logEntry);
    
    // Keep only recent logs based on retention
    cleanupOldLogs();
    
    // Save logs
    saveAuditLogs();
    
    // Update UI if visible
    updateAuditLogsDisplay();
    
    // Check for real-time alerts
    if (auditSettings.realTimeAlerts) {
        checkForAlerts(logEntry);
    }
    
    console.log('Audit event logged:', logEntry);
}

// Get current user ID
function getCurrentUserId() {
    return localStorage.getItem('currentUserId') || 'admin_001';
}

// Cleanup old logs
function cleanupOldLogs() {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - auditSettings.retentionDays);
    
    auditLogs = auditLogs.filter(log => {
        const logDate = new Date(log.timestamp);
        return logDate > cutoffDate;
    });
}

// Setup audit UI
function setupAuditUI() {
    // Setup filters
    setupAuditFilters();
    
    // Setup settings
    setupAuditSettings();
    
    // Setup event listeners
    setupAuditEventListeners();
    
    // Initial display update
    updateAuditLogsDisplay();
}

// Setup audit filters
function setupAuditFilters() {
    // Date range filter
    const dateFromInput = document.getElementById('audit-date-from');
    const dateToInput = document.getElementById('audit-date-to');
    
    if (dateFromInput && dateToInput) {
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        dateFromInput.value = weekAgo.toISOString().split('T')[0];
        dateToInput.value = today.toISOString().split('T')[0];
        
        logFilters.dateRange = {
            from: weekAgo,
            to: today
        };
    }
    
    // Level filter
    const levelSelect = document.getElementById('audit-level-filter');
    if (levelSelect) {
        levelSelect.value = logFilters.logLevel;
    }
    
    // Category filter
    const categorySelect = document.getElementById('audit-category-filter');
    if (categorySelect) {
        categorySelect.value = logFilters.category;
    }
}

// Setup audit settings
function setupAuditSettings() {
    // Enable logging toggle
    const enableLoggingToggle = document.getElementById('enable-audit-logging');
    if (enableLoggingToggle) {
        enableLoggingToggle.checked = auditSettings.enableLogging;
    }
    
    // Log level select
    const logLevelSelect = document.getElementById('audit-log-level');
    if (logLevelSelect) {
        logLevelSelect.value = auditSettings.logLevel;
    }
    
    // Retention days input
    const retentionInput = document.getElementById('audit-retention-days');
    if (retentionInput) {
        retentionInput.value = auditSettings.retentionDays;
    }
    
    // Real-time alerts toggle
    const realTimeAlertsToggle = document.getElementById('audit-realtime-alerts');
    if (realTimeAlertsToggle) {
        realTimeAlertsToggle.checked = auditSettings.realTimeAlerts;
    }
}

// Setup audit event listeners
function setupAuditEventListeners() {
    // Filter change events
    ['audit-date-from', 'audit-date-to', 'audit-level-filter', 'audit-category-filter', 'audit-user-filter', 'audit-action-filter'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', updateFiltersAndDisplay);
        }
    });
    
    // Settings change events
    const settingsElements = {
        'enable-audit-logging': 'enableLogging',
        'audit-log-level': 'logLevel',
        'audit-retention-days': 'retentionDays',
        'audit-realtime-alerts': 'realTimeAlerts'
    };
    
    Object.entries(settingsElements).forEach(([elementId, settingKey]) => {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener('change', (e) => {
                if (element.type === 'checkbox') {
                    auditSettings[settingKey] = e.target.checked;
                } else if (element.type === 'number') {
                    auditSettings[settingKey] = parseInt(e.target.value);
                } else {
                    auditSettings[settingKey] = e.target.value;
                }
                saveAuditSettings();
            });
        }
    });
}

// Update filters and display
function updateFiltersAndDisplay() {
    // Update date range
    const dateFrom = document.getElementById('audit-date-from')?.value;
    const dateTo = document.getElementById('audit-date-to')?.value;
    
    if (dateFrom && dateTo) {
        logFilters.dateRange = {
            from: new Date(dateFrom),
            to: new Date(dateTo)
        };
    }
    
    // Update other filters
    logFilters.logLevel = document.getElementById('audit-level-filter')?.value || 'all';
    logFilters.category = document.getElementById('audit-category-filter')?.value || 'all';
    logFilters.userId = document.getElementById('audit-user-filter')?.value || '';
    logFilters.action = document.getElementById('audit-action-filter')?.value || '';
    
    // Update display
    updateAuditLogsDisplay();
}

// Update audit logs display
function updateAuditLogsDisplay() {
    const filteredLogs = filterAuditLogs();
    const container = document.getElementById('audit-logs-container');
    
    if (!container) return;
    
    container.innerHTML = filteredLogs.map(log => `
        <div class="audit-log-entry ${log.level}" onclick="showLogDetails('${log.id}')">
            <div class="log-header">
                <span class="log-timestamp">${new Date(log.timestamp).toLocaleString('ar-SA')}</span>
                <span class="log-level ${log.level}">${log.level.toUpperCase()}</span>
                <span class="log-category">${log.category}</span>
            </div>
            <div class="log-content">
                <div class="log-description">${log.description}</div>
                <div class="log-meta">
                    <span class="log-user">المستخدم: ${log.userId}</span>
                    <span class="log-action">الإجراء: ${log.action}</span>
                    <span class="log-ip">IP: ${log.ipAddress}</span>
                </div>
            </div>
            ${log.details.success === false ? '<div class="log-error-indicator">❌ فشل</div>' : ''}
        </div>
    `).join('');
    
    // Update statistics
    updateAuditStatistics(filteredLogs);
}

// Filter audit logs
function filterAuditLogs() {
    return auditLogs.filter(log => {
        // Date range filter
        if (logFilters.dateRange) {
            const logDate = new Date(log.timestamp);
            if (logDate < logFilters.dateRange.from || logDate > logFilters.dateRange.to) {
                return false;
            }
        }
        
        // Level filter
        if (logFilters.logLevel !== 'all' && log.level !== logFilters.logLevel) {
            return false;
        }
        
        // Category filter
        if (logFilters.category !== 'all' && log.category !== logFilters.category) {
            return false;
        }
        
        // User filter
        if (logFilters.userId && !log.userId.toLowerCase().includes(logFilters.userId.toLowerCase())) {
            return false;
        }
        
        // Action filter
        if (logFilters.action && !log.action.toLowerCase().includes(logFilters.action.toLowerCase())) {
            return false;
        }
        
        return true;
    });
}

// Update audit statistics
function updateAuditStatistics(logs) {
    const stats = {
        total: logs.length,
        info: logs.filter(log => log.level === 'info').length,
        warn: logs.filter(log => log.level === 'warn').length,
        error: logs.filter(log => log.level === 'error').length,
        debug: logs.filter(log => log.level === 'debug').length
    };
    
    // Update UI elements
    updateStatElement('audit-total-count', stats.total);
    updateStatElement('audit-info-count', stats.info);
    updateStatElement('audit-warn-count', stats.warn);
    updateStatElement('audit-error-count', stats.error);
    updateStatElement('audit-debug-count', stats.debug);
}

// Update stat element
function updateStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

// Show log details
function showLogDetails(logId) {
    const log = auditLogs.find(l => l.id === logId);
    if (!log) return;
    
    const modal = document.createElement('div');
    modal.className = 'audit-log-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3><i class="fas fa-file-alt"></i> تفاصيل السجل</h3>
                <button class="close-btn" onclick="this.closest('.audit-log-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="log-detail-grid">
                    <div class="detail-item">
                        <label>معرف السجل:</label>
                        <span>${log.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>التاريخ والوقت:</label>
                        <span>${new Date(log.timestamp).toLocaleString('ar-SA')}</span>
                    </div>
                    <div class="detail-item">
                        <label>المستخدم:</label>
                        <span>${log.userId}</span>
                    </div>
                    <div class="detail-item">
                        <label>الإجراء:</label>
                        <span>${log.action}</span>
                    </div>
                    <div class="detail-item">
                        <label>الفئة:</label>
                        <span>${log.category}</span>
                    </div>
                    <div class="detail-item">
                        <label>المستوى:</label>
                        <span class="log-level ${log.level}">${log.level.toUpperCase()}</span>
                    </div>
                    <div class="detail-item">
                        <label>عنوان IP:</label>
                        <span>${log.ipAddress}</span>
                    </div>
                    <div class="detail-item full-width">
                        <label>الوصف:</label>
                        <span>${log.description}</span>
                    </div>
                    <div class="detail-item full-width">
                        <label>User Agent:</label>
                        <span class="small-text">${log.userAgent}</span>
                    </div>
                    ${log.details ? `
                        <div class="detail-item full-width">
                            <label>التفاصيل الإضافية:</label>
                            <pre class="details-json">${JSON.stringify(log.details, null, 2)}</pre>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Start real-time monitoring
function startRealTimeMonitoring() {
    // This would set up real-time monitoring for critical events
    console.log('Real-time audit monitoring started');
}

// Check for alerts
function checkForAlerts(logEntry) {
    // Check for critical events that need immediate attention
    const criticalActions = ['user_ban', 'mod_delete', 'settings_change'];
    const criticalLevels = ['error', 'warn'];
    
    if (criticalActions.includes(logEntry.action) || criticalLevels.includes(logEntry.level)) {
        showAuditAlert(logEntry);
    }
}

// Show audit alert
function showAuditAlert(logEntry) {
    const alert = document.createElement('div');
    alert.className = 'audit-alert critical';
    alert.innerHTML = `
        <div class="alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="alert-content">
            <h4>تنبيه أمني</h4>
            <p>${logEntry.description}</p>
            <small>المستخدم: ${logEntry.userId} | الوقت: ${new Date(logEntry.timestamp).toLocaleTimeString('ar-SA')}</small>
        </div>
        <button class="alert-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    const alertContainer = document.getElementById('audit-alerts');
    if (alertContainer) {
        alertContainer.appendChild(alert);
        
        // Auto-remove after 15 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 15000);
    }
}

// Export audit logs
function exportAuditLogs() {
    const filteredLogs = filterAuditLogs();
    const data = {
        exportDate: new Date().toISOString(),
        totalLogs: filteredLogs.length,
        filters: logFilters,
        logs: filteredLogs
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
}

// Clear audit logs
function clearAuditLogs() {
    if (confirm('هل أنت متأكد من مسح جميع سجلات التدقيق؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        auditLogs = [];
        saveAuditLogs();
        updateAuditLogsDisplay();
        
        // Log this action
        logAuditEvent('audit_clear', 'system', 'warn', 'تم مسح جميع سجلات التدقيق');
    }
}

// Make functions globally available
window.logAuditEvent = logAuditEvent;
window.showLogDetails = showLogDetails;
window.exportAuditLogs = exportAuditLogs;
window.clearAuditLogs = clearAuditLogs;
