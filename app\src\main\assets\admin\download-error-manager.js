// إدارة أخطاء التحميل - Download Error Manager
// نظام إدارة شامل لأخطاء تحميل المودات

class DownloadErrorManager {
    constructor() {
        this.supabaseClient = null;
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalItems = 0;
        this.currentFilters = {};
        this.selectedErrors = new Set();
        
        this.init();
    }
    
    async init() {
        try {
            // تهيئة Supabase
            if (typeof supabaseManager !== 'undefined') {
                this.supabaseClient = supabaseManager.getClient();
            } else {
                // Fallback configuration
                const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
                this.supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
            }
            
            console.log('✅ تم تهيئة مدير أخطاء التحميل');
            
            // تحميل البيانات الأولية
            await this.loadStatistics();
            await this.loadErrors();
            await this.loadModsList();
            
            // تحديث البيانات كل دقيقة
            setInterval(() => {
                this.loadStatistics();
            }, 60000);
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير أخطاء التحميل:', error);
            this.showAlert('خطأ في تهيئة النظام', 'danger');
        }
    }
    
    async loadStatistics() {
        try {
            // إحصائيات الأخطاء
            const { data: errors, error: errorsError } = await this.supabaseClient
                .from('download_errors')
                .select('*');
            
            if (errorsError) throw errorsError;
            
            const totalErrors = errors?.length || 0;
            const unresolvedErrors = errors?.filter(e => !e.is_resolved).length || 0;
            
            // إحصائيات التحميل
            const { data: stats, error: statsError } = await this.supabaseClient
                .from('download_statistics')
                .select('*')
                .gte('date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);
            
            if (statsError) throw statsError;
            
            let totalAttempts = 0;
            let successfulDownloads = 0;
            let backupDownloads = 0;
            
            stats?.forEach(stat => {
                totalAttempts += stat.total_attempts || 0;
                successfulDownloads += stat.successful_downloads || 0;
                backupDownloads += stat.backup_downloads || 0;
            });
            
            const successRate = totalAttempts > 0 ? ((successfulDownloads / totalAttempts) * 100).toFixed(1) : 0;
            const backupUsage = totalAttempts > 0 ? ((backupDownloads / totalAttempts) * 100).toFixed(1) : 0;
            
            // تحديث الواجهة
            document.getElementById('totalErrors').textContent = totalErrors;
            document.getElementById('unresolvedErrors').textContent = unresolvedErrors;
            document.getElementById('successRate').textContent = successRate;
            document.getElementById('backupUsage').textContent = backupUsage;
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الإحصائيات:', error);
        }
    }
    
    async loadErrors(page = 1) {
        try {
            this.currentPage = page;
            const offset = (page - 1) * this.itemsPerPage;
            
            // بناء الاستعلام مع الفلاتر
            let query = this.supabaseClient
                .from('download_errors')
                .select('*, mods(name)', { count: 'exact' });
            
            // تطبيق الفلاتر
            if (this.currentFilters.errorType) {
                query = query.eq('error_type', this.currentFilters.errorType);
            }
            
            if (this.currentFilters.status !== undefined && this.currentFilters.status !== '') {
                query = query.eq('is_resolved', this.currentFilters.status === 'true');
            }
            
            if (this.currentFilters.timeFilter && this.currentFilters.timeFilter !== 'all') {
                const timeMap = {
                    '24h': 1,
                    '7d': 7,
                    '30d': 30
                };
                const days = timeMap[this.currentFilters.timeFilter];
                const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
                query = query.gte('timestamp', cutoffDate);
            }
            
            // ترتيب وتحديد النطاق
            query = query
                .order('timestamp', { ascending: false })
                .range(offset, offset + this.itemsPerPage - 1);
            
            const { data: errors, error, count } = await query;
            
            if (error) throw error;
            
            this.totalItems = count || 0;
            this.renderErrors(errors || []);
            this.renderPagination();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الأخطاء:', error);
            this.showAlert('خطأ في تحميل قائمة الأخطاء', 'danger');
        }
    }
    
    renderErrors(errors) {
        const container = document.getElementById('errorsList');
        
        if (errors.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-check-circle text-success" style="font-size: 3em;"></i>
                    <h4 class="mt-3">لا توجد أخطاء</h4>
                    <p class="text-muted">جميع روابط التحميل تعمل بشكل طبيعي</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        
        errors.forEach(error => {
            const severityClass = this.getSeverityClass(error.error_type);
            const statusBadge = this.getStatusBadge(error.is_resolved);
            const timeAgo = this.getTimeAgo(error.timestamp);
            
            html += `
                <div class="error-card ${severityClass}" data-error-id="${error.id}">
                    <div class="row align-items-center">
                        <div class="col-md-1">
                            <input type="checkbox" class="form-check-input error-checkbox" 
                                   value="${error.id}" onchange="errorManager.toggleErrorSelection('${error.id}')">
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-1">
                                <i class="fas fa-cube text-primary"></i>
                                ${error.mod_name || 'مود غير معروف'}
                            </h6>
                            <p class="mb-1 text-muted small">
                                <i class="fas fa-clock"></i> ${timeAgo}
                            </p>
                            <span class="badge bg-secondary">${this.getErrorTypeLabel(error.error_type)}</span>
                            ${statusBadge}
                        </div>
                        <div class="col-md-3">
                            <div class="error-details">
                                <strong>الرابط الأصلي:</strong><br>
                                <small class="text-break">${error.original_url}</small>
                                ${error.backup_url ? `<br><strong>الرابط الاحتياطي:</strong><br><small class="text-break">${error.backup_url}</small>` : ''}
                                ${error.error_message ? `<br><strong>رسالة الخطأ:</strong><br><small>${error.error_message}</small>` : ''}
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="btn-group-vertical w-100">
                                <button class="action-btn btn-success btn-sm" onclick="errorManager.showFixModal('${error.id}')" 
                                        ${error.is_resolved ? 'disabled' : ''}>
                                    <i class="fas fa-wrench"></i> إصلاح
                                </button>
                                <button class="action-btn btn-warning btn-sm" onclick="errorManager.viewErrorDetails('${error.id}')">
                                    <i class="fas fa-eye"></i> تفاصيل
                                </button>
                                <button class="action-btn btn-danger btn-sm" onclick="errorManager.deleteError('${error.id}')">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    getSeverityClass(errorType) {
        const severityMap = {
            'complete_failure': 'error-critical',
            'url_not_found': 'error-high',
            'file_corrupted': 'error-high',
            'timeout': 'error-medium',
            'network_error': 'error-medium',
            'original_failed_backup_success': 'error-low'
        };
        return severityMap[errorType] || 'error-medium';
    }
    
    getStatusBadge(isResolved) {
        if (isResolved) {
            return '<span class="status-badge status-resolved">محلول</span>';
        } else {
            return '<span class="status-badge status-unresolved">غير محلول</span>';
        }
    }
    
    getErrorTypeLabel(errorType) {
        const labels = {
            'complete_failure': 'فشل كامل',
            'original_failed_backup_success': 'نجح الاحتياطي',
            'timeout': 'انتهاء المهلة',
            'network_error': 'خطأ شبكة',
            'file_corrupted': 'ملف تالف',
            'url_not_found': 'رابط غير موجود',
            'permission_denied': 'رفض الوصول'
        };
        return labels[errorType] || errorType;
    }
    
    getTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInSeconds = Math.floor((now - time) / 1000);
        
        if (diffInSeconds < 60) return 'منذ لحظات';
        if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
        if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
        return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
    }
    
    renderPagination() {
        const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        const container = document.getElementById('pagination');
        
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // Previous button
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="errorManager.loadErrors(${this.currentPage - 1})">السابق</a>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                html += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="errorManager.loadErrors(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // Next button
        html += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="errorManager.loadErrors(${this.currentPage + 1})">التالي</a>
            </li>
        `;
        
        container.innerHTML = html;
    }
    
    async showFixModal(errorId) {
        try {
            // تحميل بيانات الخطأ
            const { data: error, error: fetchError } = await this.supabaseClient
                .from('download_errors')
                .select('*')
                .eq('id', errorId)
                .single();
            
            if (fetchError) throw fetchError;
            
            // ملء النموذج
            document.getElementById('fixModName').value = error.mod_name;
            document.getElementById('fixCurrentUrl').value = error.original_url;
            document.getElementById('fixNewUrl').value = '';
            document.getElementById('fixBackupUrl').value = error.backup_url || '';
            document.getElementById('fixNotes').value = '';
            
            // حفظ معرف الخطأ
            document.getElementById('fixUrlModal').setAttribute('data-error-id', errorId);
            
            // عرض النموذج
            const modal = new bootstrap.Modal(document.getElementById('fixUrlModal'));
            modal.show();
            
        } catch (error) {
            console.error('❌ خطأ في عرض نموذج الإصلاح:', error);
            this.showAlert('خطأ في تحميل بيانات الخطأ', 'danger');
        }
    }
    
    async applyFix() {
        try {
            const modal = document.getElementById('fixUrlModal');
            const errorId = modal.getAttribute('data-error-id');
            const newUrl = document.getElementById('fixNewUrl').value.trim();
            const backupUrl = document.getElementById('fixBackupUrl').value.trim();
            const notes = document.getElementById('fixNotes').value.trim();
            
            if (!newUrl) {
                this.showAlert('يجب إدخال الرابط الجديد', 'warning');
                return;
            }
            
            // اختبار الرابط إذا كان مطلوباً
            if (document.getElementById('testUrlBeforeFix').checked) {
                const isValid = await this.testUrl(newUrl);
                if (!isValid) {
                    this.showAlert('الرابط الجديد لا يعمل', 'danger');
                    return;
                }
            }
            
            // تحديث رابط المود في جدول المودات
            const { data: errorData } = await this.supabaseClient
                .from('download_errors')
                .select('mod_id, original_url')
                .eq('id', errorId)
                .single();
            
            if (errorData && errorData.mod_id) {
                await this.supabaseClient
                    .from('mods')
                    .update({ download_url: newUrl })
                    .eq('id', errorData.mod_id);
            }
            
            // إضافة رابط احتياطي إذا تم توفيره
            if (backupUrl) {
                await this.supabaseClient
                    .from('mod_backup_urls')
                    .insert({
                        mod_id: errorData.mod_id,
                        backup_url: backupUrl,
                        backup_type: 'external',
                        is_active: true,
                        priority: 1
                    });
            }
            
            // تسجيل الإصلاح في السجل
            await this.supabaseClient
                .from('url_fix_history')
                .insert({
                    mod_id: errorData.mod_id,
                    error_id: errorId,
                    old_url: errorData.original_url,
                    new_url: newUrl,
                    backup_url: backupUrl,
                    fix_type: 'url_update',
                    fix_description: notes,
                    fixed_by: 'admin',
                    fix_method: 'manual',
                    is_verified: document.getElementById('testUrlBeforeFix').checked
                });
            
            // تحديث حالة الخطأ
            await this.supabaseClient
                .from('download_errors')
                .update({
                    is_resolved: true,
                    resolved_at: new Date().toISOString(),
                    resolved_by: 'admin',
                    resolution_notes: notes
                })
                .eq('id', errorId);
            
            // إغلاق النموذج وتحديث القائمة
            bootstrap.Modal.getInstance(modal).hide();
            this.showAlert('تم إصلاح الرابط بنجاح', 'success');
            await this.loadErrors(this.currentPage);
            await this.loadStatistics();
            
        } catch (error) {
            console.error('❌ خطأ في تطبيق الإصلاح:', error);
            this.showAlert('خطأ في تطبيق الإصلاح', 'danger');
        }
    }
    
    async testUrl(url = null) {
        const testUrl = url || document.getElementById('fixNewUrl').value.trim();
        
        if (!testUrl) {
            this.showAlert('يجب إدخال رابط للاختبار', 'warning');
            return false;
        }
        
        try {
            this.showAlert('جاري اختبار الرابط...', 'info');
            
            // محاولة الوصول للرابط
            const response = await fetch(testUrl, { method: 'HEAD', mode: 'no-cors' });
            
            // في حالة no-cors، لا يمكننا قراءة الاستجابة، لذا نعتبر الاختبار ناجح
            this.showAlert('الرابط يعمل بشكل طبيعي', 'success');
            return true;
            
        } catch (error) {
            console.error('❌ خطأ في اختبار الرابط:', error);
            this.showAlert('لا يمكن الوصول للرابط', 'danger');
            return false;
        }
    }
    
    toggleErrorSelection(errorId) {
        if (this.selectedErrors.has(errorId)) {
            this.selectedErrors.delete(errorId);
        } else {
            this.selectedErrors.add(errorId);
        }
        
        console.log('Selected errors:', Array.from(this.selectedErrors));
    }
    
    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
    
    async loadModsList() {
        try {
            const { data: mods, error } = await this.supabaseClient
                .from('mods')
                .select('id, name')
                .order('name');
            
            if (error) throw error;
            
            const select = document.getElementById('backupModSelect');
            select.innerHTML = '<option value="">اختر مود...</option>';
            
            mods?.forEach(mod => {
                const option = document.createElement('option');
                option.value = mod.id;
                option.textContent = mod.name;
                select.appendChild(option);
            });
            
        } catch (error) {
            console.error('❌ خطأ في تحميل قائمة المودات:', error);
        }
    }
}

// الوظائف العامة
function applyFilters() {
    const errorType = document.getElementById('errorTypeFilter').value;
    const status = document.getElementById('statusFilter').value;
    const timeFilter = document.getElementById('timeFilter').value;
    
    errorManager.currentFilters = {
        errorType,
        status,
        timeFilter
    };
    
    errorManager.loadErrors(1);
}

function refreshData() {
    errorManager.loadStatistics();
    errorManager.loadErrors(errorManager.currentPage);
}

function showBulkFixModal() {
    if (errorManager.selectedErrors.size === 0) {
        errorManager.showAlert('يجب اختيار أخطاء للإصلاح الجماعي', 'warning');
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('bulkFixModal'));
    modal.show();
}

function showAddBackupModal() {
    const modal = new bootstrap.Modal(document.getElementById('addBackupModal'));
    modal.show();
}

function testUrl() {
    errorManager.testUrl();
}

function applyFix() {
    errorManager.applyFix();
}

function addBackupUrl() {
    // تنفيذ إضافة رابط احتياطي
    errorManager.showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

function applyBulkFix() {
    // تنفيذ الإصلاح الجماعي
    errorManager.showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

function exportErrorReport() {
    // تصدير تقرير الأخطاء
    errorManager.showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

function cleanupOldErrors() {
    if (confirm('هل أنت متأكد من حذف الأخطاء القديمة المحلولة؟')) {
        errorManager.showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
    }
}

// تهيئة النظام عند تحميل الصفحة
let errorManager;

document.addEventListener('DOMContentLoaded', function() {
    errorManager = new DownloadErrorManager();
});
