# نظام النسخ الاحتياطي الذكي لقاعدة البيانات
# Smart Database Backup System

## نظرة عامة | Overview

نظام النسخ الاحتياطي الذكي هو حل شامل لحماية بيانات تطبيق Minecraft Mods مع إمكانية التبديل السلس بين قواعد البيانات الأساسية والاحتياطية. يوفر النظام حماية كاملة ضد فقدان البيانات مع آليات استعادة متقدمة.

The Smart Database Backup System is a comprehensive solution for protecting Minecraft Mods app data with seamless switching between primary and backup databases. The system provides complete protection against data loss with advanced recovery mechanisms.

## المميزات الرئيسية | Key Features

### 🔄 النسخ الاحتياطي الذكي | Smart Backup
- **نسخ كامل**: نسخ جميع البيانات من قاعدة البيانات الأساسية
- **نسخ تزايدي**: نسخ التغييرات الجديدة فقط لتوفير الوقت والموارد
- **نسخ تلقائي**: جدولة النسخ الاحتياطي في أوقات محددة
- **ضغط البيانات**: تقليل حجم النسخ الاحتياطية
- **تشفير البيانات**: حماية البيانات الحساسة

### 🔀 التبديل الذكي | Smart Switching
- **كشف الأعطال**: رصد تلقائي لحالة قواعد البيانات
- **تبديل تلقائي**: التحول للقاعدة الاحتياطية عند تعطل الأساسية
- **تبديل يدوي**: إمكانية التبديل اليدوي بين القواعد
- **استعادة سلسة**: العودة للقاعدة الأساسية عند إصلاحها

### 📊 المراقبة والإدارة | Monitoring & Management
- **لوحة تحكم شاملة**: واجهة إدارية متقدمة
- **إحصائيات مفصلة**: تتبع حالة النسخ والاستعادة
- **تنبيهات ذكية**: إشعارات عند حدوث مشاكل
- **سجل شامل**: تتبع جميع العمليات والأحداث

## المكونات الأساسية | Core Components

### 1. نظام النسخ الاحتياطي | Backup System
```
📁 database-backup-system.js
├── DatabaseBackupSystem (الفئة الرئيسية)
├── createFullBackup() (نسخ كامل)
├── createIncrementalBackup() (نسخ تزايدي)
├── switchToBackupDatabase() (التبديل للاحتياطية)
├── switchToPrimaryDatabase() (التبديل للأساسية)
└── restoreFromBackup() (الاستعادة)
```

### 2. واجهة الإدارة | Management Interface
```
📁 database-backup-manager.html
📁 database-backup-manager.js
├── DatabaseBackupManagerUI (واجهة المستخدم)
├── updateDatabaseStatus() (تحديث الحالة)
├── updateBackupHistory() (سجل النسخ)
└── updateTableSyncStatus() (حالة المزامنة)
```

### 3. إعدادات Firebase | Firebase Configuration
```
📁 firebase-backup-config.js
├── FirebaseBackupConfig (إعدادات Firebase)
├── initialize() (التهيئة)
├── testConnection() (اختبار الاتصال)
└── scheduleAutoBackup() (الجدولة التلقائية)
```

## التثبيت والإعداد | Installation & Setup

### 1. إعداد قاعدة البيانات الاحتياطية | Backup Database Setup

#### إعداد Firebase
```javascript
// في firebase-backup-config.js
const firebaseBackupConfig = {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    // ... باقي الإعدادات
};
```

#### إنشاء مشروع Firebase جديد
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. أنشئ مشروع جديد باسم "minecraft-mods-backup"
3. فعّل Firestore Database
4. فعّل Firebase Storage
5. انسخ إعدادات المشروع إلى ملف التكوين

### 2. إعداد جداول قاعدة البيانات | Database Tables Setup

```sql
-- تشغيل ملف SQL لإنشاء الجداول المطلوبة
\i download-fallback-system-tables.sql
```

### 3. إضافة الملفات للتطبيق | Add Files to App

```html
<!-- في index.html -->
<script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
<script src="admin/firebase-backup-config.js"></script>
<script src="admin/database-backup-system.js"></script>
```

## الاستخدام | Usage

### 1. إنشاء نسخة احتياطية | Create Backup

#### نسخة احتياطية كاملة
```javascript
// نسخ جميع البيانات
const result = await window.databaseBackupSystem.createFullBackup();

if (result.success) {
    console.log(`تم نسخ ${result.totalRecords} سجل بنجاح`);
} else {
    console.error('فشل في النسخ:', result.errors);
}
```

#### نسخة احتياطية تزايدية
```javascript
// نسخ التغييرات الجديدة فقط
const result = await window.databaseBackupSystem.createIncrementalBackup();
```

### 2. التبديل بين قواعد البيانات | Switch Databases

#### التبديل للقاعدة الاحتياطية
```javascript
try {
    await window.databaseBackupSystem.switchToBackupDatabase();
    console.log('تم التبديل للقاعدة الاحتياطية');
} catch (error) {
    console.error('فشل التبديل:', error);
}
```

#### التبديل للقاعدة الأساسية
```javascript
try {
    await window.databaseBackupSystem.switchToPrimaryDatabase();
    console.log('تم التبديل للقاعدة الأساسية');
} catch (error) {
    console.error('فشل التبديل:', error);
}
```

### 3. فحص حالة قواعد البيانات | Check Database Health

```javascript
const health = await window.databaseBackupSystem.checkDatabaseHealth();

console.log('حالة القاعدة الأساسية:', health.primary);
console.log('حالة القاعدة الاحتياطية:', health.backup);

if (health.errors.length > 0) {
    console.log('أخطاء:', health.errors);
}
```

### 4. استعادة البيانات | Restore Data

```javascript
// استعادة من نسخة احتياطية محددة
const backupId = 'backup_2024-01-15_abc123';
const result = await window.databaseBackupSystem.restoreFromBackup(backupId, {
    clearTable: true, // مسح الجدول قبل الاستعادة
    batchSize: 500    // حجم الدفعة
});
```

## الجداول المدعومة | Supported Tables

النظام يدعم نسخ الجداول التالية:

- `mods` - بيانات المودات
- `user_languages` - لغات المستخدمين  
- `user_likes` - إعجابات المستخدمين
- `featured_mods` - المودات المميزة
- `free_addons` - الإضافات المجانية
- `suggested_mods` - المودات المقترحة
- `banner_ads` - إعلانات البانر
- `free_subscription_campaigns` - حملات الاشتراك المجاني
- `subscription_tasks` - مهام الاشتراك
- `custom_mod_dialogs` - نوافذ المودات المخصصة
- `download_errors` - أخطاء التحميل
- `mod_backup_urls` - روابط النسخ الاحتياطية
- `download_statistics` - إحصائيات التحميل
- `url_fix_history` - تاريخ إصلاح الروابط
- `system_events_log` - سجل أحداث النظام

## واجهة الإدارة | Admin Interface

### الوصول للوحة الإدارة | Access Admin Panel
```
الرابط: admin/database-backup-manager.html
```

### المميزات المتاحة | Available Features

#### 1. مراقبة الحالة | Status Monitoring
- حالة قاعدة البيانات الأساسية
- حالة قاعدة البيانات الاحتياطية  
- قاعدة البيانات النشطة حالياً
- وقت آخر نسخ احتياطي

#### 2. عمليات النسخ | Backup Operations
- إنشاء نسخة احتياطية كاملة
- إنشاء نسخة احتياطية تزايدية
- جدولة النسخ التلقائي
- مراقبة تقدم العمليات

#### 3. إدارة النسخ | Backup Management
- عرض سجل النسخ الاحتياطية
- استعادة من نسخة محددة
- حذف النسخ القديمة
- عرض تفاصيل كل نسخة

#### 4. أدوات الصيانة | Maintenance Tools
- فحص اتصالات قواعد البيانات
- مزامنة قواعد البيانات
- تنظيف النسخ القديمة
- عرض حالة مزامنة الجداول

## الأمان والحماية | Security & Protection

### 1. تشفير البيانات | Data Encryption
```javascript
// تفعيل التشفير
const settings = {
    encryptSensitiveData: true,
    encryptionKey: "your-secure-key"
};

window.firebaseBackupConfig.updateBackupSettings(settings);
```

### 2. صلاحيات الوصول | Access Permissions
```javascript
// قائمة المستخدمين المصرح لهم
const authorizedUsers = [
    "<EMAIL>",
    "<EMAIL>"
];
```

### 3. قواعد الأمان | Security Rules
```javascript
// Firebase Security Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null 
        && request.auth.token.email in [
          "<EMAIL>",
          "<EMAIL>"
        ];
    }
  }
}
```

## المراقبة والتنبيهات | Monitoring & Alerts

### 1. مراقبة تلقائية | Automatic Monitoring
```javascript
// فحص دوري كل 5 دقائق
setInterval(async () => {
    const health = await window.databaseBackupSystem.checkDatabaseHealth();
    
    if (!health.primary && health.backup) {
        // تبديل تلقائي للقاعدة الاحتياطية
        await window.databaseBackupSystem.switchToBackupDatabase();
        
        // إرسال تنبيه
        sendAlert('تم التبديل التلقائي للقاعدة الاحتياطية');
    }
}, 5 * 60 * 1000);
```

### 2. تنبيهات البريد الإلكتروني | Email Alerts
```javascript
// إعداد تنبيهات البريد الإلكتروني
const alertSettings = {
    email: "<EMAIL>",
    events: [
        "database_failure",
        "backup_failure", 
        "automatic_switch",
        "restore_completed"
    ]
};
```

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

#### 1. فشل الاتصال بـ Firebase
```javascript
// التحقق من الإعدادات
const config = window.firebaseBackupConfig.config;
console.log('إعدادات Firebase:', config);

// اختبار الاتصال
const testResult = await window.firebaseBackupConfig.testConnection();
console.log('نتيجة اختبار الاتصال:', testResult);
```

#### 2. فشل النسخ الاحتياطي
```javascript
// فحص الأخطاء
const errors = window.databaseBackupSystem.syncStatus.errors;
console.log('أخطاء النسخ:', errors);

// إعادة المحاولة
await window.databaseBackupSystem.createFullBackup();
```

#### 3. مشاكل التبديل بين القواعد
```javascript
// فحص حالة القواعد
const info = window.databaseBackupSystem.getCurrentDatabaseInfo();
console.log('معلومات القاعدة الحالية:', info);

// إجبار التبديل
await window.databaseBackupSystem.switchToBackupDatabase();
```

## الصيانة الدورية | Regular Maintenance

### 1. تنظيف النسخ القديمة | Cleanup Old Backups
```javascript
// حذف النسخ الأقدم من 30 يوم
await window.databaseBackupSystem.cleanupOldBackups(30, true);
```

### 2. فحص سلامة البيانات | Data Integrity Check
```javascript
// مقارنة البيانات بين القواعد
await window.databaseBackupSystem.syncDatabases();
```

### 3. تحديث الإعدادات | Update Settings
```javascript
// تحديث إعدادات النسخ الاحتياطي
const newSettings = {
    batchSize: 2000,
    retentionDays: 60,
    autoBackup: {
        enabled: true,
        interval: 12, // كل 12 ساعة
        time: "03:00"
    }
};

window.firebaseBackupConfig.updateBackupSettings(newSettings);
```

## الأداء والتحسين | Performance & Optimization

### 1. تحسين حجم النسخ | Optimize Backup Size
- استخدام النسخ التزايدي للتحديثات اليومية
- ضغط البيانات لتوفير المساحة
- حذف النسخ القديمة بانتظام

### 2. تحسين سرعة العمليات | Optimize Operation Speed
- زيادة حجم الدفعة للجداول الكبيرة
- تقليل التأخير بين الدفعات
- استخدام الفهارس المناسبة

### 3. مراقبة الموارد | Resource Monitoring
- مراقبة استخدام الذاكرة
- مراقبة استخدام الشبكة
- مراقبة استخدام التخزين

## الدعم والمساعدة | Support & Help

### معلومات الاتصال | Contact Information
- **البريد الإلكتروني**: <EMAIL>
- **الدعم الفني**: متاح 24/7
- **التوثيق**: راجع ملفات README الأخرى

### الموارد المفيدة | Useful Resources
- [Firebase Documentation](https://firebase.google.com/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [JavaScript Backup Best Practices](https://developer.mozilla.org/en-US/docs/Web/API/IndexedDB_API)

---

**ملاحظة مهمة**: تأكد من اختبار النظام في بيئة التطوير قبل النشر في الإنتاج. احتفظ بنسخ احتياطية متعددة في مواقع مختلفة لضمان أقصى حماية للبيانات.
