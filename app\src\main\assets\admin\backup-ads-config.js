// Backup Ads Configuration
// تكوين الإعلانات الاحتياطية

// Configuration for backup ads system
const BACKUP_ADS_CONFIG = {
    // Enable/disable backup ads system
    enabled: true,
    
    // Default settings
    defaults: {
        priority: 5,
        duration: 5,
        adType: 'image',
        clickAction: 'none'
    },
    
    // File upload settings
    upload: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        allowedVideoTypes: ['video/mp4', 'video/webm', 'video/ogg'],
        recommendedImageDimensions: {
            banner: { width: 1200, height: 400 },
            popup: { width: 600, height: 800 },
            square: { width: 500, height: 500 }
        },
        recommendedVideoDimensions: {
            landscape: { width: 1280, height: 720 },
            portrait: { width: 720, height: 1280 },
            square: { width: 720, height: 720 }
        }
    },
    
    // Display settings
    display: {
        minDuration: 3,
        maxDuration: 30,
        defaultShowDelay: 3000, // 3 seconds after <PERSON><PERSON><PERSON> fails
        fadeInDuration: 300,
        fadeOutDuration: 200
    },
    
    // Priority levels
    priorityLevels: {
        1: { name: 'منخفضة جداً', color: '#6c757d' },
        2: { name: 'منخفضة', color: '#17a2b8' },
        3: { name: 'عادية', color: '#28a745' },
        4: { name: 'متوسطة', color: '#ffc107' },
        5: { name: 'عالية', color: '#fd7e14' },
        6: { name: 'عالية جداً', color: '#dc3545' },
        7: { name: 'حرجة', color: '#6f42c1' },
        8: { name: 'طارئة', color: '#e83e8c' },
        9: { name: 'قصوى', color: '#20c997' },
        10: { name: 'أولوية مطلقة', color: '#007bff' }
    },
    
    // Click actions
    clickActions: {
        none: { name: 'لا يوجد إجراء', icon: 'fas fa-ban' },
        url: { name: 'فتح رابط', icon: 'fas fa-external-link-alt' },
        close: { name: 'إغلاق الإعلان', icon: 'fas fa-times' }
    },
    
    // Categories for targeting
    targetCategories: [
        'Addons',
        'Shaders', 
        'Texture Pack',
        'Maps',
        'Seeds',
        'Skins',
        'Mods',
        'Tools',
        'Furniture',
        'Vehicles',
        'Animals',
        'Weapons',
        'Food',
        'Decorations',
        'Buildings',
        'Redstone',
        'Adventure',
        'Survival',
        'Creative',
        'Multiplayer'
    ],
    
    // User types for targeting
    targetUserTypes: [
        'new', // New users
        'returning', // Returning users
        'premium', // Premium subscribers
        'free', // Free users
        'active', // Active users
        'inactive', // Inactive users
        'mobile', // Mobile users
        'desktop' // Desktop users
    ],
    
    // Analytics settings
    analytics: {
        trackViews: true,
        trackClicks: true,
        trackCloses: true,
        trackErrors: true,
        batchSize: 10, // Send analytics in batches
        flushInterval: 30000 // Flush analytics every 30 seconds
    },
    
    // Error handling
    errorHandling: {
        maxRetries: 3,
        retryDelay: 1000,
        fallbackToDefault: true,
        logErrors: true
    },
    
    // Performance settings
    performance: {
        preloadImages: true,
        lazyLoadVideos: true,
        cacheAds: true,
        maxCacheSize: 50,
        cacheExpiry: 24 * 60 * 60 * 1000 // 24 hours
    },
    
    // UI settings
    ui: {
        showTimer: true,
        showCloseButton: true,
        showSkipButton: false,
        skipButtonDelay: 5000, // Show skip button after 5 seconds
        animations: true,
        darkMode: true
    },
    
    // Validation rules
    validation: {
        title: {
            minLength: 3,
            maxLength: 255,
            required: true
        },
        description: {
            minLength: 0,
            maxLength: 1000,
            required: false
        },
        mediaUrl: {
            required: true,
            validateUrl: true
        },
        clickUrl: {
            required: false,
            validateUrl: true
        }
    }
};

// Validation functions
const BackupAdsValidator = {
    // Validate ad data
    validateAd(adData) {
        const errors = [];
        
        // Title validation
        if (!adData.title || adData.title.trim().length < BACKUP_ADS_CONFIG.validation.title.minLength) {
            errors.push(`العنوان يجب أن يكون على الأقل ${BACKUP_ADS_CONFIG.validation.title.minLength} أحرف`);
        }
        
        if (adData.title && adData.title.length > BACKUP_ADS_CONFIG.validation.title.maxLength) {
            errors.push(`العنوان يجب أن يكون أقل من ${BACKUP_ADS_CONFIG.validation.title.maxLength} حرف`);
        }
        
        // Description validation
        if (adData.description && adData.description.length > BACKUP_ADS_CONFIG.validation.description.maxLength) {
            errors.push(`الوصف يجب أن يكون أقل من ${BACKUP_ADS_CONFIG.validation.description.maxLength} حرف`);
        }
        
        // Ad type validation
        if (!adData.ad_type || !['image', 'video'].includes(adData.ad_type)) {
            errors.push('نوع الإعلان يجب أن يكون صورة أو فيديو');
        }
        
        // Priority validation
        if (adData.priority < 1 || adData.priority > 10) {
            errors.push('الأولوية يجب أن تكون بين 1 و 10');
        }
        
        // Duration validation
        if (adData.duration < BACKUP_ADS_CONFIG.display.minDuration || 
            adData.duration > BACKUP_ADS_CONFIG.display.maxDuration) {
            errors.push(`المدة يجب أن تكون بين ${BACKUP_ADS_CONFIG.display.minDuration} و ${BACKUP_ADS_CONFIG.display.maxDuration} ثانية`);
        }
        
        // Click action validation
        if (!Object.keys(BACKUP_ADS_CONFIG.clickActions).includes(adData.click_action)) {
            errors.push('إجراء النقر غير صالح');
        }
        
        // Click URL validation
        if (adData.click_action === 'url' && (!adData.click_url || !this.isValidUrl(adData.click_url))) {
            errors.push('رابط الوجهة مطلوب وصالح عند اختيار إجراء فتح رابط');
        }
        
        return errors;
    },
    
    // Validate file
    validateFile(file, type) {
        const errors = [];
        
        // File size validation
        if (file.size > BACKUP_ADS_CONFIG.upload.maxFileSize) {
            errors.push(`حجم الملف يجب أن يكون أقل من ${this.formatFileSize(BACKUP_ADS_CONFIG.upload.maxFileSize)}`);
        }
        
        // File type validation
        const allowedTypes = type === 'image' 
            ? BACKUP_ADS_CONFIG.upload.allowedImageTypes 
            : BACKUP_ADS_CONFIG.upload.allowedVideoTypes;
            
        if (!allowedTypes.includes(file.type)) {
            errors.push(`نوع الملف غير مدعوم. الأنواع المدعومة: ${allowedTypes.join(', ')}`);
        }
        
        return errors;
    },
    
    // Check if URL is valid
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    },
    
    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};

// Utility functions for backup ads
const BackupAdsUtils = {
    // Get priority info
    getPriorityInfo(priority) {
        return BACKUP_ADS_CONFIG.priorityLevels[priority] || BACKUP_ADS_CONFIG.priorityLevels[5];
    },
    
    // Get click action info
    getClickActionInfo(action) {
        return BACKUP_ADS_CONFIG.clickActions[action] || BACKUP_ADS_CONFIG.clickActions.none;
    },
    
    // Format duration
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds} ثانية`;
        } else {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes} دقيقة${remainingSeconds > 0 ? ` و ${remainingSeconds} ثانية` : ''}`;
        }
    },
    
    // Generate random ID
    generateId() {
        return 'backup_ad_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    },
    
    // Check if ad should be shown based on targeting
    shouldShowAd(ad, context = {}) {
        // Check if ad is active
        if (!ad.is_active) return false;
        
        // Check date range
        const now = new Date();
        if (ad.start_date && new Date(ad.start_date) > now) return false;
        if (ad.end_date && new Date(ad.end_date) < now) return false;
        
        // Check category targeting
        if (ad.target_categories && ad.target_categories.length > 0 && context.category) {
            if (!ad.target_categories.includes(context.category)) return false;
        }
        
        // Check user type targeting
        if (ad.target_user_types && ad.target_user_types.length > 0 && context.userType) {
            if (!ad.target_user_types.includes(context.userType)) return false;
        }
        
        return true;
    },
    
    // Get recommended dimensions for ad type
    getRecommendedDimensions(adType, variant = 'banner') {
        if (adType === 'image') {
            return BACKUP_ADS_CONFIG.upload.recommendedImageDimensions[variant] || 
                   BACKUP_ADS_CONFIG.upload.recommendedImageDimensions.banner;
        } else if (adType === 'video') {
            return BACKUP_ADS_CONFIG.upload.recommendedVideoDimensions[variant] || 
                   BACKUP_ADS_CONFIG.upload.recommendedVideoDimensions.landscape;
        }
        return null;
    }
};

// Export configuration and utilities
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        BACKUP_ADS_CONFIG,
        BackupAdsValidator,
        BackupAdsUtils
    };
} else {
    // Browser environment
    window.BACKUP_ADS_CONFIG = BACKUP_ADS_CONFIG;
    window.BackupAdsValidator = BackupAdsValidator;
    window.BackupAdsUtils = BackupAdsUtils;
}
