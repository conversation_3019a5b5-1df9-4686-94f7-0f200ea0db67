-- SQL code to create the free_addons table for managing Free Addons section
-- This table will store which mods should appear in the "Free Addons" section

CREATE TABLE IF NOT EXISTS free_addons (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    display_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Foreign key constraint to ensure mod exists
    CONSTRAINT fk_free_addons_mod_id
        FOREIGN KEY (mod_id)
        REFERENCES mods(id)
        ON DELETE CASCADE,

    -- Unique constraint to prevent duplicate entries
    CONSTRAINT unique_mod_in_free_addons
        UNIQUE (mod_id)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_free_addons_display_order ON free_addons(display_order);
CREATE INDEX IF NOT EXISTS idx_free_addons_active ON free_addons(is_active);
CREATE INDEX IF NOT EXISTS idx_free_addons_mod_id ON free_addons(mod_id);

-- Add RLS (Row Level Security) if needed
ALTER TABLE free_addons ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access
CREATE POLICY "Allow public read access on free_addons"
    ON free_addons FOR SELECT
    USING (true);

-- Create policy for admin write access (you can modify this based on your auth system)
CREATE POLICY "Allow admin write access on free_addons"
    ON free_addons FOR ALL
    USING (true);

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_free_addons_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp update
CREATE TRIGGER trigger_update_free_addons_updated_at
    BEFORE UPDATE ON free_addons
    FOR EACH ROW
    EXECUTE FUNCTION update_free_addons_updated_at();

-- إنشاء جدول Featured Addons (للقسم المميز)
CREATE TABLE IF NOT EXISTS featured_addons (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    display_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Foreign key constraint to ensure mod exists
    CONSTRAINT fk_featured_addons_mod_id
        FOREIGN KEY (mod_id)
        REFERENCES mods(id)
        ON DELETE CASCADE,

    -- Unique constraint to prevent duplicate entries
    CONSTRAINT unique_mod_in_featured_addons
        UNIQUE (mod_id)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_featured_addons_display_order ON featured_addons(display_order);
CREATE INDEX IF NOT EXISTS idx_featured_addons_active ON featured_addons(is_active);
CREATE INDEX IF NOT EXISTS idx_featured_addons_mod_id ON featured_addons(mod_id);

-- Add RLS (Row Level Security) if needed
ALTER TABLE featured_addons ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access
CREATE POLICY "Allow public read access on featured_addons"
    ON featured_addons FOR SELECT
    USING (true);

-- Create policy for admin write access
CREATE POLICY "Allow admin write access on featured_addons"
    ON featured_addons FOR ALL
    USING (true);

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_featured_addons_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp update
CREATE TRIGGER trigger_update_featured_addons_updated_at
    BEFORE UPDATE ON featured_addons
    FOR EACH ROW
    EXECUTE FUNCTION update_featured_addons_updated_at();

-- Insert some example data (optional - remove if not needed)
-- INSERT INTO free_addons (mod_id, display_order, is_active)
-- VALUES
--     (1, 1, true),
--     (2, 2, true),
--     (3, 3, true);
