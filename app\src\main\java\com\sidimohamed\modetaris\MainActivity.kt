package com.sidimohamed.modetaris

import android.annotation.SuppressLint
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.os.Environment // Keep for DIRECTORY_DOWNLOADS constant
import android.os.Handler // Import Handler
import android.os.Looper // Import Looper
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebView // Import standard WebView for WebViewClient parameter type
// REMOVED: Timer, TimerTask imports
// REMOVED: import android.webkit.WebView (using CustomWebView instead) - Note: Still need standard WebView for parameter types
import android.webkit.WebViewClient
import android.widget.RelativeLayout // Import RelativeLayout
import android.widget.Toast
// REMOVED: Permission imports (ActivityCompat, ContextCompat, Manifest, PackageManager)
import android.os.Build // Keep for SDK version check
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.pm.PackageManager
import android.Manifest
import androidx.activity.ComponentActivity
import androidx.activity.enableEdgeToEdge
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import android.content.SharedPreferences // Import SharedPreferences
import android.util.TypedValue // Import for DP calculation
import android.graphics.Color // Import for Color parsing
import android.provider.Settings // Import Settings for app settings intent
import android.view.View // Import View for visibility control
import android.view.ViewGroup // Import ViewGroup for finding views
import android.view.WindowManager // Import WindowManager for fullscreen flags
import androidx.core.view.ViewCompat // Import ViewCompat for window insets
import com.google.android.material.snackbar.Snackbar // Import Snackbar
import java.io.File
import java.net.URLDecoder
import java.util.concurrent.ConcurrentHashMap
import org.json.JSONObject // Import JSONObject for storing multiple pending updates
import java.util.HashSet // Import HashSet for storing downloaded mod IDs

// Import AdMob classes
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdView // Import AdView for Banner
import com.google.android.gms.ads.OnUserEarnedRewardListener // Still needed for Rewarded Ad
import com.google.android.gms.ads.rewarded.RewardedAd // Import for Rewarded Ad
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback // Import for Rewarded Ad
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen // Import for Splash Screen
import androidx.core.view.WindowCompat // Import for immersive mode
import androidx.core.view.WindowInsetsCompat // Import for immersive mode
import androidx.core.view.WindowInsetsControllerCompat // Import for immersive mode
import androidx.lifecycle.lifecycleScope // Import for coroutines
import kotlinx.coroutines.launch // For launching coroutines
// REMOVED: kotlinx.serialization.Serializable (No longer needed after AdConfig removal)

// REMOVED: Supabase Imports

// REMOVED: ScrollListener implementation

// REMOVED: Data class for Ad Configuration from Supabase (AdConfig)

class MainActivity : ComponentActivity() {

    // REMOVED: STORAGE_PERMISSION_REQUEST_CODE companion object

    private lateinit var webView: WebView
    private lateinit var adViewBanner: AdView // Declare AdView for Banner
    // REMOVED: private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private lateinit var downloadManager: DownloadManager
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var webAppInterface: WebAppInterface
    // Map to track active downloads initiated by this activity instance
    private val activeDownloads = ConcurrentHashMap<Long, String>() // downloadId -> modId
    // Handler for polling
    private val pollingHandler = Handler(Looper.getMainLooper())
    // Handler for auto-cleanup
    private val cleanupHandler = Handler(Looper.getMainLooper())
    // Runnable for auto-cleanup
    private val cleanupRunnable = Runnable { cleanupOldDownloads() }

    // AdMob Rewarded Ad
    private var mRewardedAd: RewardedAd? = null
    // Default/Fallback Ad Unit IDs (Constants)
    private companion object {
        const val DEFAULT_REWARDED_AD_UNIT_ID = "ca-app-pub-4373910379376809/6427121599"
        const val DEFAULT_BANNER_AD_UNIT_ID = "ca-app-pub-4373910379376809/6035179861"
    }
    private var currentRewardedAdUnitId: String = DEFAULT_REWARDED_AD_UNIT_ID
    private var isRewardedAdEnabled: Boolean = true // Default/Fallback

    // Banner Ad
    private var currentBannerAdUnitId: String = DEFAULT_BANNER_AD_UNIT_ID
    private var isBannerAdEnabled: Boolean = true // Default/Fallback
    private var bannerAdPosition: String = "bottom" // Default/Fallback

    private var pendingDownloadUrl: String? = null // To store URL while ad is showing
    private var pendingModId: String? = null       // To store Mod ID
    private var pendingModName: String? = null     // To store Mod Name

    // Flag to track the first touch event after page load
    private var isFirstTouchEventAfterLoad = true

    // Keys for SharedPreferences
    private val PREFS_NAME = "ModetarisPrefs"
    private val PENDING_DOWNLOADS_KEY = "pending_downloads" // Keep if still used elsewhere
    private val DOWNLOADED_MODS_KEY = "downloaded_mod_ids" // New key for tracking counted downloads
    private val MOD_DOWNLOAD_TIMESTAMPS_KEY = "mod_download_timestamps" // Key for tracking download timestamps
    private val AUTO_DELETE_ENABLED_KEY = "auto_delete_enabled" // Key for auto-delete feature toggle

    // تحسين ثوابت الحذف التلقائي
    private val AUTO_DELETE_DELAY_MS = 1 * 60 * 60 * 1000L // ساعة واحدة بدلاً من ساعتين لتوفير المساحة
    private val AUTO_CLEANUP_INTERVAL_MS = 15 * 60 * 1000L // فحص كل 15 دقيقة بدلاً من 30

    // REMOVED: Supabase Client and related properties


    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        // Keep the splash screen visible until initialization is complete
        val splashScreen = installSplashScreen()
        splashScreen.setKeepOnScreenCondition { true } // Keep splash screen visible

        super.onCreate(savedInstanceState)
        // enableEdgeToEdge() // Disable edge-to-edge as we are manually controlling system bars

        // Initialize services in background thread to prevent ANR
        lifecycleScope.launch {
            // Initialize Mobile Ads SDK
            MobileAds.initialize(this@MainActivity) {}
            Log.d("MainActivity", "MobileAds SDK Initialized.")

            // Apply default ad settings (can be done before view inflation)
            isRewardedAdEnabled = true // Default
            currentRewardedAdUnitId = DEFAULT_REWARDED_AD_UNIT_ID
            isBannerAdEnabled = true // Default
            currentBannerAdUnitId = DEFAULT_BANNER_AD_UNIT_ID
            bannerAdPosition = "bottom" // Default

            // REMOVED: Explicit window background setting that overwrites splash screen
            // val darkGrayColor = android.graphics.Color.parseColor("#21221f")
            // window.setBackgroundDrawable(ColorDrawable(darkGrayColor))

            downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            sharedPreferences = getSharedPreferences("ModetarisPrefs", Context.MODE_PRIVATE)

            // Create cache directories if they don't exist
            createCacheDirectories()

            // Signal that background initialization is complete
            runOnUiThread {
                // --- Load the layout file ---
                setContentView(R.layout.activity_main) // Load the XML layout

                // Allow the splash screen to be removed
                splashScreen.setKeepOnScreenCondition { false }

                // --- Find WebView and AdView from the layout ---
                webView = findViewById(R.id.webViewMain) // Find WebView by ID
                adViewBanner = findViewById(R.id.adViewBanner) // Find AdView by ID

                // --- ADS INITIALIZATION MOVED HERE (After views are found) ---
                // Load Rewarded Ad
                if (isRewardedAdEnabled) {
                    Log.d("MainActivity", "Rewarded Ad is ENABLED (default). Unit ID: $currentRewardedAdUnitId")
                    loadRewardedAd()
                } else {
                    Log.d("MainActivity", "Rewarded Ad is DISABLED (default).")
                    mRewardedAd = null
                }

                // Load Banner Ad
                if (isBannerAdEnabled) {
                    Log.d("MainActivity", "Banner Ad is ENABLED (default). Unit ID from XML, Position: $bannerAdPosition") // Updated Log
                    adViewBanner.visibility = View.VISIBLE
                    // adViewBanner.adUnitId = currentBannerAdUnitId // REMOVED: Ad Unit ID is set in XML

                    val layoutParams = adViewBanner.layoutParams as RelativeLayout.LayoutParams
                    val webViewLayoutParams = webView.layoutParams as RelativeLayout.LayoutParams

                    if (bannerAdPosition == "top") {
                        layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP)
                        webViewLayoutParams.removeRule(RelativeLayout.ABOVE)
                        webViewLayoutParams.addRule(RelativeLayout.BELOW, adViewBanner.id)
                    } else { // Default to bottom
                        layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_TOP)
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                        webViewLayoutParams.removeRule(RelativeLayout.BELOW)
                        webViewLayoutParams.addRule(RelativeLayout.ABOVE, adViewBanner.id)
                    }
                    adViewBanner.layoutParams = layoutParams
                    webView.layoutParams = webViewLayoutParams

                    val adRequestBanner = AdRequest.Builder().build()
                    adViewBanner.loadAd(adRequestBanner)
                    Log.d("MainActivity", "Banner Ad request sent with default settings.")
                } else {
                    Log.d("MainActivity", "Banner Ad is DISABLED (default).")
                    adViewBanner.visibility = View.GONE
                    val webViewLayoutParams = webView.layoutParams as RelativeLayout.LayoutParams
                    webViewLayoutParams.removeRule(RelativeLayout.ABOVE)
                    webViewLayoutParams.removeRule(RelativeLayout.BELOW)
                    webView.layoutParams = webViewLayoutParams
                }
                // --- END ADS INITIALIZATION ---

                // --- REMOVED: SwipeRefreshLayout setup and configuration ---

                // Hide system bars for immersive mode *after* setting content view
                hideSystemUI()

                // --- Setup scroll detection ---
                webView.setOnScrollChangeListener { _, _, scrollY, _, _ ->
                    val isAtTop = scrollY == 0
                    // Handle scroll state if needed
                    Log.d("MainActivity", "WebView scroll changed: isAtTop=$isAtTop")
                }

                val webSettings: WebSettings = webView.settings
                webSettings.javaScriptEnabled = true
                webSettings.domStorageEnabled = true
                webSettings.allowFileAccess = true // Keep true for file:/// URLs and potentially FileProvider access
                webSettings.allowContentAccess = true // Keep true
                // تحسين إعدادات التخزين المؤقت
                webSettings.cacheMode = WebSettings.LOAD_DEFAULT
                // Note: setAppCacheEnabled, setAppCachePath, and setAppCacheMaxSize are deprecated
                // and removed in newer Android versions. Modern WebView handles caching automatically.

                // تحسين قاعدة البيانات
                webSettings.databaseEnabled = true
                webSettings.domStorageEnabled = true

                // تحسينات إضافية للأداء
                // Note: setRenderPriority and setEnableSmoothTransition are deprecated
                // Modern WebView handles rendering optimization automatically

                // تحسين عرض الصفحة
                webView.isVerticalScrollBarEnabled = false
                webView.isHorizontalScrollBarEnabled = false
                webView.overScrollMode = android.view.View.OVER_SCROLL_NEVER

                // تحسينات إضافية للأداء
                webView.isScrollbarFadingEnabled = true
                webView.scrollBarDefaultDelayBeforeFade = 300
                webView.scrollBarFadeDuration = 300

                webAppInterface = WebAppInterface(this@MainActivity) // Initialize the member variable
                webView.addJavascriptInterface(webAppInterface, "AndroidInterface")

                webView.setBackgroundColor(android.graphics.Color.TRANSPARENT)

                // Explicitly enable hardware acceleration (should be default, but for safety)
                // webView.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                // Log.d("MainActivity", "Set WebView layer type to HARDWARE (explicitly)")

                // Hardware acceleration is generally preferred (default)
                // webView.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null) // Example if needed explicitly
                Log.d("MainActivity", "Using default WebView layer type (likely HARDWARE)")

                // --- Reverted: Removed Software Layer Type experiment ---
                // webView.setLayerType(android.view.View.LAYER_TYPE_SOFTWARE, null)
                // Log.d("MainActivity", "Set WebView layer type to SOFTWARE for testing")
                // --- END Reverted ---


                // --- REMOVED: Focus properties ---
                // webView.isFocusable = true
                // webView.isFocusableInTouchMode = true
                // --- END REMOVED ---

                // Ensure WebView is focusable before loading content
                webView.isFocusable = true
                webView.isFocusableInTouchMode = true
                Log.d("MainActivity", "Set WebView focusable and focusableInTouchMode to true")

                // --- Use Custom WebViewClient (Removed simulated click) ---
                webView.webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        Log.d("MainActivity", "WebView onPageFinished: $url")
                        // Execute JavaScript to attach the listener AFTER page is finished
                        view?.evaluateJavascript("javascript:attachDelayedEventListeners();", null)
                        Log.d("MainActivity", "onPageFinished: Called attachDelayedEventListeners()")

                        // Request focus after a short delay to potentially fix first click issue
                        Handler(Looper.getMainLooper()).postDelayed({
                            Log.d("MainActivity", "Requesting focus (FOCUS_DOWN) for WebView after delay.")
                            // Try focusing downwards into the view hierarchy
                            view?.requestFocus(android.view.View.FOCUS_DOWN)
                        }, 300) // 300ms delay

                        // Reset the first touch flag whenever a page finishes loading
                        isFirstTouchEventAfterLoad = true
                        Log.d("MainActivity", "onPageFinished: Reset isFirstTouchEventAfterLoad to true")
                    }

                    // --- ADDED: Handle external links ---
                    override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                        Log.d("MainActivity", "shouldOverrideUrlLoading: Intercepted URL: $url")
                        if (url == null) return false // Let WebView handle null URLs

                        val uri = Uri.parse(url)
                        return when (uri.scheme) {
                            "http", "https" -> {
                                // Open web links externally
                                Log.i("MainActivity", "Opening external web link: $url")
                                val intent = Intent(Intent.ACTION_VIEW, uri)
                                try {
                                    startActivity(intent)
                                    true // Indicate we've handled the URL
                                } catch (e: Exception) {
                                    Log.e("MainActivity", "Failed to open external web link", e)
                                    Toast.makeText(this@MainActivity, "لا يمكن فتح الرابط.", Toast.LENGTH_SHORT).show()
                                    true // Still handled, even if failed
                                }
                            }
                            "mailto" -> {
                                // Open email links externally
                                Log.i("MainActivity", "Opening mailto link: $url")
                                val intent = Intent(Intent.ACTION_SENDTO, uri)
                                try {
                                    startActivity(intent)
                                    true // Indicate we've handled the URL
                                } catch (e: Exception) {
                                    Log.e("MainActivity", "Failed to open mailto link", e)
                                    Toast.makeText(this@MainActivity, "لا يمكن فتح تطبيق البريد.", Toast.LENGTH_SHORT).show()
                                    true // Still handled, even if failed
                                }
                            }
                            // Add other schemes like "tel:", "sms:" if needed
                            else -> {
                                // Let the WebView handle other schemes (e.g., file:, data:, javascript:)
                                Log.d("MainActivity", "Letting WebView handle scheme: ${uri.scheme}")
                                false // Indicate WebView should handle it
                            }
                        }
                    }
                    // --- END ADDED ---
                }
                // --- END Custom WebViewClient ---

                // Clear cache before loading URL
                webView.clearCache(true)
                Log.d("MainActivity", "WebView cache cleared.")

                webView.loadUrl("file:///android_asset/index.html") // Changed back to index.html

                // --- REMOVED: Delayed focus request ---
                // Handler(Looper.getMainLooper()).postDelayed({
                //     webView.requestFocus()
                //     Log.d("MainActivity", "Delayed focus requested for WebView.")
                // }, 300) // Delay for 300 milliseconds (adjust if needed)

                // --- NATIVE TOUCH LISTENER REMOVED ---
                // The setOnTouchListener block was removed here to fix the first click issue.

                // Ad loading is now handled above, after views are initialized

                // Start the auto-cleanup service for downloaded mods
                scheduleNextCleanup()
                Log.d("MainActivity", "Auto-cleanup service started. Files will be deleted after ${AUTO_DELETE_DELAY_MS / 1000 / 60 / 60} hours")
            } // End of runOnUiThread
        } // End of lifecycleScope.launch

    } // Closing brace for onCreate

    // REMOVED: fetchAdConfiguration function
    // REMOVED: applyAdSettings function


    // Changed function name and implementation for RewardedAd
    private fun loadRewardedAd() {
        // Uses currentRewardedAdUnitId and isRewardedAdEnabled which are now set with defaults in onCreate
        if (!isRewardedAdEnabled || currentRewardedAdUnitId.isBlank()) {
            Log.w("MainActivity", "Rewarded ad loading skipped: disabled or no Unit ID (using defaults).")
            mRewardedAd = null
            return
        }
        Log.d("MainActivity", "Loading Rewarded Ad with Unit ID: $currentRewardedAdUnitId")
        val adRequest = AdRequest.Builder().build()
        RewardedAd.load(this, currentRewardedAdUnitId, adRequest, object : RewardedAdLoadCallback() {
            override fun onAdFailedToLoad(adError: LoadAdError) {
                Log.e("MainActivity", "RewardedAd failed to load: ${adError.message} (Unit ID: $currentRewardedAdUnitId)")
                mRewardedAd = null
            }

            override fun onAdLoaded(rewardedAd: RewardedAd) {
                Log.i("MainActivity", "RewardedAd loaded successfully. (Unit ID: $currentRewardedAdUnitId)")
                mRewardedAd = rewardedAd
                
                // تعيين إعدادات الإعلان المكافئ لضمان تغطية الشاشة بالكامل
                mRewardedAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
                    override fun onAdShowedFullScreenContent() {
                        Log.d("MainActivity", "Rewarded Ad showed fullscreen content.")
                        // إخفاء شريط الحالة وشريط التنقل عند عرض الإعلان
                        hideSystemUI()
                    }
                    
                    override fun onAdDismissedFullScreenContent() {
                        // إعادة عرض شريط الحالة وشريط التنقل بعد إغلاق الإعلان
                        showSystemUI()
                    }
                }
            }
        })
    }
    // --- End AdMob ---

    // وظيفة لإخفاء واجهة النظام (شريط الحالة وشريط التنقل) للعرض بملء الشاشة
    private fun hideSystemUI() {
        try {
            // إخفاء شريط الإشعارات (Status Bar)
            window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

            // إخفاء شريط التنقل (Navigation Bar)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // للإصدارات الأحدث من أندرويد (Android 11+)
                window.setDecorFitsSystemWindows(false)
                val controller = WindowCompat.getInsetsController(window, window.decorView)
                controller.hide(WindowInsetsCompat.Type.systemBars())
                controller.hide(WindowInsetsCompat.Type.statusBars())
                controller.hide(WindowInsetsCompat.Type.navigationBars())
                controller.hide(WindowInsetsCompat.Type.displayCutout())
                controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

                // إزالة جميع الحشوات لضمان العرض بملء الشاشة
                val rootView = window.decorView.findViewById<ViewGroup>(android.R.id.content)
                WindowCompat.setDecorFitsSystemWindows(window, false)
                ViewCompat.setOnApplyWindowInsetsListener(rootView) { view, windowInsets ->
                    view.setPadding(0, 0, 0, 0)
                    WindowInsetsCompat.CONSUMED
                }
            } else {
                // للإصدارات الأقدم من أندرويد
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LOW_PROFILE
                )
            }

            // تعيين العرض بملء الشاشة
            window.decorView.fitsSystemWindows = false

            // إضافة خصائص إضافية لضمان العرض بملء الشاشة
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT

            Log.d("MainActivity", "System UI hidden for fullscreen experience")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error hiding system UI", e)
        }
    }

    // وظيفة لإظهار واجهة النظام (شريط الحالة وشريط التنقل)
    private fun showSystemUI() {
        try {
            // إعادة عرض شريط الإشعارات (Status Bar)
            window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // للإصدارات الأحدث من أندرويد (Android 11+)
                window.setDecorFitsSystemWindows(true)
                val controller = WindowCompat.getInsetsController(window, window.decorView)
                controller.show(WindowInsetsCompat.Type.systemBars())
                controller.show(WindowInsetsCompat.Type.statusBars())
                controller.show(WindowInsetsCompat.Type.navigationBars())

                // إعادة تعيين الحشوات (padding) للعرض الطبيعي
                val rootView = window.decorView.findViewById<ViewGroup>(android.R.id.content)
                WindowCompat.setDecorFitsSystemWindows(window, true)
                ViewCompat.setOnApplyWindowInsetsListener(rootView) { view, windowInsets ->
                    val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
                    view.setPadding(insets.left, insets.top, insets.right, insets.bottom)
                    windowInsets
                }
            } else {
                // للإصدارات الأقدم من أندرويد
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
            }

            // إعادة تعيين العرض العادي
            window.decorView.fitsSystemWindows = true

            // إعادة تعيين ألوان أشرطة النظام
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.statusBarColor = resources.getColor(android.R.color.black, null)
                window.navigationBarColor = resources.getColor(android.R.color.black, null)
            }

            Log.d("MainActivity", "System UI restored")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error restoring system UI", e)
        }
    }



    // --- REMOVED: ScrollListener Implementation ---

    override fun onResume() {
        super.onResume()
        // Re-apply immersive mode when the activity resumes
        hideSystemUI()
        // REMOVED: Receiver registration
    }

    override fun onPause() {
        super.onPause()
       // REMOVED: Receiver unregistration
    }

    override fun onDestroy() {
        super.onDestroy()
        // Stop the auto-cleanup service
        cleanupHandler.removeCallbacks(cleanupRunnable)
        Log.d("MainActivity", "Auto-cleanup service stopped")
    }

    // --- SharedPreferences Helper Method ---
    private fun saveCompletedDownloadPath(context: Context?, modId: String, filePath: String) {
        // Add null check for context
        if (context == null) {
            Log.e("MainActivity", "Context is null in saveCompletedDownloadPath")
            return
        }
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val pendingJsonString = prefs.getString(PENDING_DOWNLOADS_KEY, "{}") ?: "{}"
        try {
            val pendingDownloads = JSONObject(pendingJsonString)
            pendingDownloads.put(modId, filePath)
            prefs.edit().putString(PENDING_DOWNLOADS_KEY, pendingDownloads.toString()).apply()
            Log.d("MainActivity", "Saved completed download path to SharedPreferences: $modId -> $filePath")
        } catch (e: org.json.JSONException) {
            Log.e("MainActivity", "Error saving completed download path to SharedPreferences", e)
        }
    }


    // Function to create cache directories
    private fun createCacheDirectories() {
        try {
            // Create WebView cache directories
            val webViewCacheDir = File(cacheDir, "WebView")
            if (!webViewCacheDir.exists()) {
                webViewCacheDir.mkdirs()
                Log.d("MainActivity", "Created WebView cache directory")
            }

            val defaultDir = File(webViewCacheDir, "Default")
            if (!defaultDir.exists()) {
                defaultDir.mkdirs()
                Log.d("MainActivity", "Created Default directory")
            }

            val httpCacheDir = File(defaultDir, "HTTP Cache")
            if (!httpCacheDir.exists()) {
                httpCacheDir.mkdirs()
                Log.d("MainActivity", "Created HTTP Cache directory")
            }

            val codeCacheDir = File(httpCacheDir, "Code Cache")
            if (!codeCacheDir.exists()) {
                codeCacheDir.mkdirs()
                Log.d("MainActivity", "Created Code Cache directory")
            }

            val wasmCacheDir = File(codeCacheDir, "wasm")
            if (!wasmCacheDir.exists()) {
                wasmCacheDir.mkdirs()
                Log.d("MainActivity", "Created wasm cache directory")
            }

            // Set permissions
            webViewCacheDir.setReadable(true, false)
            webViewCacheDir.setWritable(true, false)
            defaultDir.setReadable(true, false)
            defaultDir.setWritable(true, false)
            httpCacheDir.setReadable(true, false)
            httpCacheDir.setWritable(true, false)
            codeCacheDir.setReadable(true, false)
            codeCacheDir.setWritable(true, false)
            wasmCacheDir.setReadable(true, false)
            wasmCacheDir.setWritable(true, false)

            Log.d("MainActivity", "Cache directories created and permissions set")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error creating cache directories", e)
        }
    }

    // --- Function to initiate the actual download (Refactored from WebAppInterface) ---
    private fun initiateActualDownload(modId: String, modName: String, downloadUrl: String) {
        Log.d("MainActivity", "Initiating actual download: ID=$modId, Name=$modName, URL=$downloadUrl")

        // Validation and filename generation (moved from original startModDownload)
        if (!downloadUrl.startsWith("http")) {
            runOnUiThread { Toast.makeText(this, "رابط التحميل غير صالح.", Toast.LENGTH_SHORT).show() }
            Log.e("MainActivity", "Invalid download URL: $downloadUrl")
            return
        }

        // Use webAppInterface instance to call the helper function
        val filename = webAppInterface.generateFilenameFromUrl(modName, downloadUrl)
        if (filename == null) {
            runOnUiThread { Toast.makeText(this, "لا يمكن تحديد اسم الملف من الرابط.", Toast.LENGTH_SHORT).show() }
            Log.e("MainActivity", "Could not determine filename for URL: $downloadUrl")
            return
        }
        Log.d("MainActivity", "Determined filename for download: $filename")

        // Determine appropriate MIME type based on file extension
        val mimeType = when {
            filename.endsWith(".mcpack", ignoreCase = true) -> "application/mcpack"
            filename.endsWith(".mcaddon", ignoreCase = true) -> "application/mcaddon"
            filename.endsWith(".zip", ignoreCase = true) -> {
                // Para archivos ZIP, usamos application/zip para la descarga
                // y luego determinaremos el tipo MIME adecuado al abrir el archivo
                "application/zip"
            }
            else -> "application/octet-stream" // Default fallback
        }
        Log.d("MainActivity", "Using MIME type for download: $mimeType")

        // Download logic (moved from original startModDownload)
        try {
            val request = DownloadManager.Request(Uri.parse(downloadUrl))
            request.setTitle(filename) // Use the non-null filename directly
            request.setDescription("جارٍ تحميل المود...")
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            // Explicitly allow both Wi-Fi and Mobile networks
            request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or DownloadManager.Request.NETWORK_MOBILE)
            request.setAllowedOverMetered(true) // Keep this for clarity, though implied by NETWORK_MOBILE
            request.setAllowedOverRoaming(true) // Allow download while roaming
            // --- Use App-Specific External Downloads directory ---
            request.setDestinationInExternalFilesDir(this@MainActivity, Environment.DIRECTORY_DOWNLOADS, filename) // Use filename directly (already checked non-null)
            // --- END CHANGE ---
            request.setMimeType(mimeType) // Use the determined MIME type

            val downloadId = downloadManager.enqueue(request)
            Log.i("MainActivity", "Enqueued download with ID: $downloadId, Filename: $filename, MIME type: $mimeType, Destination: App's External Downloads") // Updated log message

            activeDownloads[downloadId] = modId
            Log.d("MainActivity", "Tracking download ID $downloadId for mod ID $modId.")

            webAppInterface.scheduleDownloadCheck(downloadId, modId) // Use helper from interface instance

            runOnUiThread {
                val message = "بدء تحميل $filename..."
                Toast.makeText(this, message, Toast.LENGTH_LONG).show()
            }

        } catch (e: Exception) {
            Log.e("MainActivity", "Error setting up or enqueuing download for $modName", e)
            runOnUiThread { Toast.makeText(this, "حدث خطأ أثناء بدء التحميل.", Toast.LENGTH_SHORT).show() }
        }
    }
    // --- End initiateActualDownload ---


    // JavaScript Interface Class
    inner class WebAppInterface(private val context: Context) {

        // Function to get app version name for JavaScript
        @JavascriptInterface
        fun getAppVersionName(): String {
            return try {
                val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                packageInfo.versionName ?: "unknown" // Use Elvis operator to handle null case
            } catch (e: Exception) {
                Log.e("WebAppInterface", "Error getting app version name", e)
                "unknown" // Fallback value if version can't be determined
            }
        }

        // Safe clipboard access function
        @JavascriptInterface
        fun copyToClipboard(text: String): Boolean {
            return try {
                // Only attempt to copy when app is in foreground
                runOnUiThread {
                    try {
                        val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                        val clipData = android.content.ClipData.newPlainText("Modetaris Text", text)
                        clipboardManager.setPrimaryClip(clipData)
                        Toast.makeText(context, "تم النسخ إلى الحافظة", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Log.e("WebAppInterface", "Error copying to clipboard", e)
                        Toast.makeText(context, "فشل النسخ إلى الحافظة", Toast.LENGTH_SHORT).show()
                    }
                }
                true
            } catch (e: Exception) {
                Log.e("WebAppInterface", "Error in copyToClipboard", e)
                false
            }
        }

        // *** NEW Function: Called by JS to request download WITH AD ***
        @JavascriptInterface
        fun requestModDownloadWithAd(modId: String, modName: String, downloadUrl: String) {
             Log.i("WebAppInterface", "requestModDownloadWithAd called for mod: $modName")
             runOnUiThread { // Ensure UI operations are on the main thread
                 // Store download info FIRST
                 pendingModId = modId
                 pendingModName = modName
                 pendingDownloadUrl = downloadUrl
                 Log.d("WebAppInterface", "Stored pending download info: ID=$modId, Name=$modName, URL=$downloadUrl")

                 // No longer need to check/request permission for app-specific storage on API 23+
                 Log.d("WebAppInterface", "Proceeding directly to ad/download flow (no permission check needed).")
                 showAdOrDownload() // Proceed directly
             }
        }

        // Helper function within WebAppInterface to avoid duplicating ad logic
        internal fun showAdOrDownload() {
            if (!isRewardedAdEnabled) {
                Log.i("WebAppInterface", "Rewarded ad is disabled by config. Proceeding directly with download.")
                if (<EMAIL> != null && <EMAIL> != null && <EMAIL> != null) {
                    <EMAIL>(<EMAIL>!!, <EMAIL>!!, <EMAIL>!!)
                }
                <EMAIL>()
                // Optionally, still try to load the next ad if it was just temporarily unavailable but now enabled
                if(isRewardedAdEnabled) loadRewardedAd()
                return
            }

            // Check if Rewarded Ad is loaded and show it
            if (mRewardedAd != null) {
                Log.i("WebAppInterface", "Rewarded Ad is loaded. Setting callbacks and showing ad...")

                // إخفاء واجهة النظام لضمان عرض الإعلان بملء الشاشة
                <EMAIL>()
                
                // إخفاء الشريط العلوي الثابت في WebView باستخدام JavaScript - محسّن
                webView.evaluateJavascript("javascript:(function() { " +
                        "// إخفاء جميع العناصر الثابتة بشكل شامل" +
                        "var allFixedElements = document.querySelectorAll(" +
                        "  'header, " +                                // جميع وسوم header
                        "  .navbar, " +                                // جميع عناصر navbar
                        "  .header, " +                                // جميع عناصر header
                        "  [class*=\"header\"], " +                    // أي عنصر يحتوي على كلمة header في اسم الفئة
                        "  [id*=\"header\"], " +                       // أي عنصر يحتوي على كلمة header في المعرف
                        "  [class*=\"nav\"], " +                       // أي عنصر يحتوي على كلمة nav في اسم الفئة
                        "  [id*=\"nav\"], " +                          // أي عنصر يحتوي على كلمة nav في المعرف
                        "  [class*=\"top\"], " +                       // أي عنصر يحتوي على كلمة top في اسم الفئة
                        "  [id*=\"top\"], " +                          // أي عنصر يحتوي على كلمة top في المعرف
                        "  [class*=\"bar\"], " +                       // أي عنصر يحتوي على كلمة bar في اسم الفئة
                        "  [id*=\"bar\"], " +                          // أي عنصر يحتوي على كلمة bar في المعرف
                        "  [style*=\"position: fixed\"], " +           // أي عنصر له نمط position: fixed
                        "  [style*=\"position:fixed\"], " +            // بديل بدون مسافة
                        "  [style*=\"position: sticky\"], " +          // أي عنصر له نمط position: sticky
                        "  [style*=\"position:sticky\"], " +           // بديل بدون مسافة
                        "  [style*=\"position: absolute\"], " +        // أي عنصر له نمط position: absolute في الأعلى
                        "  [style*=\"position:absolute\"], " +         // بديل بدون مسافة
                        "  [style*=\"top: 0\"], " +                    // أي عنصر له نمط top: 0
                        "  [style*=\"top:0\"], " +                     // بديل بدون مسافة
                        "  [style*=\"z-index\"], " +                   // أي عنصر له z-index عالي
                        "  .fixed, " +                                 // فئة fixed
                        "  .fixed-top, " +                             // فئة fixed-top
                        "  .sticky-top, " +                            // فئة sticky-top
                        "  .navbar-fixed-top, " +                      // فئة navbar-fixed-top
                        "  .app-header, " +                            // فئة app-header
                        "  .site-header, " +                           // فئة site-header
                        "  .page-header, " +                           // فئة page-header
                        "  .main-header, " +                           // فئة main-header
                        "  .top-bar, " +                               // فئة top-bar
                        "  .navigation-bar, " +                        // فئة navigation-bar
                        "  .toolbar, " +                               // فئة toolbar
                        "  .app-bar, " +                               // فئة app-bar
                        "  .action-bar, " +                            // فئة action-bar
                        "  .top-fixed-bar, " +                         // الشريط العلوي الثابت
                        "  .drawer, " +                                // الشريط الجانبي
                        "  .drawer-overlay, " +                        // خلفية الشريط الجانبي
                        "  .modal, " +                                 // النافذة المنبثقة
                        "  .download-bar, " +                          // شريط التحميل
                        "  .network-status-indicator, " +              // مؤشر حالة الشبكة
                        "  .offline-banner, " +                        // لافتة عدم الاتصال
                        "  .floating-icon, " +                         // أيقونة الاشتراك العائمة
                        "  #install-instructions-modal, " +            // نافذة تعليمات التثبيت
                        "  #image-zoom-modal, " +                      // نافذة تكبير الصورة
                        "  .banner-ad-modal" +                         // نافذة الإعلان البانر
                        "');" +
                        
                        "// إخفاء جميع العناصر المحددة" +
                        "for(var i=0; i<allFixedElements.length; i++) { " +
                        "    var element = allFixedElements[i];" +
                        "    // حفظ الحالة الأصلية للعنصر لاستعادتها لاحقاً" +
                        "    element.setAttribute('data-original-display', element.style.display || 'block');" +
                        "    element.style.display = 'none';" +
                        "    element.style.visibility = 'hidden';" +
                        "    element.style.opacity = '0';" +
                        "    element.style.pointerEvents = 'none';" +
                        "    console.log('Hidden fixed element:', element.tagName, element.className || element.id || 'unknown');" +
                        "}" +
                        
                        "// إخفاء أي عنصر مطلق الموضع في الأعلى" +
                        "var topElements = document.querySelectorAll('*');" +
                        "for(var i=0; i<topElements.length; i++) {" +
                        "    var element = topElements[i];" +
                        "    var style = window.getComputedStyle(element);" +
                        "    if((style.position === 'fixed' || style.position === 'sticky' || " +
                        "       (style.position === 'absolute' && (style.top === '0px' || parseInt(style.top) < 50))) && " +
                        "       style.display !== 'none') {" +
                        "        element.setAttribute('data-original-display', element.style.display || 'block');" +
                        "        element.style.display = 'none';" +
                        "        element.style.visibility = 'hidden';" +
                        "        element.style.opacity = '0';" +
                        "        element.style.pointerEvents = 'none';" +
                        "        console.log('Hidden computed fixed element:', element.tagName, element.className || element.id || 'unknown');" +
                        "    }" +
                        "}" +
                        
                        "// إضافة CSS مؤقت لمنع أي عناصر ثابتة من الظهور" +
                        "var style = document.createElement('style');" +
                        "style.id = 'temp-ad-styles';" +
                        "style.innerHTML = 'header, .navbar, .header, .fixed, .fixed-top, .sticky-top, .navbar-fixed-top, " +
                        "                  [style*=\"position: fixed\"], [style*=\"position:fixed\"], " +
                        "                  [style*=\"position: sticky\"], [style*=\"position:sticky\"], " +
                        "                  [style*=\"top: 0\"], [style*=\"top:0\"], " +
                        "                  [class*=\"header\"], [id*=\"header\"], " +
                        "                  [class*=\"nav\"], [id*=\"nav\"], " +
                        "                  [class*=\"top\"], [id*=\"top\"], " +
                        "                  [class*=\"bar\"], [id*=\"bar\"], " +
                        "                  .top-fixed-bar, .drawer, .drawer-overlay, .modal, .download-bar, " +
                        "                  .network-status-indicator, .offline-banner, .floating-icon, " +
                        "                  #install-instructions-modal, #image-zoom-modal, .banner-ad-modal " +
                        "                  { display: none !important; visibility: hidden !important; opacity: 0 !important; pointer-events: none !important; z-index: -9999 !important; }';" +
                        "document.head.appendChild(style);" +
                        
                        "console.log('Added temporary CSS to hide all fixed elements');" +
                        "})();", null)
                Log.d("WebAppInterface", "Executed enhanced JS to hide all fixed elements before showing ad")
                
                // Set FullScreenContentCallback before showing
                mRewardedAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
                    override fun onAdClicked() {
                        Log.d("WebAppInterface", "Rewarded Ad was clicked.")
                    }

                    override fun onAdDismissedFullScreenContent() {
                        Log.d("WebAppInterface", "Rewarded Ad dismissed.")

                        // إعادة عرض واجهة النظام بعد إغلاق الإعلان
                        <EMAIL>()

                        // إعادة عرض جميع العناصر المخفية
                        runOnUiThread {
                            // إعادة عرض WebView
                            webView.visibility = View.VISIBLE

                            // إعادة عرض البانر الإعلاني
                            if (isBannerAdEnabled) {
                                adViewBanner.visibility = View.VISIBLE
                            }
                            
                            // إعادة عرض الشريط العلوي الثابت في WebView باستخدام JavaScript - محسّن
                            webView.evaluateJavascript("javascript:(function() { " +
                                    "// إزالة CSS المؤقت أولاً" +
                                    "var tempStyle = document.getElementById('temp-ad-styles');" +
                                    "if(tempStyle) {" +
                                    "    tempStyle.parentNode.removeChild(tempStyle);" +
                                    "    console.log('Removed temporary CSS styles');" +
                                    "}" +
                                    
                                    "// استعادة جميع العناصر الثابتة بشكل شامل" +
                                    "var allFixedElements = document.querySelectorAll(" +
                                    "  'header, " +                                // جميع وسوم header
                                    "  .navbar, " +                                // جميع عناصر navbar
                                    "  .header, " +                                // جميع عناصر header
                                    "  [class*=\"header\"], " +                    // أي عنصر يحتوي على كلمة header في اسم الفئة
                                    "  [id*=\"header\"], " +                       // أي عنصر يحتوي على كلمة header في المعرف
                                    "  [class*=\"nav\"], " +                       // أي عنصر يحتوي على كلمة nav في اسم الفئة
                                    "  [id*=\"nav\"], " +                          // أي عنصر يحتوي على كلمة nav في المعرف
                                    "  [class*=\"top\"], " +                       // أي عنصر يحتوي على كلمة top في اسم الفئة
                                    "  [id*=\"top\"], " +                          // أي عنصر يحتوي على كلمة top في المعرف
                                    "  [class*=\"bar\"], " +                       // أي عنصر يحتوي على كلمة bar في اسم الفئة
                                    "  [id*=\"bar\"], " +                          // أي عنصر يحتوي على كلمة bar في المعرف
                                    "  [style*=\"position: fixed\"], " +           // أي عنصر له نمط position: fixed
                                    "  [style*=\"position:fixed\"], " +            // بديل بدون مسافة
                                    "  [style*=\"position: sticky\"], " +          // أي عنصر له نمط position: sticky
                                    "  [style*=\"position:sticky\"], " +           // بديل بدون مسافة
                                    "  [style*=\"position: absolute\"], " +        // أي عنصر له نمط position: absolute في الأعلى
                                    "  [style*=\"position:absolute\"], " +         // بديل بدون مسافة
                                    "  [style*=\"top: 0\"], " +                    // أي عنصر له نمط top: 0
                                    "  [style*=\"top:0\"], " +                     // بديل بدون مسافة
                                    "  [style*=\"z-index\"], " +                   // أي عنصر له z-index عالي
                                    "  .fixed, " +                                 // فئة fixed
                                    "  .fixed-top, " +                             // فئة fixed-top
                                    "  .sticky-top, " +                            // فئة sticky-top
                                    "  .navbar-fixed-top, " +                      // فئة navbar-fixed-top
                                    "  .app-header, " +                            // فئة app-header
                                    "  .site-header, " +                           // فئة site-header
                                    "  .page-header, " +                           // فئة page-header
                                    "  .main-header, " +                           // فئة main-header
                                    "  .top-bar, " +                               // فئة top-bar
                                    "  .navigation-bar, " +                        // فئة navigation-bar
                                    "  .toolbar, " +                               // فئة toolbar
                                    "  .app-bar, " +                               // فئة app-bar
                                    "  .action-bar, " +                            // فئة action-bar
                                    "  .top-fixed-bar, " +                         // الشريط العلوي الثابت
                                    "  .drawer, " +                                // الشريط الجانبي
                                    "  .drawer-overlay, " +                        // خلفية الشريط الجانبي
                                    "  .modal, " +                                 // النافذة المنبثقة
                                    "  .download-bar, " +                          // شريط التحميل
                                    "  .network-status-indicator, " +              // مؤشر حالة الشبكة
                                    "  .offline-banner, " +                        // لافتة عدم الاتصال
                                    "  .floating-icon, " +                         // أيقونة الاشتراك العائمة
                                    "  #install-instructions-modal, " +            // نافذة تعليمات التثبيت
                                    "  #image-zoom-modal, " +                      // نافذة تكبير الصورة
                                    "  .banner-ad-modal" +                         // نافذة الإعلان البانر
                                    "');" +
                                    
                                    "// استعادة جميع العناصر المحددة" +
                                    "for(var i=0; i<allFixedElements.length; i++) { " +
                                    "    var element = allFixedElements[i];" +
                                    "    // استعادة الحالة الأصلية للعنصر" +
                                    "    var originalDisplay = element.getAttribute('data-original-display');" +
                                    "    if(originalDisplay) {" +
                                    "        element.style.display = originalDisplay;" +
                                    "        element.removeAttribute('data-original-display');" +
                                    "    } else {" +
                                    "        element.style.display = '';" +
                                    "    }" +
                                    "    element.style.visibility = '';" +
                                    "    element.style.opacity = '';" +
                                    "    element.style.pointerEvents = '';" +
                                    "    console.log('Restored fixed element:', element.tagName, element.className || element.id || 'unknown');" +
                                    "}" +
                                    
                                    "// استعادة أي عنصر تم إخفاؤه بناءً على الأنماط المحسوبة" +
                                    "var allElements = document.querySelectorAll('[data-original-display]');" +
                                    "for(var i=0; i<allElements.length; i++) {" +
                                    "    var element = allElements[i];" +
                                    "    var originalDisplay = element.getAttribute('data-original-display');" +
                                    "    element.style.display = originalDisplay || '';" +
                                    "    element.style.visibility = '';" +
                                    "    element.style.opacity = '';" +
                                    "    element.style.pointerEvents = '';" +
                                    "    element.removeAttribute('data-original-display');" +
                                    "    console.log('Restored element with computed style:', element.tagName, element.className || element.id || 'unknown');" +
                                    "}" +
                                    
                                    "console.log('All fixed elements restored successfully');" +
                                    "})();", null)
                            Log.d("WebAppInterface", "Executed enhanced JS to restore all fixed elements after ad dismissal")

                            // إعادة عرض جميع العناصر في التخطيط الرئيسي
                            val decorView = window.decorView as ViewGroup
                            val rootView = decorView.findViewById<ViewGroup>(android.R.id.content)
                            val mainLayout = rootView.getChildAt(0) as? ViewGroup

                            mainLayout?.let { layout ->
                                for (i in 0 until layout.childCount) {
                                    val child = layout.getChildAt(i)
                                    child?.visibility = View.VISIBLE
                                }
                            }

                            Log.d("WebAppInterface", "All UI elements restored after ad dismissal")
                        }

                        // بدء تحميل المود مباشرة بعد إغلاق الإعلان
                        if (<EMAIL> != null && <EMAIL> != null && <EMAIL> != null) {
                            Log.i("WebAppInterface", "Ad dismissed, initiating download for mod: ${<EMAIL>}")
                            <EMAIL>(<EMAIL>!!, <EMAIL>!!, <EMAIL>!!)
                        } else {
                            Log.w("WebAppInterface", "Ad dismissed but no pending download info found.")
                        }

                        // تنظيف المعلومات المعلقة وتحميل إعلان جديد
                        <EMAIL>()
                        mRewardedAd = null
                        loadRewardedAd()
                    }

                    override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                        Log.e("WebAppInterface", "Rewarded Ad failed to show: ${adError.message}")

                        // إعادة عرض واجهة النظام في حالة فشل الإعلان
                        <EMAIL>()

                        // إعادة عرض جميع العناصر في حالة فشل الإعلان
                        runOnUiThread {
                            // إعادة عرض WebView
                            webView.visibility = View.VISIBLE

                            // إعادة عرض البانر الإعلاني
                            if (isBannerAdEnabled) {
                                adViewBanner.visibility = View.VISIBLE
                            }

                            // إعادة عرض أي شريط علوي ثابت في WebView باستخدام JavaScript - محسّن
                            webView.evaluateJavascript("javascript:(function() { " +
                                    "// إزالة CSS المؤقت أولاً" +
                                    "var tempStyle = document.getElementById('temp-ad-styles');" +
                                    "if(tempStyle) {" +
                                    "    tempStyle.parentNode.removeChild(tempStyle);" +
                                    "    console.log('Removed temporary CSS styles after ad failure');" +
                                    "}" +
                                    
                                    "// استعادة جميع العناصر الثابتة بشكل شامل" +
                                    "var allFixedElements = document.querySelectorAll(" +
                                    "  'header, " +                                // جميع وسوم header
                                    "  .navbar, " +                                // جميع عناصر navbar
                                    "  .header, " +                                // جميع عناصر header
                                    "  [class*=\"header\"], " +                    // أي عنصر يحتوي على كلمة header في اسم الفئة
                                    "  [id*=\"header\"], " +                       // أي عنصر يحتوي على كلمة header في المعرف
                                    "  [class*=\"nav\"], " +                       // أي عنصر يحتوي على كلمة nav في اسم الفئة
                                    "  [id*=\"nav\"], " +                          // أي عنصر يحتوي على كلمة nav في المعرف
                                    "  [class*=\"top\"], " +                       // أي عنصر يحتوي على كلمة top في اسم الفئة
                                    "  [id*=\"top\"], " +                          // أي عنصر يحتوي على كلمة top في المعرف
                                    "  [class*=\"bar\"], " +                       // أي عنصر يحتوي على كلمة bar في اسم الفئة
                                    "  [id*=\"bar\"], " +                          // أي عنصر يحتوي على كلمة bar في المعرف
                                    "  [style*=\"position: fixed\"], " +           // أي عنصر له نمط position: fixed
                                    "  [style*=\"position:fixed\"], " +            // بديل بدون مسافة
                                    "  [style*=\"position: sticky\"], " +          // أي عنصر له نمط position: sticky
                                    "  [style*=\"position:sticky\"], " +           // بديل بدون مسافة
                                    "  [style*=\"position: absolute\"], " +        // أي عنصر له نمط position: absolute في الأعلى
                                    "  [style*=\"position:absolute\"], " +         // بديل بدون مسافة
                                    "  [style*=\"top: 0\"], " +                    // أي عنصر له نمط top: 0
                                    "  [style*=\"top:0\"], " +                     // بديل بدون مسافة
                                    "  [style*=\"z-index\"], " +                   // أي عنصر له z-index عالي
                                    "  .fixed, " +                                 // فئة fixed
                                    "  .fixed-top, " +                             // فئة fixed-top
                                    "  .sticky-top, " +                            // فئة sticky-top
                                    "  .navbar-fixed-top, " +                      // فئة navbar-fixed-top
                                    "  .app-header, " +                            // فئة app-header
                                    "  .site-header, " +                           // فئة site-header
                                    "  .page-header, " +                           // فئة page-header
                                    "  .main-header, " +                           // فئة main-header
                                    "  .top-bar, " +                               // فئة top-bar
                                    "  .navigation-bar, " +                        // فئة navigation-bar
                                    "  .toolbar, " +                               // فئة toolbar
                                    "  .app-bar, " +                               // فئة app-bar
                                    "  .action-bar, " +                            // فئة action-bar
                                    "  .top-fixed-bar, " +                         // الشريط العلوي الثابت
                                    "  .drawer, " +                                // الشريط الجانبي
                                    "  .drawer-overlay, " +                        // خلفية الشريط الجانبي
                                    "  .modal, " +                                 // النافذة المنبثقة
                                    "  .download-bar, " +                          // شريط التحميل
                                    "  .network-status-indicator, " +              // مؤشر حالة الشبكة
                                    "  .offline-banner, " +                        // لافتة عدم الاتصال
                                    "  .floating-icon, " +                         // أيقونة الاشتراك العائمة
                                    "  #install-instructions-modal, " +            // نافذة تعليمات التثبيت
                                    "  #image-zoom-modal, " +                      // نافذة تكبير الصورة
                                    "  .banner-ad-modal" +                         // نافذة الإعلان البانر
                                    "');" +
                                    
                                    "// استعادة جميع العناصر المحددة" +
                                    "for(var i=0; i<allFixedElements.length; i++) { " +
                                    "    var element = allFixedElements[i];" +
                                    "    // استعادة الحالة الأصلية للعنصر" +
                                    "    var originalDisplay = element.getAttribute('data-original-display');" +
                                    "    if(originalDisplay) {" +
                                    "        element.style.display = originalDisplay;" +
                                    "        element.removeAttribute('data-original-display');" +
                                    "    } else {" +
                                    "        element.style.display = '';" +
                                    "    }" +
                                    "    element.style.visibility = '';" +
                                    "    element.style.opacity = '';" +
                                    "    element.style.pointerEvents = '';" +
                                    "    console.log('Restored fixed element after ad failure:', element.tagName, element.className || element.id || 'unknown');" +
                                    "}" +
                                    
                                    "// استعادة أي عنصر تم إخفاؤه بناءً على الأنماط المحسوبة" +
                                    "var allElements = document.querySelectorAll('[data-original-display]');" +
                                    "for(var i=0; i<allElements.length; i++) {" +
                                    "    var element = allElements[i];" +
                                    "    var originalDisplay = element.getAttribute('data-original-display');" +
                                    "    element.style.display = originalDisplay || '';" +
                                    "    element.style.visibility = '';" +
                                    "    element.style.opacity = '';" +
                                    "    element.style.pointerEvents = '';" +
                                    "    element.removeAttribute('data-original-display');" +
                                    "    console.log('Restored element with computed style after ad failure:', element.tagName, element.className || element.id || 'unknown');" +
                                    "}" +
                                    
                                    "console.log('All fixed elements restored successfully after ad failure');" +
                                    "})();", null)
                            Log.d("WebAppInterface", "Executed enhanced JS to restore all fixed elements after ad failure")

                            // إعادة عرض جميع العناصر في التخطيط الرئيسي
                            val decorView = window.decorView as ViewGroup
                            val rootView = decorView.findViewById<ViewGroup>(android.R.id.content)
                            val mainLayout = rootView.getChildAt(0) as? ViewGroup

                            mainLayout?.let { layout ->
                                for (i in 0 until layout.childCount) {
                                    val child = layout.getChildAt(i)
                                    child?.visibility = View.VISIBLE
                                }
                            }
                            Log.d("WebAppInterface", "All Android UI elements restored after ad failure")
                        }

                        // بدء تحميل المود مباشرة في حالة فشل الإعلان
                        if (<EMAIL> != null && <EMAIL> != null && <EMAIL> != null) {
                            Log.w("WebAppInterface", "Rewarded Ad failed to show, proceeding with download for mod: ${<EMAIL>}")
                            <EMAIL>(<EMAIL>!!, <EMAIL>!!, <EMAIL>!!)
                        }

                        <EMAIL>()
                        mRewardedAd = null
                        loadRewardedAd()
                    }

                    override fun onAdImpression() {
                        Log.d("WebAppInterface", "Rewarded Ad recorded an impression.")
                    }

                    override fun onAdShowedFullScreenContent() {
                        Log.d("WebAppInterface", "Rewarded Ad showed fullscreen content.")

                        // تأكيد أن الإعلان يعرض بملء الشاشة
                        <EMAIL>()

                        // إخفاء جميع العناصر لضمان عرض الإعلان بملء الشاشة
                        runOnUiThread {
                            // إخفاء WebView
                            webView.visibility = View.GONE

                            // إخفاء البانر الإعلاني أيضاً لضمان تغطية الشاشة بالكامل
                            adViewBanner.visibility = View.GONE
                            
                            // إخفاء الشريط العلوي الثابت في WebView باستخدام JavaScript (تأكيد إضافي) - محسّن
                            webView.evaluateJavascript("javascript:(function() { " +
                                    "// إخفاء جميع العناصر الثابتة بشكل شامل" +
                                    "var allFixedElements = document.querySelectorAll(" +
                                    "  'header, " +                                // جميع وسوم header
                                    "  .navbar, " +                                // جميع عناصر navbar
                                    "  .header, " +                                // جميع عناصر header
                                    "  [class*=\"header\"], " +                    // أي عنصر يحتوي على كلمة header في اسم الفئة
                                    "  [id*=\"header\"], " +                       // أي عنصر يحتوي على كلمة header في المعرف
                                    "  [class*=\"nav\"], " +                       // أي عنصر يحتوي على كلمة nav في اسم الفئة
                                    "  [id*=\"nav\"], " +                          // أي عنصر يحتوي على كلمة nav في المعرف
                                    "  [class*=\"top\"], " +                       // أي عنصر يحتوي على كلمة top في اسم الفئة
                                    "  [id*=\"top\"], " +                          // أي عنصر يحتوي على كلمة top في المعرف
                                    "  [class*=\"bar\"], " +                       // أي عنصر يحتوي على كلمة bar في اسم الفئة
                                    "  [id*=\"bar\"], " +                          // أي عنصر يحتوي على كلمة bar في المعرف
                                    "  [style*=\"position: fixed\"], " +           // أي عنصر له نمط position: fixed
                                    "  [style*=\"position:fixed\"], " +            // بديل بدون مسافة
                                    "  [style*=\"position: sticky\"], " +          // أي عنصر له نمط position: sticky
                                    "  [style*=\"position:sticky\"], " +           // بديل بدون مسافة
                                    "  [style*=\"position: absolute\"], " +        // أي عنصر له نمط position: absolute في الأعلى
                                    "  [style*=\"position:absolute\"], " +         // بديل بدون مسافة
                                    "  [style*=\"top: 0\"], " +                    // أي عنصر له نمط top: 0
                                    "  [style*=\"top:0\"], " +                     // بديل بدون مسافة
                                    "  [style*=\"z-index\"], " +                   // أي عنصر له z-index عالي
                                    "  .fixed, " +                                 // فئة fixed
                                    "  .fixed-top, " +                             // فئة fixed-top
                                    "  .sticky-top, " +                            // فئة sticky-top
                                    "  .navbar-fixed-top, " +                      // فئة navbar-fixed-top
                                    "  .app-header, " +                            // فئة app-header
                                    "  .site-header, " +                           // فئة site-header
                                    "  .page-header, " +                           // فئة page-header
                                    "  .main-header, " +                           // فئة main-header
                                    "  .top-bar, " +                               // فئة top-bar
                                    "  .navigation-bar, " +                        // فئة navigation-bar
                                    "  .toolbar, " +                               // فئة toolbar
                                    "  .app-bar, " +                               // فئة app-bar
                                    "  .action-bar" +                              // فئة action-bar
                                    "');" +
                                    
                                    "// إخفاء جميع العناصر المحددة" +
                                    "for(var i=0; i<allFixedElements.length; i++) { " +
                                    "    var element = allFixedElements[i];" +
                                    "    // حفظ الحالة الأصلية للعنصر لاستعادتها لاحقاً" +
                                    "    element.setAttribute('data-original-display', element.style.display || 'block');" +
                                    "    element.style.display = 'none';" +
                                    "    element.style.visibility = 'hidden';" +
                                    "    element.style.opacity = '0';" +
                                    "    element.style.pointerEvents = 'none';" +
                                    "    console.log('Hidden fixed element in onAdShowedFullScreenContent:', element.tagName, element.className || element.id || 'unknown');" +
                                    "}" +
                                    
                                    "// إخفاء أي عنصر مطلق الموضع في الأعلى" +
                                    "var topElements = document.querySelectorAll('*');" +
                                    "for(var i=0; i<topElements.length; i++) {" +
                                    "    var element = topElements[i];" +
                                    "    var style = window.getComputedStyle(element);" +
                                    "    if((style.position === 'fixed' || style.position === 'sticky' || " +
                                    "       (style.position === 'absolute' && (style.top === '0px' || parseInt(style.top) < 50))) && " +
                                    "       style.display !== 'none') {" +
                                    "        element.setAttribute('data-original-display', element.style.display || 'block');" +
                                    "        element.style.display = 'none';" +
                                    "        element.style.visibility = 'hidden';" +
                                    "        element.style.opacity = '0';" +
                                    "        element.style.pointerEvents = 'none';" +
                                    "        console.log('Hidden computed fixed element in onAdShowedFullScreenContent:', element.tagName, element.className || element.id || 'unknown');" +
                                    "    }" +
                                    "}" +
                                    
                                    "// تأكيد إضافي: إضافة CSS مؤقت لمنع أي عناصر ثابتة من الظهور" +
                                    "var style = document.createElement('style');" +
                                    "style.id = 'temp-ad-styles';" +
                                    "style.innerHTML = 'header, .navbar, .header, .fixed, .fixed-top, .sticky-top, .navbar-fixed-top, " +
                                    "                  [style*=\"position: fixed\"], [style*=\"position:fixed\"], " +
                                    "                  [style*=\"position: sticky\"], [style*=\"position:sticky\"], " +
                                    "                  [style*=\"top: 0\"], [style*=\"top:0\"], " +
                                    "                  [class*=\"header\"], [id*=\"header\"], " +
                                    "                  [class*=\"nav\"], [id*=\"nav\"], " +
                                    "                  [class*=\"top\"], [id*=\"top\"], " +
                                    "                  [class*=\"bar\"], [id*=\"bar\"], " +
                                    "                  .top-fixed-bar, .drawer, .drawer-overlay, .modal, .download-bar, " +
                                    "                  .network-status-indicator, .offline-banner, .floating-icon, " +
                                    "                  #install-instructions-modal, #image-zoom-modal, .banner-ad-modal " +
                                    "                  { display: none !important; visibility: hidden !important; opacity: 0 !important; pointer-events: none !important; z-index: -9999 !important; }';" +
                                    "document.head.appendChild(style);" +
                                    
                                    "console.log('Added temporary CSS to hide all fixed elements in onAdShowedFullScreenContent');" +
                                    "})();", null)
                            Log.d("WebAppInterface", "Executed enhanced JS to hide all fixed elements in onAdShowedFullScreenContent")

                            // إخفاء جميع العناصر الأخرى في التخطيط الرئيسي
                            val decorView = window.decorView as ViewGroup
                            val rootView = decorView.findViewById<ViewGroup>(android.R.id.content)
                            val mainLayout = rootView.getChildAt(0) as? ViewGroup

                            mainLayout?.let { layout ->
                                for (i in 0 until layout.childCount) {
                                    val child = layout.getChildAt(i)
                                    child?.visibility = View.GONE
                                }
                            }

                            Log.d("WebAppInterface", "All UI elements hidden for fullscreen ad display")
                        }

                        // إعداد مؤقت لضمان أن مدة الإعلان لا تتجاوز 30 ثانية
                        Handler(Looper.getMainLooper()).postDelayed({
                            // التحقق مما إذا كان الإعلان لا يزال معروضًا بعد 30 ثانية
                            if (mRewardedAd != null) {
                                Log.i("WebAppInterface", "30 seconds passed, ensuring reward is granted")

                                // بدء تحميل المود بعد 30 ثانية حتى لو لم ينته الإعلان
                                if (<EMAIL> != null && <EMAIL> != null && <EMAIL> != null) {
                                    Log.i("WebAppInterface", "30 seconds passed, initiating download for mod: ${<EMAIL>}")
                                    <EMAIL>(<EMAIL>!!, <EMAIL>!!, <EMAIL>!!)
                                    <EMAIL>()
                                }
                            }
                        }, 30000) // 30 ثانية
                    }
                }

                // Show the ad and define the reward listener
                mRewardedAd?.show(this@MainActivity, OnUserEarnedRewardListener { rewardItem ->
                    var rewardAmount = rewardItem.amount
                    var rewardType = rewardItem.type
                    Log.i("WebAppInterface", "User earned reward: Amount=$rewardAmount, Type=$rewardType (Item: ${rewardItem.type})")

                    // بدء تحميل المود فور الحصول على المكافأة
                    if (<EMAIL> != null && <EMAIL> != null && <EMAIL> != null) {
                        Log.i("WebAppInterface", "Reward earned, initiating download for mod: ${<EMAIL>}")
                        <EMAIL>(<EMAIL>!!, <EMAIL>!!, <EMAIL>!!)
                    } else {
                        Log.w("WebAppInterface", "Reward earned but no pending download info found.")
                    }
                    
                    // لا نقوم بمسح المعلومات المعلقة هنا لأن ذلك سيتم في onAdDismissedFullScreenContent
                    // <EMAIL>()
                })
            } else {
                Log.w("WebAppInterface", "Rewarded Ad not loaded when requested. Proceeding directly with download.")
                Toast.makeText(context, "Ad not ready, starting download.", Toast.LENGTH_SHORT).show()
                
                // بدء تحميل المود مباشرة إذا لم يكن الإعلان جاهزًا
                if (<EMAIL> != null && <EMAIL> != null && <EMAIL> != null) {
                    <EMAIL>(<EMAIL>!!, <EMAIL>!!, <EMAIL>!!)
                }
                
                <EMAIL>()
                loadRewardedAd()
            }
        }

        // REMOVED: Redundant clearPendingDownloadInfo helper in WebAppInterface


        // *** OLD Function: Keep for now, but should not be called directly by JS anymore ***
        @JavascriptInterface
        fun startModDownload(modId: String, modName: String, downloadUrl: String) {
             Log.w("WebAppInterface", "DEPRECATED startModDownload called directly. Use requestModDownloadWithAd instead.")
             // For safety, maybe just trigger the ad flow from here too? Or log an error.
             // Let's trigger the ad flow for robustness
             requestModDownloadWithAd(modId, modName, downloadUrl)

            /* --- Original logic moved to initiateActualDownload in MainActivity ---
            Log.d("WebAppInterface", "Received download request: ID=$modId, Name=$modName, URL=$downloadUrl")

            if (!downloadUrl.startsWith("http")) {
                runOnUiThread { Toast.makeText(context, "رابط التحميل غير صالح.", Toast.LENGTH_SHORT).show() }
                Log.e("WebAppInterface", "Invalid download URL: $downloadUrl")
                return
            }

            val filename = generateFilenameFromUrl(modName, downloadUrl)
            if (filename == null) {
                runOnUiThread { Toast.makeText(context, "لا يمكن تحديد اسم الملف من الرابط.", Toast.LENGTH_SHORT).show() }
                Log.e("WebAppInterface", "Could not determine filename for URL: $downloadUrl")
                return
            }
            Log.d("WebAppInterface", "Determined filename for download: $filename")

            // --- MODIFICATION START: Change download destination ---
            // Get the app-specific external downloads directory
            // Using setDestinationInExternalFilesDir handles directory creation and path logic
            // --- MODIFICATION END ---


            try {
                val request = DownloadManager.Request(Uri.parse(downloadUrl))
                request.setTitle(filename)
                request.setDescription("جارٍ تحميل المود...")
                request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
                request.setAllowedOverMetered(true)
                request.setAllowedOverRoaming(true)

                // --- MODIFICATION START: Set destination using setDestinationInExternalFilesDir ---
                request.setDestinationInExternalFilesDir(context, Environment.DIRECTORY_DOWNLOADS, filename)
                // --- MODIFICATION END ---

                request.setMimeType("application/octet-stream") // Keep generic type

                val downloadId = downloadManager.enqueue(request)
                Log.i("WebAppInterface", "Enqueued download with ID: $downloadId, Filename: $filename, Destination: App's External Downloads")

                // Store the download ID and mod ID for tracking completion
                activeDownloads[downloadId] = modId
                Log.d("WebAppInterface", "Tracking download ID $downloadId for mod ID $modId.")

                // Start polling for download status using Handler
                scheduleDownloadCheck(downloadId, modId)

                runOnUiThread {
                    val message = "بدء تحميل $filename..."
                    Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                }

            } catch (e: Exception) {
                Log.e("WebAppInterface", "Error setting up or enqueuing download for $modName", e)
                runOnUiThread { Toast.makeText(context, "حدث خطأ أثناء بدء التحميل.", Toast.LENGTH_SHORT).show() }
            }
            */
        }


        // --- Download Status Polling (Handler-based) ---
        // Make this public so MainActivity can call it
        fun scheduleDownloadCheck(downloadId: Long, modId: String) {
             Log.i("WebAppInterface", "*** scheduleDownloadCheck called for downloadId: $downloadId ***") // Log entry
             // Check if download is still being tracked before scheduling
             if (!activeDownloads.containsKey(downloadId)) {
                 Log.w("WebAppInterface", "scheduleDownloadCheck: Download $downloadId no longer tracked, stopping polling.")
                 return
             }
             Log.i("WebAppInterface", "Scheduling status check via Handler for downloadId: $downloadId in 2 seconds.")
             pollingHandler.postDelayed({
                 Log.i("WebAppInterface", "*** Handler executing check for downloadId: $downloadId ***") // Log handler execution
                 checkDownloadStatusAndProceed(downloadId, modId)
             }, 500) // Check after 0.5 seconds (reduced interval)
        }

        // Helper function to get progress and detailed status string
        @SuppressLint("Range") // Suppress Range warning for getColumnIndex
        private fun getDownloadProgress(downloadId: Long): Triple<Int, Int, String> {
            val query = DownloadManager.Query().setFilterById(downloadId)
            var cursor = downloadManager.query(query)
            var progress = 0
            var status = DownloadManager.STATUS_FAILED // Default to failed
            var statusString = "Failed" // Default status string

            cursor?.use {
                if (it.moveToFirst()) {
                    val statusIndex = it.getColumnIndex(DownloadManager.COLUMN_STATUS)
                    val reasonIndex = it.getColumnIndex(DownloadManager.COLUMN_REASON) // Get reason column
                    val bytesDownloadedIndex = it.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR)
                    val totalBytesIndex = it.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES)

                    if (statusIndex != -1) {
                        status = it.getInt(statusIndex)
                    } else {
                        Log.e("WebAppInterface", "getDownloadProgress: COLUMN_STATUS not found for $downloadId")
                        return Triple(0, DownloadManager.STATUS_FAILED, "Error") // Return error state
                    }

                    // Calculate progress if columns exist
                    if (bytesDownloadedIndex != -1 && totalBytesIndex != -1) {
                        val bytesDownloaded = it.getLong(bytesDownloadedIndex)
                        val totalBytes = it.getLong(totalBytesIndex)
                        Log.v("WebAppInterface", "Progress Check: ID=$downloadId, Bytes=$bytesDownloaded, Total=$totalBytes")

                        if (totalBytes > 0) {
                            progress = ((bytesDownloaded * 100L) / totalBytes).toInt()
                        } else if (totalBytes == -1L && status == DownloadManager.STATUS_RUNNING) {
                            progress = -1 // Indeterminate progress
                        } else {
                            progress = 0 // Default to 0 if total size is 0 or unknown in non-running state
                        }
                    } else {
                        Log.w("WebAppInterface", "Download $downloadId: Progress columns not found.")
                        progress = if (status == DownloadManager.STATUS_RUNNING) -1 else 0 // Indeterminate if running, else 0
                    }

                    // Determine status string based on status and reason
                    statusString = when (status) {
                        DownloadManager.STATUS_SUCCESSFUL -> "Completed"
                        DownloadManager.STATUS_FAILED -> "Failed"
                        DownloadManager.STATUS_PENDING -> "Pending..."
                        DownloadManager.STATUS_RUNNING -> "Downloading..."
                        DownloadManager.STATUS_PAUSED -> {
                            val reason = if (reasonIndex != -1) it.getInt(reasonIndex) else 0
                            when (reason) {
                                DownloadManager.PAUSED_WAITING_TO_RETRY -> "Retrying..."
                                DownloadManager.PAUSED_WAITING_FOR_NETWORK -> "Waiting for network..."
                                DownloadManager.PAUSED_QUEUED_FOR_WIFI -> "Waiting for Wi-Fi..."
                                else -> "Paused" // PAUSED_UNKNOWN or other reasons
                            }
                        }
                        else -> "Unknown"
                    }
                    Log.d("WebAppInterface", "Download $downloadId Status: $statusString, Progress: $progress%")

                } else {
                    Log.w("WebAppInterface", "getDownloadProgress: Cursor empty for $downloadId")
                    status = DownloadManager.STATUS_FAILED // Treat as failed if cursor is empty
                    statusString = "Error"
                }
            } ?: run {
                Log.e("WebAppInterface", "getDownloadProgress: Query failed for $downloadId")
                status = DownloadManager.STATUS_FAILED // Treat as failed if query fails
                statusString = "Error"
            }

            return Triple(progress, status, statusString)
        }


        // Corrected checkDownloadStatusAndProceed function
        private fun checkDownloadStatusAndProceed(downloadId: Long, modId: String) {
            Log.i("WebAppInterface", "*** checkDownloadStatusAndProceed called for downloadId: $downloadId ***") // Log entry
            // Ensure download is still tracked
            if (!activeDownloads.containsKey(downloadId)) {
                Log.d("WebAppInterface", "checkDownloadStatusAndProceed: Download $downloadId no longer tracked.")
                return
            }

            // Get progress, status code, and status string
            val (progress, status, statusString) = getDownloadProgress(downloadId) // Destructuring declaration inside the function

            // Update JavaScript UI
            // Escape single quotes in statusString for JavaScript safety
            val escapedStatusString = statusString.replace("'", "\\'")
            // Use MainActivity's webView instance
            val jsUpdate = "javascript:updateDownloadProgress('$modId', $progress, '$escapedStatusString');"
            <EMAIL> {
                <EMAIL>(jsUpdate, null) // Use MainActivity's webView
            }

            // Handle download completion or failure
            when (status) {
                DownloadManager.STATUS_SUCCESSFUL -> {
                    Log.i("WebAppInterface", "Polling: Download $downloadId successful.")
                    queryAndOpenFile(downloadId, modId) // This will call openDownloadedMod
                    activeDownloads.remove(downloadId) // Stop tracking
                }
                DownloadManager.STATUS_FAILED -> {
                    Log.e("WebAppInterface", "Polling: Download $downloadId failed.")
                    // Use MainActivity context for Toast
                    <EMAIL> { Toast.makeText(this@MainActivity, "فشل تحميل المود.", Toast.LENGTH_SHORT).show() }
                    activeDownloads.remove(downloadId) // Stop tracking
                }
                DownloadManager.STATUS_PENDING, DownloadManager.STATUS_RUNNING, DownloadManager.STATUS_PAUSED -> {
                    // Download is ongoing, reschedule the check
                    Log.d("WebAppInterface", "Polling: Download $downloadId not complete (Status: $statusString). Rescheduling check.")
                    scheduleDownloadCheck(downloadId, modId)
                }
                else -> { // Includes status -1 (error/not found) or other unknown statuses
                    Log.w("WebAppInterface", "Polling: Download $downloadId has unknown status ($status) or query failed. Stopping check.")
                    activeDownloads.remove(downloadId) // Stop tracking
                }
            }
        } // End of checkDownloadStatusAndProceed

        private fun queryAndOpenFile(downloadId: Long, modId: String) {
             val query = DownloadManager.Query().setFilterById(downloadId)
             var cursor = downloadManager.query(query)
             cursor?.use {
                 if (it.moveToFirst()) {
                     // Get the URI instead of the local filename path
                     val uriIndex = it.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)
                     if (uriIndex != -1) {
                         val uriString = it.getString(uriIndex)
                         if (uriString != null) {
                             try {
                                 // 1. Parse the file URI from DownloadManager
                                 val fileUri = Uri.parse(uriString)
                                 Log.d("WebAppInterface", "queryAndOpenFile: DownloadManager URI: $fileUri for $downloadId")

                                 // 2. Get the actual file path from the file URI
                                 // Important: DownloadManager might return a file:// URI even for internal storage
                                 val filePath = fileUri.path // Get path from file URI
                                 if (filePath == null) {
                                     Log.e("WebAppInterface", "queryAndOpenFile: Could not get path from URI: $fileUri")
                                     return@use // Exit cursor block if path is null
                                 }
                                 val file = File(filePath)

                                 // 3. Check if the file exists before creating content URI
                                 if (!file.exists()) {
                                     Log.e("WebAppInterface", "queryAndOpenFile: File does not exist at path: $filePath")
                                     runOnUiThread { Toast.makeText(context, "ملف التحميل غير موجود.", Toast.LENGTH_SHORT).show() }
                                     return@use // Exit cursor block
                                 }

                                 // 4. Generate a content URI using FileProvider
                                 val authority = "${<EMAIL>}.provider" // Use MainActivity context for package name
                                 Log.d("WebAppInterface", "queryAndOpenFile: Using authority: $authority for file: ${file.absolutePath}") // Log authority and file path
                                 val contentUri = FileProvider.getUriForFile(this@MainActivity, authority, file) // Use MainActivity context
                                 Log.d("WebAppInterface", "queryAndOpenFile: Generated FileProvider URI: $contentUri for $downloadId") // Log generated URI

                                 // --- INCREMENT DOWNLOAD COUNT LOGIC ---
                                 incrementDownloadCountIfNeeded(modId)
                                 // --- END INCREMENT DOWNLOAD COUNT LOGIC ---

                                 // --- RECORD DOWNLOAD TIMESTAMP FOR AUTO-DELETE ---
                                 recordDownloadTimestamp(modId)
                                 // --- END RECORD DOWNLOAD TIMESTAMP ---

                                 // --- MARK AS DOWNLOADED IN JAVASCRIPT ---
                                 val jsMarkDownloaded = "javascript:markModAsDownloaded('$modId');"
                                 <EMAIL> { // Use MainActivity context
                                     <EMAIL>(jsMarkDownloaded, null) // Use MainActivity's webView
                                     Log.d("WebAppInterface", "Executed JS to mark mod as downloaded: $jsMarkDownloaded")
                                 }
                                 // --- END MARK AS DOWNLOADED ---

                                 // 5. Open the file using the Content URI on the main thread
                                 <EMAIL> { // Use MainActivity context
                                     openDownloadedMod(contentUri) // Pass the content:// Uri object
                                 }
                             } catch (e: Exception) {
                                 // Catch potential exceptions during URI generation or file access
                                 Log.e("WebAppInterface", "queryAndOpenFile: Error processing URI or creating content URI for '$uriString'", e)
                                 <EMAIL> { Toast.makeText(this@MainActivity, "خطأ في الوصول إلى الملف المحمل.", Toast.LENGTH_SHORT).show() } // Use MainActivity context
                             }
                         } else {
                             Log.e("WebAppInterface", "queryAndOpenFile: Local URI string is null for $downloadId")
                         }
                     } else {
                          Log.e("WebAppInterface", "queryAndOpenFile: COLUMN_LOCAL_URI not found for $downloadId")
                     }
                 } else {
                      Log.w("WebAppInterface", "queryAndOpenFile: Cursor empty for $downloadId")
                 }
             } ?: run {
                 Log.e("WebAppInterface", "queryAndOpenFile: Query failed for $downloadId")
             }
         } // End of queryAndOpenFile

         // --- NEW @JavascriptInterface function to open an already downloaded file ---
         @JavascriptInterface
         fun openDownloadedMod(modId: String, modName: String, downloadUrl: String) {
             Log.d("WebAppInterface", "JS request to open already downloaded mod: ID=$modId, Name=$modName")

             // 1. Regenerate the expected filename
             val filename = generateFilenameFromUrl(modName, downloadUrl)
             if (filename == null) {
                 Log.e("WebAppInterface", "openDownloadedMod: Could not generate filename for URL: $downloadUrl")
                 <EMAIL> { Toast.makeText(this@MainActivity, "لا يمكن تحديد اسم الملف.", Toast.LENGTH_SHORT).show() } // Use MainActivity context
                 return
             }

             // 2. Construct the File path in the app-specific external Downloads directory
             val downloadsDir = <EMAIL>(Environment.DIRECTORY_DOWNLOADS) // Use MainActivity context
             if (downloadsDir == null) {
                 Log.e("WebAppInterface", "openDownloadedMod (JS Interface): External files directory is null.")
                 <EMAIL> { Toast.makeText(this@MainActivity, "لا يمكن الوصول إلى مجلد التحميلات.", Toast.LENGTH_SHORT).show() } // Use MainActivity context
                 return
             }
             // Correct File constructor call
             val file = File(downloadsDir, filename)
             Log.d("WebAppInterface", "openDownloadedMod (JS Interface): Checking for file at: ${file.absolutePath}") // Use file.absolutePath

             // 3. Check if the file exists
             if (file.exists()) { // Use file.exists()
                 try {
                     // 4. Get the FileProvider URI
                     val authority = "${<EMAIL>}.provider" // Use MainActivity context for package name
                     Log.d("WebAppInterface", "openDownloadedMod (JS): Using authority: $authority for file: ${file.absolutePath}") // Log authority and file path
                     val contentUri = FileProvider.getUriForFile(this@MainActivity, authority, file) // Use MainActivity context
                     Log.d("WebAppInterface", "openDownloadedMod (JS): Found file, generated FileProvider URI: $contentUri") // Log generated URI

                     // 5. Call the internal helper to open the file
                     <EMAIL> { // Use MainActivity context
                         openDownloadedMod(contentUri) // Call the existing helper that takes a Uri
                     }
                 } catch (e: Exception) {
                     Log.e("WebAppInterface", "openDownloadedMod: Error creating content URI for existing file: ${file.absolutePath}", e) // Use file.absolutePath
                     <EMAIL> { Toast.makeText(this@MainActivity, "خطأ في الوصول إلى الملف.", Toast.LENGTH_SHORT).show() } // Use MainActivity context
                 }
             } else {
                 // 6. File not found - Trigger the ad/download flow again
                 Log.w("WebAppInterface", "openDownloadedMod: File not found at ${file.absolutePath}. Triggering download flow.") // Use file.absolutePath
                 // Call requestModDownloadWithAd to show the ad before re-downloading
                 // No need <NAME_EMAIL> as it's within the same class instance
                 requestModDownloadWithAd(modId, modName, downloadUrl)
                 // Remove the Toast message as the ad flow will handle user feedback
                 // runOnUiThread { Toast.makeText(context, "لم يتم العثور على الملف المحمل. ربما تم حذفه؟", Toast.LENGTH_LONG).show() }
             }
         } // End of openDownloadedMod (JS Interface)
         // --- END new openDownloadedMod interface function ---


         // --- New function to handle download count increment ---
         private fun incrementDownloadCountIfNeeded(modId: String) {
            val downloadedMods = sharedPreferences.getStringSet(DOWNLOADED_MODS_KEY, HashSet<String>()) ?: HashSet()

            if (!downloadedMods.contains(modId)) {
                Log.i("WebAppInterface", "First successful download for mod $modId. Incrementing count.")
                // Add modId to the set
                val newSet = HashSet(downloadedMods) // Create a mutable copy
                newSet.add(modId)
                sharedPreferences.edit().putStringSet(DOWNLOADED_MODS_KEY, newSet).apply()

                 // Call JavaScript to update the UI
                 val jsUiUpdateCode = "javascript:androidDidIncrementDownloadCount('$modId');"
                 <EMAIL> { // Use MainActivity context
                     <EMAIL>(jsUiUpdateCode, null) // Use MainActivity's webView
                     Log.d("WebAppInterface", "Executed JS for UI update: $jsUiUpdateCode")
                 }

                 // --- ADDED: Call JavaScript to persist the increment in the database ---
                 val jsPersistCode = "javascript:androidShouldPersistDownloadIncrement('$modId');"
                 <EMAIL> { // Use MainActivity context
                     <EMAIL>(jsPersistCode, null) // Use MainActivity's webView
                     Log.d("WebAppInterface", "Executed JS for persistence: $jsPersistCode")
                 }
                // --- END ADDED ---

            } else {
                Log.d("WebAppInterface", "Mod $modId already counted. Skipping increment.")
            }
        }
        // --- End incrementDownloadCountIfNeeded ---

        // --- New function to record download timestamp for auto-delete ---
        private fun recordDownloadTimestamp(modId: String) {
            try {
                // Get the current timestamps
                val timestampsJson = sharedPreferences.getString(MOD_DOWNLOAD_TIMESTAMPS_KEY, "{}") ?: "{}"
                val timestamps = JSONObject(timestampsJson)

                // Record the current time for this mod
                val currentTime = System.currentTimeMillis()
                timestamps.put(modId, currentTime)

                // Save the updated timestamps
                sharedPreferences.edit().putString(MOD_DOWNLOAD_TIMESTAMPS_KEY, timestamps.toString()).apply()

                Log.d("WebAppInterface", "Recorded download timestamp for mod $modId: ${currentTime}")
            } catch (e: Exception) {
                Log.e("WebAppInterface", "Error recording download timestamp for mod $modId", e)
            }
        }
        // --- End recordDownloadTimestamp ---

        // --- End Download Status Polling ---

        // --- REMOVED: hideRefreshIndicator function ---

        // Function to open external URLs (for update notifications)
        @JavascriptInterface
        fun openExternalUrl(url: String) {
            if (url.isNotEmpty() && (url.startsWith("http://") || url.startsWith("https://"))) {
                Log.d("WebAppInterface", "Opening external URL: $url")
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                try {
                    context.startActivity(intent)
                } catch (e: Exception) {
                    Log.e("WebAppInterface", "Error opening URL: $url", e)
                    Toast.makeText(context, "لا يمكن فتح الرابط", Toast.LENGTH_SHORT).show()
                }
            } else {
                Log.e("WebAppInterface", "Invalid URL: $url")
                Toast.makeText(context, "رابط غير صالح", Toast.LENGTH_SHORT).show()
            }
        }


        @JavascriptInterface // Function for JS to get the stored path (keep for potential future use)
        fun getDownloadedModPath(modId: String): String? {
             val pendingJsonString = sharedPreferences.getString(PENDING_DOWNLOADS_KEY, "{}") ?: "{}"
             if (pendingJsonString == "{}") return null // No pending downloads object stored

             try {
                 val pendingDownloads = JSONObject(pendingJsonString)
                 val path = pendingDownloads.optString(modId, null) // Get path or null if not found
                 Log.d("WebAppInterface", "JS requested path for mod $modId. Found in SharedPreferences: $path")
                 // Note: We don't remove the entry here. It persists until the app data is cleared or overwritten.
                 return path
             } catch (e: org.json.JSONException) {
                  Log.e("WebAppInterface", "Error parsing SharedPreferences JSON for getDownloadedModPath", e)
                  return null
             }
         } // End of getDownloadedModPath

        // Modified to accept Uri directly again
        fun openDownloadedMod(contentUri: Uri) {
            Log.d("WebAppInterface", "Request to open file with URI: $contentUri")

            // Determine MIME type based on file extension
            val uriString = contentUri.toString()
            val mimeType = when {
                uriString.endsWith(".mcpack", ignoreCase = true) -> "application/mcpack"
                uriString.endsWith(".mcaddon", ignoreCase = true) -> "application/mcaddon"
                uriString.endsWith(".zip", ignoreCase = true) -> {
                    // Para archivos ZIP, intentamos usar un tipo MIME que Minecraft pueda reconocer
                    // Primero intentamos con application/mcaddon que es más común para mods
                    "application/mcaddon"
                    // Si esto falla, tenemos un fallback en el bloque catch que intentará con application/mcpack
                    // y finalmente con */* si es necesario
                }
                else -> "*/*" // Default fallback
            }

            Log.d("WebAppInterface", "Determined MIME type for file: $mimeType")

            val openIntent = Intent(Intent.ACTION_VIEW).apply {
                // Use the provided content URI with the determined MIME type
                setDataAndType(contentUri, mimeType)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION) // Grant read permission
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK) // Needed if starting from non-Activity context
            }

            try {
                Log.d("WebAppInterface", "Attempting to start ACTION_VIEW intent. URI: $contentUri, Type: $mimeType, Flags: ${openIntent.flags}") // Log intent details
                context.startActivity(openIntent)
                Log.d("WebAppInterface", "startActivity called successfully for URI: $contentUri")

                // Show success message
                <EMAIL> {
                    Toast.makeText(this@MainActivity, "جاري فتح الملف في ماين كرافت...", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e("WebAppInterface", "Failed to start activity for URI: $contentUri with MIME type: $mimeType", e)

                // Si el archivo es ZIP y falló con application/mcaddon, intentar con application/mcpack
                if (uriString.endsWith(".zip", ignoreCase = true) && mimeType == "application/mcaddon") {
                    Log.d("WebAppInterface", "ZIP file failed with application/mcaddon, trying with application/mcpack")
                    val mcpackIntent = Intent(Intent.ACTION_VIEW).apply {
                        setDataAndType(contentUri, "application/mcpack")
                        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }

                    try {
                        context.startActivity(mcpackIntent)
                        Log.d("WebAppInterface", "Successfully opened ZIP file with application/mcpack MIME type")
                        return
                    } catch (e2: Exception) {
                        Log.e("WebAppInterface", "Failed to open ZIP with application/mcpack", e2)
                        // Continuar con el siguiente intento
                    }
                }

                // Try again with generic MIME type as fallback
                if (mimeType != "*/*") {
                    Log.d("WebAppInterface", "Trying again with generic MIME type")
                    val fallbackIntent = Intent(Intent.ACTION_VIEW).apply {
                        setDataAndType(contentUri, "*/*")
                        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }

                    try {
                        context.startActivity(fallbackIntent)
                        Log.d("WebAppInterface", "Fallback startActivity called successfully with */* MIME type")
                        return
                    } catch (e2: Exception) {
                        Log.e("WebAppInterface", "Fallback also failed", e2)
                    }
                }

                // Show specific error if no app found
                if (e is android.content.ActivityNotFoundException) {
                    <EMAIL> {
                        Toast.makeText(this@MainActivity, "لا يوجد تطبيق مناسب لفتح هذا الملف. تأكد من تثبيت ماين كرافت.", Toast.LENGTH_LONG).show()
                    }
                } else {
                    <EMAIL> {
                        Toast.makeText(this@MainActivity, "فشل في فتح الملف. حاول مرة أخرى.", Toast.LENGTH_LONG).show()
                    }
                }
            }
        } // End of openDownloadedMod (Uri helper)
        // --- End openDownloadedMod ---


        // Helper to generate filename, ensuring .mcaddon extension (kept as is)
        // Make this public so MainActivity can call it
        fun generateFilenameFromUrl(fallbackName: String, url: String): String? {
            var originalFileName: String? = null
            var fileExtension: String = ".mcaddon" // Default extension

            try {
                val decodedUrl = URLDecoder.decode(url, "UTF-8")
                val pathPart = decodedUrl.substringBefore("?")
                val lastSlashIndex = pathPart.lastIndexOf('/')
                if (lastSlashIndex != -1 && lastSlashIndex < pathPart.length - 1) {
                    originalFileName = pathPart.substring(lastSlashIndex + 1)
                    Log.d("WebAppInterface", "Extracted original filename from URL: $originalFileName")

                    // Check for .mcpack, .mcaddon, or .zip extension
                    if (originalFileName.endsWith(".mcpack", ignoreCase = true)) {
                        fileExtension = ".mcpack"
                    } else if (originalFileName.endsWith(".mcaddon", ignoreCase = true)) {
                        fileExtension = ".mcaddon"
                    } else if (originalFileName.endsWith(".zip", ignoreCase = true)) {
                        // Para archivos ZIP, mantenemos la extensión .zip para evitar confusiones
                        // pero asignamos el tipo MIME adecuado al abrir el archivo
                        fileExtension = ".zip"
                        Log.d("WebAppInterface", "ZIP file detected, keeping .zip extension")
                    }
                    // If none of the above, it will use the default .mcaddon
                }
            } catch (e: Exception) {
                Log.e("WebAppInterface", "Error decoding/parsing URL for filename: $url", e)
                // Fallback to default extension if URL parsing fails
            }

            var baseName: String?
            if (originalFileName != null) {
                val dotIndex = originalFileName.lastIndexOf('.')
                baseName = if (dotIndex > 0) originalFileName.substring(0, dotIndex) else originalFileName
            } else {
                baseName = fallbackName
                Log.w("WebAppInterface", "Using fallbackName for base: $fallbackName")
            }

            if (baseName.isNullOrBlank()) { // Ensure baseName is not blank even if originalFileName was just an extension
                baseName = fallbackName
                Log.w("WebAppInterface", "Base name was blank, using fallbackName: $fallbackName")
            }

            // Sanitize the base name (remove invalid characters) and limit length
            val sanitizedBaseName = baseName.replace(Regex("[^a-zA-Z0-9_.-]+"), "_").take(60)

            // Construct the final filename with the determined (or default) extension
            val finalFilename = "${sanitizedBaseName}${fileExtension}"

            Log.d("WebAppInterface", "Generated final filename: $finalFilename")
            return finalFilename.takeLast(100) // Limit total length just in case
        }

    } // End of WebAppInterface

         // REMOVED: checkStoragePermission function
         // REMOVED: onRequestPermissionsResult function

         // --- Helper to clear pending info (callable from MainActivity & WebAppInterface) ---
         private fun clearPendingDownloadInfo() {
             // Use this@MainActivity to refer to MainActivity's members
             <EMAIL> = null
             <EMAIL> = null
             <EMAIL> = null
             Log.d("MainActivity", "Cleared pending download info.")
         }

         // Move onBackPressed back inside MainActivity class
         @Deprecated("Deprecated in Java")
         override fun onBackPressed() {
             Log.d("MainActivity", "Back button pressed - checking for open modals")

             // Check if a mod details modal is open in the WebView with improved detection
             webView.evaluateJavascript(
                 "(function() { " +
                 "    try { " +
                 "        // Check main modal" +
                 "        var modal = document.getElementById('modal'); " +
                 "        if(modal && modal.style && (modal.style.display === 'flex' || modal.style.display === 'block' || window.getComputedStyle(modal).display !== 'none')) { " +
                 "            return 'modal'; " +
                 "        } " +
                 "        // Check for creator info modal" +
                 "        var creatorInfoModal = document.querySelector('.creator-info-modal'); " +
                 "        if(creatorInfoModal && creatorInfoModal.style && (creatorInfoModal.style.display === 'flex' || creatorInfoModal.style.display === 'block' || window.getComputedStyle(creatorInfoModal).display !== 'none')) { " +
                 "            return 'creator'; " +
                 "        } " +
                 "        // Check for custom dialog" +
                 "        var customDialog = document.querySelector('.custom-dialog-overlay'); " +
                 "        if(customDialog && customDialog.style && customDialog.style.display !== 'none' && window.getComputedStyle(customDialog).display !== 'none') { " +
                 "            return 'dialog'; " +
                 "        } " +
                 "        // Check for language modal" +
                 "        var languageModal = document.getElementById('language-selection-modal'); " +
                 "        if(languageModal && languageModal.style && window.getComputedStyle(languageModal).display !== 'none') { " +
                 "            return 'language'; " +
                 "        } " +
                 "        // Check for subscription modal" +
                 "        var subscriptionModal = document.querySelector('.subscription-modal, .premium-modal'); " +
                 "        if(subscriptionModal && subscriptionModal.style && window.getComputedStyle(subscriptionModal).display !== 'none') { " +
                 "            return 'subscription'; " +
                 "        } " +
                 "        // Check for any other visible modal or overlay" +
                 "        var anyModal = document.querySelector('[id*=\"modal\"]:not([style*=\"display: none\"]), [class*=\"modal\"]:not([style*=\"display: none\"]), [class*=\"overlay\"]:not([style*=\"display: none\"])'); " +
                 "        if(anyModal && window.getComputedStyle(anyModal).display !== 'none') { " +
                 "            return 'other'; " +
                 "        } " +
                 "        return 'none'; " +
                 "    } catch(e) { " +
                 "        console.error('Error checking modals:', e); " +
                 "        return 'error'; " +
                 "    } " +
                 "})();",
                 { result ->
                     Log.d("MainActivity", "Back button pressed, modal check result: $result")
                     when {
                         result?.contains("modal") == true -> {
                             // If main mod details modal is open, close it
                             webView.evaluateJavascript("if(typeof closeModal === 'function') { closeModal(); }", null)
                             Log.d("MainActivity", "Main modal closed via back button")
                         }
                         result?.contains("creator") == true -> {
                             // If creator info modal is open, close it
                             webView.evaluateJavascript(
                                 "var creatorModal = document.querySelector('.creator-info-modal'); " +
                                 "if(creatorModal) { creatorModal.style.display = 'none'; }",
                                 null
                             )
                             Log.d("MainActivity", "Creator info modal closed via back button")
                         }
                         result?.contains("dialog") == true -> {
                             // If custom dialog is open, close it
                             webView.evaluateJavascript(
                                 "var customDialog = document.querySelector('.custom-dialog-overlay'); " +
                                 "if(customDialog) { customDialog.style.display = 'none'; }",
                                 null
                             )
                             Log.d("MainActivity", "Custom dialog closed via back button")
                         }
                         result?.contains("language") == true -> {
                             // If language selection modal is open, close it
                             webView.evaluateJavascript(
                                 "var languageModal = document.getElementById('language-selection-modal'); " +
                                 "if(languageModal) { languageModal.remove(); }",
                                 null
                             )
                             Log.d("MainActivity", "Language selection modal closed via back button")
                         }
                         result?.contains("subscription") == true -> {
                             // If subscription modal is open, close it
                             webView.evaluateJavascript(
                                 "var subscriptionModal = document.querySelector('.subscription-modal, .premium-modal'); " +
                                 "if(subscriptionModal) { subscriptionModal.style.display = 'none'; }",
                                 null
                             )
                             Log.d("MainActivity", "Subscription modal closed via back button")
                         }
                         result?.contains("other") == true -> {
                             // If any other modal is open, try to close it
                             webView.evaluateJavascript(
                                 "var anyModal = document.querySelector('[id*=\"modal\"]:not([style*=\"display: none\"]), [class*=\"modal\"]:not([style*=\"display: none\"]), [class*=\"overlay\"]:not([style*=\"display: none\"])'); " +
                                 "if(anyModal) { anyModal.style.display = 'none'; }",
                                 null
                             )
                             Log.d("MainActivity", "Other modal closed via back button")
                         }
                         result?.contains("error") == true -> {
                             // If there was an error checking modals, try generic close
                             webView.evaluateJavascript(
                                 "if(typeof closeModal === 'function') { closeModal(); } " +
                                 "else { " +
                                 "    var modals = document.querySelectorAll('.modal, [id*=\"modal\"], [class*=\"overlay\"]'); " +
                                 "    modals.forEach(function(modal) { if(modal.style) modal.style.display = 'none'; }); " +
                                 "}",
                                 null
                             )
                             Log.d("MainActivity", "Generic modal close attempted due to error")
                         }
                         else -> {
                             // If no modal is open, handle normal back navigation
                             Log.d("MainActivity", "No modal detected, handling normal back navigation")
                             if (webView.canGoBack()) {
                                 Log.d("MainActivity", "WebView can go back, navigating back")
                                 webView.goBack()
                             } else {
                                 Log.d("MainActivity", "WebView cannot go back, closing app")
                                 // Consider using androidx.activity.OnBackPressedDispatcher for modern handling
                                 super.onBackPressed() // Using suppressLint or the modern dispatcher is better
                             }
                         }
                     }
                 }
             )

             // إضافة تأخير قصير للتأكد من تنفيذ JavaScript قبل المتابعة
             // هذا يساعد في منع إغلاق التطبيق عند وجود نوافذ مفتوحة
         }


         // وظيفة مساعدة لإغلاق جميع النوافذ المفتوحة
         private fun closeAllModals() {
             Log.d("MainActivity", "Attempting to close all modals")
             webView.evaluateJavascript(
                 "(function() { " +
                 "    try { " +
                 "        // إغلاق النافذة الرئيسية للمود" +
                 "        if(typeof closeModal === 'function') { closeModal(); } " +
                 "        // إغلاق جميع النوافذ المرئية" +
                 "        var modals = document.querySelectorAll('.modal, [id*=\"modal\"], [class*=\"overlay\"], .creator-info-modal, .custom-dialog-overlay'); " +
                 "        modals.forEach(function(modal) { " +
                 "            if(modal && modal.style) { " +
                 "                modal.style.display = 'none'; " +
                 "                modal.style.visibility = 'hidden'; " +
                 "            } " +
                 "        }); " +
                 "        // إزالة النوافذ المؤقتة" +
                 "        var tempModals = document.querySelectorAll('#language-selection-modal'); " +
                 "        tempModals.forEach(function(modal) { " +
                 "            if(modal) modal.remove(); " +
                 "        }); " +
                 "        console.log('All modals closed successfully'); " +
                 "        return 'success'; " +
                 "    } catch(e) { " +
                 "        console.error('Error closing modals:', e); " +
                 "        return 'error'; " +
                 "    } " +
                 "})();",
                 { result ->
                     Log.d("MainActivity", "Close all modals result: $result")
                 }
             )
         }

         // Re-apply immersive mode when the window gains focus
         override fun onWindowFocusChanged(hasFocus: Boolean) {
             super.onWindowFocusChanged(hasFocus)
             if (hasFocus) {
                 hideSystemUI()
             }
         }

         // Function to clean up old downloads
         private fun cleanupOldDownloads() {
             Log.d("MainActivity", "Running automatic cleanup of old downloads")

             // Check if auto-delete is enabled (default to true)
             val isAutoDeleteEnabled = sharedPreferences.getBoolean(AUTO_DELETE_ENABLED_KEY, true)
             if (!isAutoDeleteEnabled) {
                 Log.d("MainActivity", "Auto-delete feature is disabled, skipping cleanup")
                 // Schedule next check anyway
                 scheduleNextCleanup()
                 return
             }

             try {
                 // Get the download timestamps from SharedPreferences
                 val timestampsJson = sharedPreferences.getString(MOD_DOWNLOAD_TIMESTAMPS_KEY, "{}") ?: "{}"
                 val timestamps = JSONObject(timestampsJson)
                 val currentTime = System.currentTimeMillis()
                 val keysToRemove = mutableListOf<String>()

                 // Identify old downloads to remove
                 val keys = timestamps.keys()
                 while (keys.hasNext()) {
                     val modId = keys.next()
                     val downloadTime = timestamps.optLong(modId, 0)

                     // Check if the download is older than AUTO_DELETE_DELAY_MS
                     if (currentTime - downloadTime > AUTO_DELETE_DELAY_MS) {
                         val ageMinutes = (currentTime - downloadTime) / 1000 / 60
                         val ageHours = ageMinutes / 60
                         val remainingMinutes = ageMinutes % 60
                         Log.d("MainActivity", "Found old download to remove: $modId (age: $ageHours hours and $remainingMinutes minutes)")
                         keysToRemove.add(modId)

                         // Get the file path from SharedPreferences
                         val pendingJsonString = sharedPreferences.getString(PENDING_DOWNLOADS_KEY, "{}") ?: "{}"
                         val pendingDownloads = JSONObject(pendingJsonString)
                         val filePath = pendingDownloads.optString(modId)

                         if (filePath.isNotEmpty()) {
                             // Delete the file
                             val file = File(filePath)
                             if (file.exists()) {
                                 val deleted = file.delete()
                                 Log.d("MainActivity", "Deleted file for mod $modId: $filePath (success: $deleted)")

                                 // If deletion was successful, remove from pending downloads
                                 if (deleted) {
                                     pendingDownloads.remove(modId)
                                     sharedPreferences.edit().putString(PENDING_DOWNLOADS_KEY, pendingDownloads.toString()).apply()
                                 }
                             } else {
                                 Log.d("MainActivity", "File for mod $modId does not exist: $filePath")
                             }
                         }
                     }
                 }

                 // Remove the timestamps for deleted files
                 for (modId in keysToRemove) {
                     timestamps.remove(modId)
                 }

                 // Save the updated timestamps
                 sharedPreferences.edit().putString(MOD_DOWNLOAD_TIMESTAMPS_KEY, timestamps.toString()).apply()

                 if (keysToRemove.isNotEmpty()) {
                     Log.d("MainActivity", "Cleaned up ${keysToRemove.size} old downloads")

                     // Show notification to user about deleted files
                     showAutoDeleteNotification(keysToRemove.size)
                 } else {
                     Log.d("MainActivity", "No old downloads to clean up")
                 }
             } catch (e: Exception) {
                 Log.e("MainActivity", "Error during cleanup of old downloads", e)
             }

             // Schedule next cleanup
             scheduleNextCleanup()
         }

         // Schedule the next cleanup
         private fun scheduleNextCleanup() {
             // Remove any pending cleanup tasks
             cleanupHandler.removeCallbacks(cleanupRunnable)
             // Schedule the next cleanup
             cleanupHandler.postDelayed(cleanupRunnable, AUTO_CLEANUP_INTERVAL_MS)
             Log.d("MainActivity", "Scheduled next cleanup in ${AUTO_CLEANUP_INTERVAL_MS / 1000 / 60} minutes")
         }

         // Show notification about auto-deleted files
         private fun showAutoDeleteNotification(filesCount: Int) {
             try {
                 // Create notification channel for Android 8.0+
                 if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                     val channelId = "auto_delete_channel"
                     val channelName = "Auto Delete Notifications"
                     val importance = NotificationManager.IMPORTANCE_DEFAULT
                     val channel = NotificationChannel(channelId, channelName, importance).apply {
                         description = "Notifications about automatically deleted mod files"
                     }

                     val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                     notificationManager.createNotificationChannel(channel)
                 }

                 // Build the notification
                 val notificationBuilder = NotificationCompat.Builder(this, "auto_delete_channel")
                 notificationBuilder.setSmallIcon(R.drawable.ic_notification)
                 notificationBuilder.setContentTitle("تم حذف الملفات تلقائيًا")
                 notificationBuilder.setContentText("تم حذف $filesCount ${if (filesCount == 1) "ملف" else "ملفات"} بعد مرور ساعتين من التنزيل")
                 notificationBuilder.setPriority(NotificationCompat.PRIORITY_DEFAULT)
                 notificationBuilder.setAutoCancel(true)

                 // Show the notification
                 val notificationManager = NotificationManagerCompat.from(this)

                 // Check for notification permission on Android 13+
                 if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                     if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
                         notificationManager.notify(1001, notificationBuilder.build())
                     } else {
                         Log.d("MainActivity", "Cannot show auto-delete notification: No notification permission")
                     }
                 } else {
                     notificationManager.notify(1001, notificationBuilder.build())
                 }
             } catch (e: Exception) {
                 Log.e("MainActivity", "Error showing auto-delete notification", e)
             }
         }
     } // End of MainActivity class
