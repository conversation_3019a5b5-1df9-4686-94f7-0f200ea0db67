<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>لوحة الإدارة - Modetaris</title>
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Additional styles if needed */
    </style>
</head>
<body>
    <div class="header">
        <h1>لوحة الإدارة - Modetaris</h1>
    </div>

    <div class="admin-container">
        <div class="admin-section">
            <h2>إدارة البانرات الإعلانية</h2>

            <div class="admin-form">
                <div class="form-group">
                    <label for="banner-title">عنوان البانر</label>
                    <input type="text" id="banner-title" placeholder="أدخل عنوان البانر">
                </div>

                <div class="form-group">
                    <label for="banner-description">وصف البانر</label>
                    <textarea id="banner-description" rows="3" placeholder="أدخل وصف البانر"></textarea>
                </div>

                <div class="form-group">
                    <label for="banner-image-url">رابط صورة البانر</label>
                    <input type="text" id="banner-image-url" placeholder="أدخل رابط صورة البانر">
                </div>

                <div class="form-group">
                    <label for="banner-image-file">أو تحميل صورة من جهازك</label>
                    <input type="file" id="banner-image-file" accept="image/*" style="padding: 8px; background-color: #333;">
                    <div style="margin-top: 5px; font-size: 0.8rem; color: #aaa;">سيتم تحويل الصورة إلى رابط URL تلقائيًا</div>
                </div>

                <div class="preview-container">
                    <img id="preview-image" class="preview-image" src="" alt="معاينة البانر" style="display: none;">
                    <span id="preview-placeholder">سيتم عرض معاينة الصورة هنا</span>
                </div>

                <div class="form-group">
                    <label for="banner-target-url">رابط الوجهة</label>
                    <input type="text" id="banner-target-url" placeholder="أدخل الرابط الذي سيتم توجيه المستخدم إليه">
                </div>

                <!-- New field for YouTube Channel ID -->
                <div class="form-group" id="youtube-channel-id-group" style="display: none;">
                    <label for="youtube-channel-id">معرف قناة يوتيوب (اختياري)</label>
                    <input type="text" id="youtube-channel-id" placeholder="أدخل معرف قناة يوتيوب (مثال: UC-xxxx)">
                    <div style="margin-top: 5px; font-size: 0.8rem; color: #aaa;">
                        يمكنك العثور على معرف القناة في رابط القناة بعد "/channel/" أو "/c/" أو "/user/".
                        مثال: <code>www.youtube.com/channel/UC-xxxx</code>
                    </div>
                </div>

                <div class="form-group">
                    <label for="banner-display-order">ترتيب العرض</label>
                    <input type="number" id="banner-display-order" min="1" value="1">
                </div>

                <div class="form-group">
                    <label for="banner-campaign-id">حملة الاشتراك المجاني</label>
                    <select id="banner-campaign-id">
                        <option value="">اختر حملة</option>
                    </select>
                </div>

                <div class="form-group checkbox-group">
                    <input type="checkbox" id="banner-is-active" checked>
                    <label for="banner-is-active">نشط</label>
                </div>

                <input type="hidden" id="banner-id">
                <input type="hidden" id="banner-display-type" value="banner">
                <input type="hidden" id="banner-banner-type" value="subscription">

                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button id="save-banner" class="admin-button">حفظ البانر</button>
                    <button id="preview-banner" class="admin-button" style="background: linear-gradient(45deg, #4CAF50, #2E7D32);">معاينة البانر</button>
                    <button id="reset-form" class="admin-button" style="background: #555;">إعادة تعيين</button>
                </div>
            </div>

            <div class="banner-list" id="banner-list">
                <!-- Banner items will be loaded here -->
                <div class="loading-indicator">
                    <div class="loading-spinner"></div>
                    جاري تحميل البانرات...
                </div>
            </div>
        </div>

        <a href="../index.html" class="back-to-app">العودة إلى التطبيق</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../supabase-manager.js"></script> <!-- Load supabase-manager.js first -->
    <script src="../smart-verification-system.js"></script> <!-- Load smart-verification-system.js -->
    <script src="admin.js"></script>
    <script src="unified-admin.js"></script> <!-- Load unified-admin.js after supabase-manager.js -->
</body>
</html>
