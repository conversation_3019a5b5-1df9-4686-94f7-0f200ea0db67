// ========================================
// إدارة المهام المحسنة - Enhanced Tasks Admin
// ========================================

// Supabase configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Global variables
let campaignsData = [];
let tasksData = [];
let verificationLogs = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced Tasks Admin Panel loaded');
    loadDashboardData();
});

// ========================================
// تحميل بيانات لوحة التحكم
// ========================================
async function loadDashboardData() {
    try {
        await Promise.all([
            loadCampaignsWithTasks(),
            loadVerificationStats(),
            updateDashboardStats()
        ]);
    } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        showError('حدث خطأ أثناء تحميل البيانات');
    }
}

// ========================================
// تحميل الحملات مع المهام
// ========================================
async function loadCampaignsWithTasks() {
    try {
        showLoading('campaignsContainer');

        // تحميل الحملات النشطة
        const { data: campaigns, error: campaignsError } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false });

        if (campaignsError) {
            throw campaignsError;
        }

        campaignsData = campaigns || [];

        if (campaignsData.length === 0) {
            showEmptyState('campaignsContainer');
            return;
        }

        // تحميل المهام لكل حملة
        const campaignIds = campaignsData.map(c => c.id);
        const { data: tasks, error: tasksError } = await supabaseClient
            .from('campaign_tasks')
            .select(`
                id,
                campaign_id,
                task_type,
                title_ar,
                title_en,
                description_ar,
                description_en,
                target_url,
                verification_method,
                display_order,
                is_required,
                task_types (
                    display_name_ar,
                    display_name_en,
                    icon
                )
            `)
            .in('campaign_id', campaignIds)
            .order('display_order');

        if (tasksError) {
            throw tasksError;
        }

        tasksData = tasks || [];

        // تحميل إحصائيات المهام
        await loadTasksStats();

        // عرض البيانات
        displayCampaignsWithTasks();

    } catch (error) {
        console.error('خطأ في تحميل الحملات والمهام:', error);
        showError('حدث خطأ أثناء تحميل الحملات والمهام: ' + error.message);
    }
}

// ========================================
// تحميل إحصائيات المهام
// ========================================
async function loadTasksStats() {
    try {
        const taskIds = tasksData.map(t => t.id);
        
        if (taskIds.length === 0) return;

        // تحميل تقدم المهام
        const { data: progress, error } = await supabaseClient
            .from('user_task_progress')
            .select('task_id, status')
            .in('task_id', taskIds);

        if (error) {
            console.error('خطأ في تحميل إحصائيات المهام:', error);
            return;
        }

        // تجميع الإحصائيات لكل مهمة
        tasksData.forEach(task => {
            const taskProgress = progress.filter(p => p.task_id === task.id);
            
            task.stats = {
                total_attempts: taskProgress.length,
                verified_count: taskProgress.filter(p => p.status === 'verified').length,
                failed_count: taskProgress.filter(p => p.status === 'failed').length,
                pending_count: taskProgress.filter(p => p.status === 'pending').length,
                avg_score: 0 // Removed calculation as verification_score column is missing
            };
        });

    } catch (error) {
        console.error('خطأ في تحميل إحصائيات المهام:', error);
    }
}

// ========================================
// عرض الحملات مع المهام
// ========================================
function displayCampaignsWithTasks() {
    const container = document.getElementById('campaignsContainer');
    
    const campaignsHTML = campaignsData.map(campaign => {
        const campaignTasks = tasksData.filter(t => t.campaign_id === campaign.id);
        
        const tasksHTML = campaignTasks.map(task => {
            const stats = task.stats || {};
            const taskType = task.task_types || {};
            const verificationMethod = task.verification_method || 'manual';
            
            // تحديد لون المنصة
            let platformClass = '';
            if (task.task_type === 'youtube_subscribe') platformClass = 'platform-youtube';
            else if (task.task_type === 'telegram_subscribe') platformClass = 'platform-telegram';
            else if (task.task_type === 'discord_join') platformClass = 'platform-discord';

            return `
                <div class="task-card">
                    <div class="task-header">
                        <i class="${taskType.icon || 'fas fa-tasks'} task-icon ${platformClass}"></i>
                        <div class="task-title">${task.title_ar}</div>
                        <div class="task-type">${task.task_type}</div>
                    </div>
                    
                    <div class="task-details">
                        ${task.description_ar || 'لا يوجد وصف'}
                        <br>
                        <strong>الرابط:</strong> ${task.target_url || 'غير محدد'}
            <br>
            <span class="verification-badge verification-${verificationMethod}">
                            ${verificationMethod === 'smart' ? '🤖 تحقق ذكي' : '👤 تحقق يدوي'}
                        </span>
                    </div>

                    <div class="task-stats">
                        <div class="task-stat">
                            <div class="task-stat-number">${stats.total_attempts || 0}</div>
                            <div class="task-stat-label">محاولات</div>
                        </div>
                        <div class="task-stat">
                            <div class="task-stat-number">${stats.verified_count || 0}</div>
                            <div class="task-stat-label">متحقق</div>
                        </div>
                        <div class="task-stat">
                            <div class="task-stat-number">${stats.failed_count || 0}</div>
                            <div class="task-stat-label">فاشل</div>
                        </div>
                        </div>
                    </div>

                    <div class="task-actions">
                        <button class="btn btn-primary" onclick="editTask('${task.id}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-secondary" onclick="viewTaskLogs('${task.id}')">
                            <i class="fas fa-chart-line"></i> السجلات
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="campaign-card">
                <div class="campaign-header">
                    <div class="campaign-title">${campaign.title_ar}</div>
                    <div class="campaign-status status-active">نشطة</div>
                </div>
                
                <div style="color: #ccc; margin-bottom: 20px; line-height: 1.5;">
                    ${campaign.description_ar}
                </div>

                <div class="tasks-grid">
                    ${tasksHTML || '<div class="empty-state"><p>لا توجد مهام لهذه الحملة</p></div>'}
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = campaignsHTML;
}

// ========================================
// تحميل إحصائيات التحقق
// ========================================
async function loadVerificationStats() {
    try {
        // استخدام جدول user_task_progress بدلاً من verification_logs
        // لأن verification_logs غير موجود وقد يكون user_task_progress هو المقصود
        const { data: logs, error } = await supabaseClient
            .from('user_task_progress')
            .select('status, created_at, task_id') // تحديد الأعمدة المطلوبة فقط
            .or('status.eq.verified,status.eq.failed') // جلب السجلات المتحققة أو الفاشلة فقط
            .order('created_at', { ascending: false })
            .limit(1000);

        if (error) {
            console.error('خطأ في تحميل سجلات التحقق:', error);
            return;
        }

        // تصفية السجلات لتشمل فقط تلك التي تم التحقق منها أو فشلت
        verificationLogs = (logs || []).filter(log => log.status === 'verified' || log.status === 'failed');

    } catch (error) {
        console.error('خطأ في تحميل إحصائيات التحقق:', error);
    }
}

// ========================================
// تحديث إحصائيات لوحة التحكم
// ========================================
async function updateDashboardStats() {
    try {
        // إحصائيات الحملات
        const activeCampaigns = campaignsData.length;
        document.getElementById('totalCampaigns').textContent = activeCampaigns;

        // إحصائيات المهام
        const totalTasks = tasksData.length;
        document.getElementById('totalTasks').textContent = totalTasks;

        // المهام المتحققة
        const verifiedTasks = tasksData.reduce((sum, task) => 
            sum + (task.stats?.verified_count || 0), 0);
        document.getElementById('verifiedTasks').textContent = verifiedTasks;

        // معدل النجاح
        const totalAttempts = tasksData.reduce((sum, task) => 
            sum + (task.stats?.total_attempts || 0), 0);
        const successRate = totalAttempts > 0 
            ? Math.round((verifiedTasks / totalAttempts) * 100)
            : 0;
        document.getElementById('successRate').textContent = successRate + '%';

    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}

// ========================================
// دوال التفاعل
// ========================================

// تعديل مهمة
function editTask(taskId) {
    const task = tasksData.find(t => t.id === taskId);
    if (!task) return;

    alert(`تعديل المهمة: ${task.title_ar}\nهذه الميزة قيد التطوير`);
}

// عرض سجلات المهمة
async function viewTaskLogs(taskId) {
    try {
        // استخدام جدول user_task_progress بدلاً من verification_logs
        const { data: logs, error } = await supabaseClient
            .from('user_task_progress')
            .select('status, created_at, verification_data') // تحديد الأعمدة المطلوبة
            .eq('task_id', taskId)
            .order('created_at', { ascending: false })
            .limit(50);

        if (error) {
            throw error;
        }

        const task = tasksData.find(t => t.id === taskId);
        const taskName = task ? task.title_ar : 'مهمة غير معروفة';

        let logsText = `سجلات المهمة: ${taskName}\n\n`;
        
        if (logs && logs.length > 0) {
            logs.forEach(log => {
                const date = new Date(log.created_at).toLocaleString('ar-SA');
                let statusEmoji = '';
                let statusText = '';

                if (log.status === 'verified') {
                    statusEmoji = '✅';
                    statusText = 'متحقق';
                } else if (log.status === 'failed') {
                    statusEmoji = '❌';
                    statusText = 'فاشل';
                } else if (log.status === 'pending') {
                    statusEmoji = '⏳';
                    statusText = 'معلق';
                } else {
                    statusEmoji = '❓';
                    statusText = log.status;
                }

                logsText += `${date} - ${statusEmoji} ${statusText}\n`;
                if (log.verification_data) {
                    logsText += `   بيانات التحقق: ${JSON.stringify(log.verification_data, null, 2)}\n`;
                }
                logsText += '\n';
            });
        } else {
            logsText += 'لا توجد سجلات متاحة';
        }

        alert(logsText);

    } catch (error) {
        console.error('خطأ في تحميل سجلات المهمة:', error);
        alert('حدث خطأ أثناء تحميل السجلات');
    }
}


// تحديث البيانات
async function refreshData() {
    showMessage('جاري تحديث البيانات...', 'info');
    await loadDashboardData();
    showMessage('تم تحديث البيانات بنجاح', 'success');
}

// ========================================
// دوال مساعدة
// ========================================

function showLoading(containerId) {
    document.getElementById(containerId).innerHTML = `
        <div class="loading">
            <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
        </div>
    `;
}

function showError(message) {
    document.getElementById('campaignsContainer').innerHTML = `
        <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: #ef4444;"></i>
            <h3 style="color: #ef4444; margin: 20px 0;">خطأ</h3>
            <p>${message}</p>
            <button class="btn btn-primary" onclick="refreshData()" style="margin-top: 20px;">
                <i class="fas fa-refresh"></i> إعادة المحاولة
            </button>
        </div>
    `;
}

function showEmptyState(containerId) {
    document.getElementById(containerId).innerHTML = `
        <div class="empty-state">
            <i class="fas fa-tasks"></i>
            <h3>لا توجد حملات نشطة</h3>
            <p>لم يتم العثور على حملات نشطة حالياً</p>
            <a href="subscription_admin.html" class="btn btn-primary" style="margin-top: 20px;">
                <i class="fas fa-plus"></i> إنشاء حملة جديدة
            </a>
        </div>
    `;
}

function showMessage(message, type = 'info') {
    // Remove existing messages
    document.querySelectorAll('.app-message').forEach(msg => msg.remove());

    const messageDiv = document.createElement('div');
    messageDiv.className = 'app-message';
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? 'linear-gradient(45deg, #22c55e, #16a34a)' :
                     type === 'error' ? 'linear-gradient(45deg, #ef4444, #dc2626)' :
                     type === 'warning' ? 'linear-gradient(45deg, #f59e0b, #d97706)' :
                     'linear-gradient(45deg, #3b82f6, #2563eb)'};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-weight: bold;
        z-index: 10002;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        animation: slideInDown 0.3s ease-out;
    `;

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 5000);
}
