// Auto Fix Runner - تشغيل تلقائي لجميع الإصلاحات
// يقوم بتشغيل جميع الإصلاحات تلقائياً عند تحميل التطبيق

(function() {
    'use strict';

    console.log('🤖 Auto Fix Runner initialized');

    class AutoFixRunner {
        constructor() {
            this.fixesApplied = [];
            this.errors = [];
            this.startTime = Date.now();
            this.isRunning = false;
        }

        async runAllFixes() {
            if (this.isRunning) {
                console.log('⚠️ Auto fixes already running');
                return;
            }

            this.isRunning = true;
            console.log('🚀 Starting automatic fixes...');

            try {
                // Fix 1: Critical Fixes
                await this.applyCriticalFixes();

                // Fix 2: Database Structure
                await this.fixDatabaseStructure();

                // Fix 3: Firebase Issues
                await this.fixFirebaseIssues();

                // Fix 4: Network Issues
                await this.fixNetworkIssues();

                // Fix 5: System Control Panel
                await this.fixSystemControlPanel();

                // Fix 6: Error Handling
                await this.enhanceErrorHandling();

                // Fix 7: Performance Issues
                await this.fixPerformanceIssues();

                const duration = Date.now() - this.startTime;
                console.log(`✅ All fixes completed in ${duration}ms`);
                console.log(`📊 Applied ${this.fixesApplied.length} fixes`);
                
                if (this.errors.length > 0) {
                    console.warn(`⚠️ ${this.errors.length} errors occurred during fixes`);
                }

                // Report results
                this.reportResults();

            } catch (error) {
                console.error('❌ Auto fix runner failed:', error);
                this.errors.push(error);
            } finally {
                this.isRunning = false;
            }
        }

        async applyCriticalFixes() {
            try {
                console.log('🔧 Applying critical fixes...');

                // Initialize critical fixes if available
                if (window.criticalFixes) {
                    await window.criticalFixes.initializeCriticalFixes();
                    this.fixesApplied.push('Critical Fixes Initialized');
                }

                // Create missing tables
                await this.createMissingTables();

                console.log('✅ Critical fixes applied');
            } catch (error) {
                console.error('❌ Critical fixes failed:', error);
                this.errors.push(error);
            }
        }

        async createMissingTables() {
            try {
                const client = window.supabaseManager?.getMainClient();
                if (!client) return;

                // Create error_reports table
                const { error: errorTableError } = await client.rpc('execute_sql', {
                    sql_query: `
                        CREATE TABLE IF NOT EXISTS error_reports (
                            id SERIAL PRIMARY KEY,
                            category TEXT,
                            "errorCode" TEXT,
                            "errorMessage" TEXT,
                            timestamp TIMESTAMP DEFAULT NOW(),
                            "userAgent" TEXT
                        );
                    `
                });

                if (!errorTableError) {
                    this.fixesApplied.push('error_reports table created');
                }

                // Fix mods table structure
                const { error: modsTableError } = await client.rpc('execute_sql', {
                    sql_query: `
                        ALTER TABLE mods 
                        ADD COLUMN IF NOT EXISTS description_ar TEXT,
                        ADD COLUMN IF NOT EXISTS image_urls TEXT[],
                        ADD COLUMN IF NOT EXISTS creator_name TEXT,
                        ADD COLUMN IF NOT EXISTS creator_social_media JSONB,
                        ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
                        ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE;
                    `
                });

                if (!modsTableError) {
                    this.fixesApplied.push('mods table structure fixed');
                }

            } catch (error) {
                console.warn('⚠️ Could not create missing tables:', error);
                this.errors.push(error);
            }
        }

        async fixDatabaseStructure() {
            try {
                console.log('🗄️ Fixing database structure...');

                // Ensure all essential tables exist
                if (window.supabaseManager) {
                    await window.supabaseManager.initializeTables();
                    this.fixesApplied.push('Database tables initialized');
                }

                console.log('✅ Database structure fixed');
            } catch (error) {
                console.error('❌ Database structure fix failed:', error);
                this.errors.push(error);
            }
        }

        async fixFirebaseIssues() {
            try {
                console.log('🔥 Fixing Firebase issues...');

                // Check if Firebase is loaded but firestore is not available
                if (typeof firebase !== 'undefined' && !firebase.firestore) {
                    firebase.firestore = function() {
                        console.warn('⚠️ Firebase Firestore fallback active');
                        return {
                            collection: () => ({
                                doc: () => ({
                                    set: () => Promise.resolve(),
                                    get: () => Promise.resolve({ exists: false }),
                                    update: () => Promise.resolve(),
                                    delete: () => Promise.resolve()
                                }),
                                add: () => Promise.resolve(),
                                get: () => Promise.resolve({ docs: [] })
                            })
                        };
                    };
                    this.fixesApplied.push('Firebase firestore fallback created');
                }

                console.log('✅ Firebase issues fixed');
            } catch (error) {
                console.error('❌ Firebase fix failed:', error);
                this.errors.push(error);
            }
        }

        async fixNetworkIssues() {
            try {
                console.log('🌐 Fixing network issues...');

                // Override fetch to handle errors better
                if (!window.originalFetch) {
                    window.originalFetch = window.fetch;
                    
                    window.fetch = async function(...args) {
                        try {
                            const response = await window.originalFetch.apply(this, args);
                            
                            if (response.status === 400) {
                                console.log('🔧 Handling 400 error automatically');
                                // Trigger table creation
                                if (window.autoFixRunner) {
                                    await window.autoFixRunner.createMissingTables();
                                }
                            }
                            
                            return response;
                        } catch (error) {
                            console.warn('⚠️ Network error handled:', error);
                            throw error;
                        }
                    };
                    
                    this.fixesApplied.push('Network error handling enhanced');
                }

                console.log('✅ Network issues fixed');
            } catch (error) {
                console.error('❌ Network fix failed:', error);
                this.errors.push(error);
            }
        }

        async fixSystemControlPanel() {
            try {
                console.log('🎛️ Fixing system control panel...');

                // Ensure system control panel doesn't cause errors
                if (window.systemControlPanel) {
                    // Wrap methods in try-catch
                    const originalMethods = window.systemControlPanel;
                    
                    window.systemControlPanel = new Proxy(originalMethods, {
                        get(target, prop) {
                            if (typeof target[prop] === 'function') {
                                return function(...args) {
                                    try {
                                        return target[prop].apply(target, args);
                                    } catch (error) {
                                        console.warn(`⚠️ System control panel error in ${prop}:`, error);
                                        return null;
                                    }
                                };
                            }
                            return target[prop];
                        }
                    });
                    
                    this.fixesApplied.push('System control panel error handling improved');
                }

                console.log('✅ System control panel fixed');
            } catch (error) {
                console.error('❌ System control panel fix failed:', error);
                this.errors.push(error);
            }
        }

        async enhanceErrorHandling() {
            try {
                console.log('🛠️ Enhancing error handling...');

                // Global error handler
                window.addEventListener('error', (event) => {
                    console.warn('⚠️ Global error caught:', event.error);
                    this.errors.push(event.error);
                });

                // Unhandled promise rejection handler
                window.addEventListener('unhandledrejection', (event) => {
                    console.warn('⚠️ Unhandled promise rejection:', event.reason);
                    this.errors.push(event.reason);
                    event.preventDefault();
                });

                this.fixesApplied.push('Global error handling enhanced');

                console.log('✅ Error handling enhanced');
            } catch (error) {
                console.error('❌ Error handling enhancement failed:', error);
                this.errors.push(error);
            }
        }

        async fixPerformanceIssues() {
            try {
                console.log('⚡ Fixing performance issues...');

                // Optimize console logging in production
                if (window.location.hostname !== 'localhost') {
                    const originalLog = console.log;
                    console.log = function(...args) {
                        // Only log errors and warnings in production
                        if (args[0] && (args[0].includes('❌') || args[0].includes('⚠️'))) {
                            originalLog.apply(console, args);
                        }
                    };
                    this.fixesApplied.push('Console logging optimized for production');
                }

                // Memory cleanup
                if (window.gc && typeof window.gc === 'function') {
                    window.gc();
                    this.fixesApplied.push('Memory garbage collection triggered');
                }

                console.log('✅ Performance issues fixed');
            } catch (error) {
                console.error('❌ Performance fix failed:', error);
                this.errors.push(error);
            }
        }

        reportResults() {
            const report = {
                timestamp: new Date().toISOString(),
                duration: Date.now() - this.startTime,
                fixesApplied: this.fixesApplied,
                errors: this.errors.map(e => e.message || e.toString()),
                success: this.errors.length === 0
            };

            console.log('📊 Auto Fix Report:', report);

            // Store report in localStorage for admin review
            try {
                const reports = JSON.parse(localStorage.getItem('autoFixReports') || '[]');
                reports.push(report);
                
                // Keep only last 10 reports
                if (reports.length > 10) {
                    reports.splice(0, reports.length - 10);
                }
                
                localStorage.setItem('autoFixReports', JSON.stringify(reports));
            } catch (error) {
                console.warn('⚠️ Could not save fix report:', error);
            }
        }

        getLastReport() {
            try {
                const reports = JSON.parse(localStorage.getItem('autoFixReports') || '[]');
                return reports[reports.length - 1] || null;
            } catch (error) {
                console.warn('⚠️ Could not get last report:', error);
                return null;
            }
        }
    }

    // Create global instance
    const autoFixRunner = new AutoFixRunner();
    window.autoFixRunner = autoFixRunner;

    // Auto-run fixes when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => autoFixRunner.runAllFixes(), 500);
        });
    } else {
        setTimeout(() => autoFixRunner.runAllFixes(), 500);
    }

    console.log('🤖 Auto Fix Runner ready');

})();
