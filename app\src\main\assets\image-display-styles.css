/* تحسينات عرض الصور */

/* تحسين عرض الصور الرئيسية للمودات */
.mod-image {
    width: 100%;
    height: 150px; /* Volver al valor original para la categoría All */
    object-fit: cover;
    object-position: center top; /* Mostrar la parte superior de la imagen */
    border-radius: 8px;
    transition: all 0.3s ease;
    background-color: transparent;
    /* تم إزالة الخلفية المتدرجة لتحسين الأداء */
}

/* Imágenes para categorías que no son All */
.mod-card:not([data-category="All"]) .mod-image {
    height: 250px; /* Aumentado significativamente para mostrar más contenido de la imagen */
}

/* حالة التحميل */
.mod-image.lazy-load {
    opacity: 0.7;
    filter: blur(2px);
}

/* حالة التحميل المكتمل */
.mod-image.loaded {
    opacity: 1;
    filter: none;
}

/* تحسين حاوي الصورة */
.mod-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    background: transparent; /* تم إزالة الخلفية المتدرجة الأرجوانية لتحسين الأداء */
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تأثير hover للصور */
.mod-image:hover {
    transform: scale(1.05);
    box-shadow: none;
}

/* تحسين عرض الصور في الشبكة */
.mods-grid .mod-image {
    height: 180px; /* Volver al valor original para la categoría All */
    object-position: center top; /* Mostrar la parte superior de la imagen */
}

/* Imágenes en la cuadrícula para categorías que no son All */
.mods-grid .mod-card:not([data-category="All"]) .mod-image {
    height: 270px; /* Aumentado significativamente para mostrar más contenido de la imagen */
}

/* تحسين عرض الصور في القوائم الأفقية */
.items-container .mod-image {
    height: 140px;
    width: 100%;
    object-position: center top; /* Mostrar la parte superior de la imagen */
}

/* تحسين عرض الصور في النافذة المنبثقة */
#mainImage {
    max-width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
    border-radius: 10px;
    border: 3px solid #ff6b35;
    background-color: #000;
}

/* تحسين عرض الصور المصغرة */
.thumbnail {
    width: 80px !important;
    height: 80px !important;
    object-fit: cover !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    border: 2px solid #ff6b35 !important;
}

.thumbnail:hover {
    transform: scale(1.1) !important;
    border-color: #ff8c00 !important;
}

.thumbnail.selected {
    border-color: #ff4500 !important;
    box-shadow: none !important;
}

/* تحسين عرض الصور في البحث */
.search-results .mod-image {
    height: 160px; /* Volver al valor original para la categoría All */
    object-position: center top; /* Mostrar la parte superior de la imagen */
}

/* Imágenes en los resultados de búsqueda para categorías que no son All */
.search-results .mod-card:not([data-category="All"]) .mod-image {
    height: 250px; /* Aumentado significativamente para mostrar más contenido de la imagen */
}

/* تحسين الصور الاحتياطية */
.mod-image[src*="placeholder"] {
    background: transparent; /* تم إزالة الخلفية المتدرجة الأرجوانية لتحسين الأداء */
    display: flex;
    align-items: center;
    justify-content: center;
}

.mod-image[src*="placeholder"]::before {
    content: "🎮";
    font-size: 3rem;
    color: white;
    /* text-shadow removed */
}

/* تحسين الأداء */
.mod-image {
    will-change: transform;
    backface-visibility: hidden;
}

/* تحسين عرض الصور على الشاشات الصغيرة */
@media (max-width: 768px) {
    .mod-image {
        height: 120px; /* Volver al valor original para la categoría All */
        object-position: center top; /* Mostrar la parte superior de la imagen */
    }
    
    .mods-grid .mod-image {
        height: 140px; /* Volver al valor original para la categoría All */
        object-position: center top; /* Mostrar la parte superior de la imagen */
    }
    
    .items-container .mod-image {
        height: 110px; /* Volver al valor original para la categoría All */
        object-position: center top; /* Mostrar la parte superior de la imagen */
    }
    
    /* Imágenes para categorías que no son All en pantallas pequeñas */
    .mod-card:not([data-category="All"]) .mod-image {
        height: 200px; /* Aumentado significativamente para mostrar más contenido de la imagen */
    }
    
    .mods-grid .mod-card:not([data-category="All"]) .mod-image {
        height: 220px; /* Aumentado significativamente para mostrar más contenido de la imagen */
    }
    
    .items-container .mod-card:not([data-category="All"]) .mod-image {
        height: 180px; /* Aumentado significativamente para mostrar más contenido de la imagen */
    }
    
    #mainImage {
        max-height: 300px;
    }
    
    .thumbnail {
        width: 60px !important;
        height: 60px !important;
    }
}

/* تحسين عرض الصور على الشاشات الكبيرة */
@media (min-width: 1200px) {
    .mod-image {
        height: 180px; /* Volver al valor original para la categoría All */
        object-position: center top; /* Mostrar la parte superior de la imagen */
    }
    
    .mods-grid .mod-image {
        height: 220px; /* Volver al valor original para la categoría All */
        object-position: center top; /* Mostrar la parte superior de la imagen */
    }
    
    .items-container .mod-image {
        height: 160px; /* Volver al valor original para la categoría All */
        object-position: center top; /* Mostrar la parte superior de la imagen */
    }
    
    /* Imágenes para categorías que no son All en pantallas grandes */
    .mod-card:not([data-category="All"]) .mod-image {
        height: 280px; /* Aumentado significativamente para mostrar más contenido de la imagen */
    }
    
    .mods-grid .mod-card:not([data-category="All"]) .mod-image {
        height: 300px; /* Aumentado significativamente para mostrar más contenido de la imagen */
    }
    
    .items-container .mod-card:not([data-category="All"]) .mod-image {
        height: 250px; /* Aumentado significativamente para mostrar más contenido de la imagen */
    }
}

/* تأثيرات التحميل */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.mod-image.lazy-load {
    background: transparent; /* تم إزالة الخلفية المتدرجة لتحسين الأداء */
    animation: none; /* تم إزالة الأنيميشن لتحسين الأداء */
}

/* إصلاح مشاكل العرض */
.mod-image {
    display: block;
    max-width: 100%;
    height: auto;
    min-height: 150px;
    background-color: rgba(0, 0, 0, 0.05); /* Fondo sutil para mejorar la visibilidad de las imágenes */
}

/* تحسين جودة الصور */
.mod-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* إخفاء الصور المعطلة */
.mod-image[src=""], .mod-image:not([src]) {
    display: none;
}
