// Advanced Settings Management System
// نظام إدارة الإعدادات المتقدم

// Global variables
let systemSettings = {};
let settingsCategories = {};
let settingsHistory = [];
let unsavedChanges = false;

// Initialize advanced settings
document.addEventListener('DOMContentLoaded', function() {
    console.log('Advanced Settings loaded');
    initializeAdvancedSettings();
});

// Initialize advanced settings system
async function initializeAdvancedSettings() {
    try {
        // Load settings categories
        loadSettingsCategories();

        // Load current settings
        await loadSystemSettings();

        // Load settings history
        loadSettingsHistory();

        // Setup settings UI
        setupSettingsUI();

        // Setup auto-save
        setupAutoSave();

        console.log('✅ Advanced settings initialized');
    } catch (error) {
        console.error('Error initializing advanced settings:', error);
    }
}

// Load settings categories
function loadSettingsCategories() {
    settingsCategories = {
        'general': {
            name: 'الإعدادات العامة',
            icon: 'cog',
            description: 'الإعدادات الأساسية للتطبيق',
            settings: [
                {
                    key: 'app_name',
                    name: 'اسم التطبيق',
                    type: 'text',
                    default: 'Mod Etaris',
                    description: 'اسم التطبيق الذي يظهر للمستخدمين'
                },
                {
                    key: 'app_version',
                    name: 'إصدار التطبيق',
                    type: 'text',
                    default: '2.0.0',
                    description: 'رقم إصدار التطبيق الحالي'
                },
                {
                    key: 'maintenance_mode',
                    name: 'وضع الصيانة',
                    type: 'boolean',
                    default: false,
                    description: 'تفعيل وضع الصيانة لإيقاف التطبيق مؤقتاً'
                },
                {
                    key: 'default_language',
                    name: 'اللغة الافتراضية',
                    type: 'select',
                    options: [
                        { value: 'ar', label: 'العربية' },
                        { value: 'en', label: 'English' }
                    ],
                    default: 'ar',
                    description: 'اللغة الافتراضية للمستخدمين الجدد'
                }
            ]
        },
        'content': {
            name: 'إعدادات المحتوى',
            icon: 'folder-open',
            description: 'إعدادات إدارة المودات والمحتوى',
            settings: [
                {
                    key: 'max_mod_size',
                    name: 'الحد الأقصى لحجم المود (MB)',
                    type: 'number',
                    default: 100,
                    min: 1,
                    max: 1000,
                    description: 'الحد الأقصى لحجم ملف المود بالميجابايت'
                },
                {
                    key: 'auto_approve_mods',
                    name: 'الموافقة التلقائية على المودات',
                    type: 'boolean',
                    default: false,
                    description: 'الموافقة تلقائياً على المودات المرفوعة'
                },
                {
                    key: 'featured_mods_limit',
                    name: 'عدد المودات المميزة',
                    type: 'number',
                    default: 10,
                    min: 1,
                    max: 50,
                    description: 'العدد الأقصى للمودات المميزة'
                },
                {
                    key: 'new_mod_duration',
                    name: 'مدة عرض المودات الجديدة (أيام)',
                    type: 'number',
                    default: 7,
                    min: 1,
                    max: 30,
                    description: 'عدد الأيام لعرض علامة "جديد" على المودات'
                }
            ]
        },
        'users': {
            name: 'إعدادات المستخدمين',
            icon: 'users',
            description: 'إعدادات إدارة المستخدمين والحسابات',
            settings: [
                {
                    key: 'max_downloads_per_day',
                    name: 'الحد الأقصى للتحميلات اليومية',
                    type: 'number',
                    default: 50,
                    min: 1,
                    max: 1000,
                    description: 'العدد الأقصى للتحميلات لكل مستخدم يومياً'
                },
                {
                    key: 'user_registration_enabled',
                    name: 'تفعيل تسجيل المستخدمين',
                    type: 'boolean',
                    default: true,
                    description: 'السماح للمستخدمين الجدد بالتسجيل'
                },
                {
                    key: 'auto_ban_threshold',
                    name: 'عتبة الحظر التلقائي',
                    type: 'number',
                    default: 10,
                    min: 1,
                    max: 100,
                    description: 'عدد المخالفات قبل الحظر التلقائي'
                }
            ]
        },
        'notifications': {
            name: 'إعدادات الإشعارات',
            icon: 'bell',
            description: 'إعدادات نظام الإشعارات',
            settings: [
                {
                    key: 'notifications_enabled',
                    name: 'تفعيل الإشعارات',
                    type: 'boolean',
                    default: true,
                    description: 'تفعيل نظام الإشعارات'
                },
                {
                    key: 'max_notifications_per_user',
                    name: 'الحد الأقصى للإشعارات لكل مستخدم',
                    type: 'number',
                    default: 100,
                    min: 10,
                    max: 1000,
                    description: 'العدد الأقصى للإشعارات المحفوظة لكل مستخدم'
                },
                {
                    key: 'notification_retention_days',
                    name: 'مدة الاحتفاظ بالإشعارات (أيام)',
                    type: 'number',
                    default: 30,
                    min: 1,
                    max: 365,
                    description: 'عدد الأيام للاحتفاظ بالإشعارات'
                }
            ]
        },
        'performance': {
            name: 'إعدادات الأداء',
            icon: 'tachometer-alt',
            description: 'إعدادات تحسين الأداء والسرعة',
            settings: [
                {
                    key: 'cache_enabled',
                    name: 'تفعيل التخزين المؤقت',
                    type: 'boolean',
                    default: true,
                    description: 'تفعيل التخزين المؤقت لتحسين الأداء'
                },
                {
                    key: 'cache_duration',
                    name: 'مدة التخزين المؤقت (دقائق)',
                    type: 'number',
                    default: 60,
                    min: 1,
                    max: 1440,
                    description: 'مدة الاحتفاظ بالبيانات في التخزين المؤقت'
                },
                {
                    key: 'image_compression_enabled',
                    name: 'تفعيل ضغط الصور',
                    type: 'boolean',
                    default: true,
                    description: 'ضغط الصور تلقائياً لتوفير المساحة'
                },
                {
                    key: 'image_quality',
                    name: 'جودة الصور',
                    type: 'select',
                    options: [
                        { value: 'low', label: 'منخفضة' },
                        { value: 'medium', label: 'متوسطة' },
                        { value: 'high', label: 'عالية' }
                    ],
                    default: 'medium',
                    description: 'مستوى جودة الصور المضغوطة'
                }
            ]
        },
        'security': {
            name: 'إعدادات الأمان',
            icon: 'shield-alt',
            description: 'إعدادات الأمان والحماية',
            settings: [
                {
                    key: 'two_factor_enabled',
                    name: 'تفعيل المصادقة الثنائية',
                    type: 'boolean',
                    default: false,
                    description: 'تفعيل المصادقة الثنائية للمشرفين'
                },
                {
                    key: 'session_timeout',
                    name: 'انتهاء صلاحية الجلسة (دقائق)',
                    type: 'number',
                    default: 120,
                    min: 15,
                    max: 1440,
                    description: 'مدة انتهاء صلاحية جلسة المستخدم'
                },
                {
                    key: 'max_login_attempts',
                    name: 'الحد الأقصى لمحاولات تسجيل الدخول',
                    type: 'number',
                    default: 5,
                    min: 3,
                    max: 20,
                    description: 'عدد المحاولات المسموحة قبل الحظر المؤقت'
                },
                {
                    key: 'ip_whitelist_enabled',
                    name: 'تفعيل القائمة البيضاء للـ IP',
                    type: 'boolean',
                    default: false,
                    description: 'السماح فقط لعناوين IP المحددة'
                }
            ]
        }
    };
}

// Load system settings
async function loadSystemSettings() {
    try {
        // Load from localStorage (in real app, this would be from database)
        const savedSettings = localStorage.getItem('systemSettings');
        if (savedSettings) {
            systemSettings = JSON.parse(savedSettings);
        } else {
            // Initialize with default values
            systemSettings = getDefaultSettings();
            saveSystemSettings();
        }

        console.log('System settings loaded:', systemSettings);
    } catch (error) {
        console.error('Error loading system settings:', error);
        systemSettings = getDefaultSettings();
    }
}

// Get default settings
function getDefaultSettings() {
    const defaults = {};

    Object.values(settingsCategories).forEach(category => {
        category.settings.forEach(setting => {
            defaults[setting.key] = setting.default;
        });
    });

    return defaults;
}

// Save system settings
function saveSystemSettings() {
    try {
        localStorage.setItem('systemSettings', JSON.stringify(systemSettings));

        // Add to history
        addToSettingsHistory('settings_saved', 'جميع الإعدادات', systemSettings);

        // Reset unsaved changes flag
        unsavedChanges = false;
        updateSaveButton();

        console.log('System settings saved');
    } catch (error) {
        console.error('Error saving system settings:', error);
    }
}

// Load settings history
function loadSettingsHistory() {
    try {
        const savedHistory = localStorage.getItem('settingsHistory');
        if (savedHistory) {
            settingsHistory = JSON.parse(savedHistory);
        }
    } catch (error) {
        console.error('Error loading settings history:', error);
        settingsHistory = [];
    }
}

// Add to settings history
function addToSettingsHistory(action, settingName, value) {
    const historyEntry = {
        id: `history_${Date.now()}`,
        timestamp: new Date().toISOString(),
        action: action,
        settingName: settingName,
        value: value,
        userId: getCurrentUserId()
    };

    settingsHistory.unshift(historyEntry);

    // Keep only last 100 entries
    if (settingsHistory.length > 100) {
        settingsHistory = settingsHistory.slice(0, 100);
    }

    // Save history
    localStorage.setItem('settingsHistory', JSON.stringify(settingsHistory));

    // Update history display
    updateSettingsHistoryDisplay();
}

// Get current user ID
function getCurrentUserId() {
    return localStorage.getItem('currentUserId') || 'admin_001';
}

// Setup settings UI
function setupSettingsUI() {
    // Create settings tabs
    createSettingsTabs();

    // Create settings forms
    createSettingsForms();

    // Setup event listeners
    setupSettingsEventListeners();

    // Update UI with current values
    updateSettingsUI();
}

// Create settings tabs
function createSettingsTabs() {
    const tabsContainer = document.getElementById('settings-tabs');
    if (!tabsContainer) return;

    tabsContainer.innerHTML = Object.entries(settingsCategories).map(([categoryKey, category]) => `
        <button class="settings-tab ${categoryKey === 'general' ? 'active' : ''}"
                data-category="${categoryKey}">
            <i class="fas fa-${category.icon}"></i>
            ${category.name}
        </button>
    `).join('');
}

// Create settings forms
function createSettingsForms() {
    const formsContainer = document.getElementById('settings-forms');
    if (!formsContainer) return;

    formsContainer.innerHTML = Object.entries(settingsCategories).map(([categoryKey, category]) => `
        <div class="settings-form ${categoryKey === 'general' ? 'active' : ''}"
             data-category="${categoryKey}">
            <div class="settings-form-header">
                <h3><i class="fas fa-${category.icon}"></i> ${category.name}</h3>
                <p>${category.description}</p>
            </div>
            <div class="settings-form-body">
                ${category.settings.map(setting => createSettingField(setting)).join('')}
            </div>
        </div>
    `).join('');
}

// Create setting field
function createSettingField(setting) {
    let fieldHTML = '';

    switch (setting.type) {
        case 'text':
            fieldHTML = `
                <input type="text"
                       id="setting-${setting.key}"
                       value="${systemSettings[setting.key] || setting.default}"
                       class="setting-input">
            `;
            break;

        case 'number':
            fieldHTML = `
                <input type="number"
                       id="setting-${setting.key}"
                       value="${systemSettings[setting.key] || setting.default}"
                       min="${setting.min || 0}"
                       max="${setting.max || 999999}"
                       class="setting-input">
            `;
            break;

        case 'boolean':
            fieldHTML = `
                <label class="setting-toggle">
                    <input type="checkbox"
                           id="setting-${setting.key}"
                           ${(systemSettings[setting.key] !== undefined ? systemSettings[setting.key] : setting.default) ? 'checked' : ''}
                           class="setting-input">
                    <span class="toggle-slider"></span>
                </label>
            `;
            break;

        case 'select':
            fieldHTML = `
                <select id="setting-${setting.key}" class="setting-input">
                    ${setting.options.map(option => `
                        <option value="${option.value}"
                                ${(systemSettings[setting.key] || setting.default) === option.value ? 'selected' : ''}>
                            ${option.label}
                        </option>
                    `).join('')}
                </select>
            `;
            break;
    }

    return `
        <div class="setting-field">
            <div class="setting-label">
                <label for="setting-${setting.key}">${setting.name}</label>
                <span class="setting-description">${setting.description}</span>
            </div>
            <div class="setting-control">
                ${fieldHTML}
            </div>
        </div>
    `;
}

// Setup settings event listeners
function setupSettingsEventListeners() {
    // Tab switching
    document.addEventListener('click', (e) => {
        if (e.target.closest('.settings-tab')) {
            const tab = e.target.closest('.settings-tab');
            const category = tab.getAttribute('data-category');
            switchSettingsTab(category);
        }
    });

    // Setting changes
    document.addEventListener('change', (e) => {
        if (e.target.classList.contains('setting-input')) {
            const settingKey = e.target.id.replace('setting-', '');
            let value = e.target.value;

            // Handle different input types
            if (e.target.type === 'checkbox') {
                value = e.target.checked;
            } else if (e.target.type === 'number') {
                value = parseFloat(value);
            }

            // Update setting
            updateSetting(settingKey, value);
        }
    });

    // Save button
    const saveButton = document.getElementById('save-settings-btn');
    if (saveButton) {
        saveButton.addEventListener('click', saveSystemSettings);
    }

    // Reset button
    const resetButton = document.getElementById('reset-settings-btn');
    if (resetButton) {
        resetButton.addEventListener('click', resetToDefaults);
    }

    // Export button
    const exportButton = document.getElementById('export-settings-btn');
    if (exportButton) {
        exportButton.addEventListener('click', exportSettings);
    }

    // Import button
    const importButton = document.getElementById('import-settings-btn');
    if (importButton) {
        importButton.addEventListener('click', () => {
            document.getElementById('import-settings-file').click();
        });
    }

    // Import file input
    const importFileInput = document.getElementById('import-settings-file');
    if (importFileInput) {
        importFileInput.addEventListener('change', importSettings);
    }
}

// Switch settings tab
function switchSettingsTab(category) {
    // Update tab buttons
    document.querySelectorAll('.settings-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-category="${category}"]`).classList.add('active');

    // Update forms
    document.querySelectorAll('.settings-form').forEach(form => {
        form.classList.remove('active');
    });
    document.querySelector(`.settings-form[data-category="${category}"]`).classList.add('active');
}

// Update setting
function updateSetting(key, value) {
    const oldValue = systemSettings[key];
    systemSettings[key] = value;

    // Mark as unsaved
    unsavedChanges = true;
    updateSaveButton();

    // Add to history
    addToSettingsHistory('setting_changed', key, { old: oldValue, new: value });

    // Log audit event
    if (typeof logAuditEvent === 'function') {
        logAuditEvent('setting_change', 'system', 'info', `تم تغيير إعداد ${key} من ${oldValue} إلى ${value}`);
    }

    console.log(`Setting updated: ${key} = ${value}`);
}

// Update settings UI
function updateSettingsUI() {
    Object.keys(systemSettings).forEach(key => {
        const element = document.getElementById(`setting-${key}`);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = systemSettings[key];
            } else {
                element.value = systemSettings[key];
            }
        }
    });
}

// Update save button
function updateSaveButton() {
    const saveButton = document.getElementById('save-settings-btn');
    if (saveButton) {
        saveButton.disabled = !unsavedChanges;
        saveButton.textContent = unsavedChanges ? 'حفظ التغييرات' : 'محفوظ';
    }
}

// Reset to defaults
function resetToDefaults() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        systemSettings = getDefaultSettings();
        updateSettingsUI();
        unsavedChanges = true;
        updateSaveButton();

        // Add to history
        addToSettingsHistory('settings_reset', 'جميع الإعدادات', 'تم إعادة التعيين للقيم الافتراضية');

        console.log('Settings reset to defaults');
    }
}

// Export settings
function exportSettings() {
    const exportData = {
        timestamp: new Date().toISOString(),
        version: '2.0',
        settings: systemSettings,
        categories: Object.keys(settingsCategories)
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `settings-export-${new Date().toISOString().split('T')[0]}.json`;
    a.click();

    // Add to history
    addToSettingsHistory('settings_exported', 'جميع الإعدادات', 'تم تصدير الإعدادات');
}

// Import settings
function importSettings(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importData = JSON.parse(e.target.result);

            if (importData.settings) {
                if (confirm('هل أنت متأكد من استيراد هذه الإعدادات؟ سيتم استبدال الإعدادات الحالية.')) {
                    systemSettings = { ...systemSettings, ...importData.settings };
                    updateSettingsUI();
                    unsavedChanges = true;
                    updateSaveButton();

                    // Add to history
                    addToSettingsHistory('settings_imported', 'جميع الإعدادات', 'تم استيراد الإعدادات');

                    console.log('Settings imported successfully');
                }
            } else {
                alert('ملف الإعدادات غير صالح');
            }
        } catch (error) {
            console.error('Error importing settings:', error);
            alert('خطأ في استيراد الإعدادات');
        }
    };

    reader.readAsText(file);

    // Reset file input
    event.target.value = '';
}

// Setup auto-save
function setupAutoSave() {
    // Auto-save every 30 seconds if there are unsaved changes
    setInterval(() => {
        if (unsavedChanges) {
            saveSystemSettings();
            console.log('Auto-saved settings');
        }
    }, 30000);
}

// Update settings history display
function updateSettingsHistoryDisplay() {
    const historyContainer = document.getElementById('settings-history-list');
    if (!historyContainer) return;

    historyContainer.innerHTML = settingsHistory.slice(0, 20).map(entry => `
        <div class="history-entry">
            <div class="history-header">
                <span class="history-action">${getActionLabel(entry.action)}</span>
                <span class="history-time">${new Date(entry.timestamp).toLocaleString('ar-SA')}</span>
            </div>
            <div class="history-details">
                <span class="history-setting">${entry.settingName}</span>
                <span class="history-user">بواسطة: ${entry.userId}</span>
            </div>
        </div>
    `).join('');
}

// Get action label
function getActionLabel(action) {
    const labels = {
        'setting_changed': 'تغيير إعداد',
        'settings_saved': 'حفظ الإعدادات',
        'settings_reset': 'إعادة تعيين',
        'settings_exported': 'تصدير',
        'settings_imported': 'استيراد'
    };

    return labels[action] || action;
}

// Get setting value
function getSettingValue(key) {
    return systemSettings[key];
}

// Set setting value
function setSettingValue(key, value) {
    updateSetting(key, value);

    // Update UI element if exists
    const element = document.getElementById(`setting-${key}`);
    if (element) {
        if (element.type === 'checkbox') {
            element.checked = value;
        } else {
            element.value = value;
        }
    }
}

// Validate settings
function validateSettings() {
    const errors = [];

    Object.values(settingsCategories).forEach(category => {
        category.settings.forEach(setting => {
            const value = systemSettings[setting.key];

            // Check required fields
            if (value === undefined || value === null || value === '') {
                errors.push(`${setting.name} مطلوب`);
            }

            // Check number ranges
            if (setting.type === 'number' && typeof value === 'number') {
                if (setting.min !== undefined && value < setting.min) {
                    errors.push(`${setting.name} يجب أن يكون أكبر من أو يساوي ${setting.min}`);
                }
                if (setting.max !== undefined && value > setting.max) {
                    errors.push(`${setting.name} يجب أن يكون أصغر من أو يساوي ${setting.max}`);
                }
            }
        });
    });

    return errors;
}

// Apply settings to system
function applySettingsToSystem() {
    // This would apply settings to the actual system
    // For example, updating cache settings, notification settings, etc.

    console.log('Applying settings to system:', systemSettings);

    // Example: Apply maintenance mode
    if (systemSettings.maintenance_mode) {
        console.log('Maintenance mode enabled');
        // Show maintenance message
    }

    // Example: Apply cache settings
    if (systemSettings.cache_enabled) {
        console.log('Cache enabled with duration:', systemSettings.cache_duration);
    }
}

// Make functions globally available
window.getSettingValue = getSettingValue;
window.setSettingValue = setSettingValue;
window.saveSystemSettings = saveSystemSettings;
window.resetToDefaults = resetToDefaults;
window.exportSettings = exportSettings;
window.validateSettings = validateSettings;
window.applySettingsToSystem = applySettingsToSystem;