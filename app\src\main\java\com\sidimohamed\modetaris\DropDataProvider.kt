package com.sidimohamed.modetaris

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.util.Log

/**
 * A simple ContentProvider implementation that doesn't do anything.
 * This is used as a placeholder to prevent crashes when the system tries to access
 * a provider that was previously declared in the manifest but doesn't exist anymore.
 */
class DropDataProvider : ContentProvider() {
    
    private val TAG = "DropDataProvider"
    
    override fun onCreate(): Boolean {
        Log.d(TAG, "onCreate called")
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        Log.d(TAG, "query called with uri: $uri")
        return null
    }

    override fun getType(uri: Uri): String? {
        Log.d(TAG, "getType called with uri: $uri")
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        Log.d(TAG, "insert called with uri: $uri")
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        Log.d(TAG, "delete called with uri: $uri")
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        Log.d(TAG, "update called with uri: $uri")
        return 0
    }
}
