-- إصلاحا<PERSON> قاعدة البيانات الشاملة
-- يحل جميع مشاكل قاعدة البيانات المكتشفة

-- 1. إنشاء جدول mods إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS mods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    category TEXT NOT NULL,
    image_url TEXT,
    image_urls JSONB,
    downloads INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    creator_name TEXT,
    creator_social_media JSONB,
    is_featured BOOLEAN DEFAULT false,
    is_popular BOOLEAN DEFAULT false,
    is_free_addon BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة الأعمدة الجديدة إذا لم تكن موجودة
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'mods' AND column_name = 'is_featured') THEN
        ALTER TABLE mods ADD COLUMN is_featured BOOLEAN DEFAULT false;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'mods' AND column_name = 'is_popular') THEN
        ALTER TABLE mods ADD COLUMN is_popular BOOLEAN DEFAULT false;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'mods' AND column_name = 'is_free_addon') THEN
        ALTER TABLE mods ADD COLUMN is_free_addon BOOLEAN DEFAULT false;
    END IF;
END $$;

-- 2. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_mods_downloads ON mods(downloads DESC);
CREATE INDEX IF NOT EXISTS idx_mods_likes ON mods(likes DESC);
CREATE INDEX IF NOT EXISTS idx_mods_featured ON mods(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_mods_popular ON mods(is_popular) WHERE is_popular = true;

-- 3. إنشاء دالة increment_clicks المحسنة
DROP FUNCTION IF EXISTS increment_clicks(text);
DROP FUNCTION IF EXISTS increment_clicks(integer);
DROP FUNCTION IF EXISTS increment_clicks(uuid);

CREATE OR REPLACE FUNCTION increment_clicks(mod_id_param UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
    new_clicks INTEGER;
BEGIN
    -- التحقق من وجود العمود clicks
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'clicks'
    ) THEN
        -- إضافة العمود إذا لم يكن موجوداً
        ALTER TABLE mods ADD COLUMN clicks INTEGER DEFAULT 0;
    END IF;
    
    -- تحديث عدد النقرات
    UPDATE mods 
    SET clicks = COALESCE(clicks, 0) + 1,
        updated_at = NOW()
    WHERE id = mod_id_param;
    
    -- الحصول على العدد الجديد
    SELECT COALESCE(clicks, 0) INTO new_clicks 
    FROM mods 
    WHERE id = mod_id_param;
    
    -- إرجاع النتيجة
    result := json_build_object(
        'success', true,
        'mod_id', mod_id_param,
        'new_clicks', new_clicks,
        'timestamp', NOW()
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'mod_id', mod_id_param,
        'timestamp', NOW()
    );
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. إنشاء دالة execute_sql للإصلاحات التلقائية
DROP FUNCTION IF EXISTS execute_sql(text);
DROP FUNCTION IF EXISTS execute_sql(TEXT);

CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    EXECUTE sql_query;
    
    result := json_build_object(
        'success', true,
        'message', 'SQL executed successfully',
        'timestamp', NOW()
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'timestamp', NOW()
    );
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. إنشاء دالة create_mods_table_if_not_exists
CREATE OR REPLACE FUNCTION create_mods_table_if_not_exists()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    -- إنشاء الجدول إذا لم يكن موجوداً
    CREATE TABLE IF NOT EXISTS mods (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        description TEXT,
        description_ar TEXT,
        category TEXT NOT NULL,
        image_url TEXT,
        image_urls JSONB,
        downloads INTEGER DEFAULT 0,
        likes INTEGER DEFAULT 0,
        clicks INTEGER DEFAULT 0,
        creator_name TEXT,
        creator_social_media JSONB,
        is_featured BOOLEAN DEFAULT false,
        is_popular BOOLEAN DEFAULT false,
        is_free_addon BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- إضافة الفهارس
    CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
    CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at DESC);
    
    result := json_build_object(
        'success', true,
        'message', 'Mods table created successfully',
        'timestamp', NOW()
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'timestamp', NOW()
    );
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. إنشاء جدول error_logs لتتبع الأخطاء
CREATE TABLE IF NOT EXISTS error_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    error_type TEXT NOT NULL,
    error_message TEXT,
    error_details JSONB,
    user_agent TEXT,
    url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON error_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_error_logs_type ON error_logs(error_type);

-- 7. إنشاء جدول user_languages إذا لم يكن موجوداً
-- إضافة الأعمدة الجديدة إلى جدول user_languages إذا لم تكن موجودة (لضمان وجودها قبل إنشاء الجدول أو الفهارس)
ALTER TABLE user_languages ADD COLUMN IF NOT EXISTS user_id TEXT;
ALTER TABLE user_languages ADD COLUMN IF NOT EXISTS language_code TEXT;
ALTER TABLE user_languages ADD COLUMN IF NOT EXISTS selected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

CREATE TABLE IF NOT EXISTS user_languages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT,
    language_code TEXT, -- Temporarily allow NULL for existing tables
    selected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- التأكد من أن language_code ليس NULL وله قيمة افتراضية، مع معالجة القيم NULL الموجودة
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_languages' AND column_name = 'language_code') THEN
        EXECUTE 'UPDATE user_languages SET language_code = COALESCE(language_code, ''en'') WHERE language_code IS NULL';
        ALTER TABLE user_languages ALTER COLUMN language_code SET NOT NULL;
        ALTER TABLE user_languages ALTER COLUMN language_code SET DEFAULT 'en';
    END IF;
END $$;

-- إضافة فهارس لجدول user_languages
CREATE INDEX IF NOT EXISTS idx_user_languages_user_id ON user_languages(user_id);
CREATE INDEX IF NOT EXISTS idx_user_languages_language ON user_languages(language_code);

-- 8. إنشاء جدول banner_ads إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS banner_ads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    click_url TEXT,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_banner_ads_active ON banner_ads(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_banner_ads_order ON banner_ads(display_order);

-- 9. إنشاء جدول free_addons إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS free_addons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mod_id UUID REFERENCES mods(id) ON DELETE CASCADE,
    is_featured BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة عمود is_featured إذا لم يكن موجوداً
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'free_addons' AND column_name = 'is_featured') THEN
        ALTER TABLE free_addons ADD COLUMN is_featured BOOLEAN DEFAULT false;
    END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_free_addons_mod_id ON free_addons(mod_id);
CREATE INDEX IF NOT EXISTS idx_free_addons_featured ON free_addons(is_featured) WHERE is_featured = true;

-- 10. إنشاء جدول custom_sections إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS custom_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 11. إنشاء دالة لإصلاح البيانات التالفة
CREATE OR REPLACE FUNCTION fix_corrupted_data()
RETURNS JSON AS $$
DECLARE
    result JSON;
    fixed_count INTEGER := 0;
BEGIN
    -- إصلاح القيم الفارغة
    UPDATE mods SET 
        downloads = 0 WHERE downloads IS NULL;
    GET DIAGNOSTICS fixed_count = ROW_COUNT;
    
    UPDATE mods SET 
        likes = 0 WHERE likes IS NULL;
    
    UPDATE mods SET 
        clicks = 0 WHERE clicks IS NULL;
    
    UPDATE mods SET 
        is_featured = false WHERE is_featured IS NULL;
    
    UPDATE mods SET 
        is_popular = false WHERE is_popular IS NULL;
    
    UPDATE mods SET 
        is_free_addon = false WHERE is_free_addon IS NULL;
    
    -- إصلاح التواريخ
    UPDATE mods SET 
        created_at = NOW() WHERE created_at IS NULL;
    
    UPDATE mods SET 
        updated_at = NOW() WHERE updated_at IS NULL;
    
    result := json_build_object(
        'success', true,
        'message', 'Data corruption fixed',
        'fixed_records', fixed_count,
        'timestamp', NOW()
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'timestamp', NOW()
    );
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. إنشاء دالة للتحقق من صحة البيانات
CREATE OR REPLACE FUNCTION validate_database_health()
RETURNS JSON AS $$
DECLARE
    result JSON;
    table_count INTEGER;
    function_count INTEGER;
    index_count INTEGER;
BEGIN
    -- عد الجداول المطلوبة
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_name IN ('mods', 'error_logs', 'user_languages', 'banner_ads', 'free_addons', 'custom_sections');
    
    -- عد الدوال المطلوبة
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines 
    WHERE routine_name IN ('increment_clicks', 'execute_sql', 'create_mods_table_if_not_exists');
    
    -- عد الفهارس
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE indexname LIKE 'idx_mods_%';
    
    result := json_build_object(
        'success', true,
        'tables_count', table_count,
        'functions_count', function_count,
        'indexes_count', index_count,
        'health_status', CASE 
            WHEN table_count >= 6 AND function_count >= 3 THEN 'excellent'
            WHEN table_count >= 4 AND function_count >= 2 THEN 'good'
            WHEN table_count >= 2 AND function_count >= 1 THEN 'fair'
            ELSE 'poor'
        END,
        'timestamp', NOW()
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    result := json_build_object(
        'success', false,
        'error', SQLERRM,
        'timestamp', NOW()
    );
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تشغيل إصلاح البيانات التالفة
SELECT fix_corrupted_data();

-- التحقق من صحة قاعدة البيانات
SELECT validate_database_health();
