# الميزات الجديدة - New Features

## 🚀 التحديثات الجديدة المضافة

### 1. نظام الإعلانات الاحتياطية (Backup Ads System)

#### الوصف
نظام إعلانات احتياطي يعمل عند فشل إعلانات AdMob الأساسية، مما يضمن عدم فقدان الإيرادات الإعلانية.

#### الميزات:
- **دعم الصور والفيديو**: إمكانية إنشاء إعلانات بالصور أو الفيديو
- **نظام الأولوية**: ترتيب الإعلانات حسب الأولوية (1-10)
- **مدة العرض المخصصة**: تحديد مدة عرض كل إعلان (3-30 ثانية)
- **إجراءات النقر**: خيارات متعددة (لا يوجد إجراء، فتح رابط، إغلاق)
- **الاستهداف**: إمكانية استهداف فئات معينة من المودات
- **الإحصائيات**: تتبع عدد المشاهدات والنقرات
- **الجدولة**: إمكانية تحديد تواريخ بداية ونهاية للإعلانات

#### الملفات المضافة:
- `admin/backup-ads-manager.html` - صفحة إدارة الإعلانات الاحتياطية
- `admin/backup-ads-manager.js` - منطق إدارة الإعلانات الاحتياطية
- `backup-ads-integration.js` - تكامل النظام مع التطبيق الرئيسي
- `admin/backup_ads_table.sql` - جداول قاعدة البيانات

#### كيفية الاستخدام:
1. الدخول إلى لوحة الإدارة
2. اختيار "الإعلانات الاحتياطية" من قسم البانرات
3. إنشاء إعلان جديد بتحديد النوع والأولوية
4. رفع ملف الوسائط (صورة أو فيديو)
5. تحديد إعدادات العرض والنقر
6. تفعيل الإعلان

### 2. منشئ الحملات والبانرات الموحد (Unified Campaign Creator)

#### الوصف
أداة موحدة لإنشاء حملة اشتراك مجاني مع بانر إعلاني في عملية واحدة مبسطة.

#### الميزات:
- **واجهة خطوة بخطوة**: عملية إنشاء مقسمة إلى 4 خطوات واضحة
- **دعم ثنائي اللغة**: إدخال المحتوى بالعربية والإنجليزية
- **معاينة فورية**: عرض معاينة للصور المرفوعة
- **ملخص شامل**: مراجعة جميع البيانات قبل الإنشاء
- **إنشاء تلقائي**: إنشاء الحملة والبانر تلقائياً في قاعدة البيانات
- **رفع الصور**: رفع صورة البانر وصورة النافذة المنبثقة

#### الملفات المضافة:
- `admin/unified-subscription-banner.html` - صفحة المنشئ الموحد
- `admin/unified-subscription-banner.js` - منطق المنشئ الموحد

#### كيفية الاستخدام:
1. الدخول إلى لوحة الإدارة
2. اختيار "منشئ موحد" من قسم البانرات
3. اتباع الخطوات الأربع:
   - معلومات الحملة (العنوان والوصف)
   - إعدادات الاشتراك (المدة والحدود)
   - تصميم البانر (الصور والتخطيط)
   - المراجعة والنشر
4. تأكيد الإنشاء

### 3. تحسينات إعدادات المستخدم (Enhanced User Settings)

#### الوصف
تحسينات شاملة لصفحة إعدادات المستخدم مع تطبيق فوري للتغييرات على التطبيق الرئيسي.

#### الميزات المحسنة:
- **تطبيق فوري**: تطبيق الإعدادات على التطبيق الرئيسي فوراً
- **تواصل بين النوافذ**: نظام رسائل لتطبيق الإعدادات
- **معالجة أفضل للأخطاء**: رسائل خطأ ونجاح واضحة
- **تحسين الأداء**: تحسين تطبيق التأثيرات البصرية
- **حفظ تلقائي**: حفظ الإعدادات تلقائياً عند التغيير

#### التحسينات المضافة:
- دالة `applyAllSettingsToMainApp()` لتطبيق جميع الإعدادات
- نظام رسائل بين النوافذ للتواصل مع التطبيق الرئيسي
- تحسين دوال `applyDarkMode()` و `applyVisualEffects()`
- إضافة دالة `autoApplySettings()` للتطبيق التلقائي

## 🛠️ التحديثات التقنية

### قاعدة البيانات
#### جداول جديدة:
1. **backup_ads** - جدول الإعلانات الاحتياطية
2. **backup_ads_stats** - إحصائيات الإعلانات الاحتياطية

#### Views جديدة:
- **backup_ads_summary** - ملخص إحصائيات الإعلانات

#### Functions جديدة:
- **get_random_backup_ad()** - جلب إعلان احتياطي عشوائي
- **log_backup_ad_event()** - تسجيل أحداث الإعلانات

### الملفات المحدثة:
- `admin/index.html` - إضافة روابط الميزات الجديدة
- `admin/unified-admin.js` - إضافة وظائف الميزات الجديدة
- `index.html` - إضافة ملف تكامل الإعلانات الاحتياطية
- `user-settings.js` - تحسينات شاملة

## 📋 متطلبات التشغيل

### قاعدة البيانات:
1. تشغيل ملف `admin/backup_ads_table.sql` لإنشاء الجداول المطلوبة
2. التأكد من صلاحيات الكتابة في جداول Supabase

### التخزين:
1. إنشاء bucket جديد في Supabase Storage باسم `backup-ads`
2. تعيين صلاحيات القراءة العامة للـ bucket

### الإعدادات:
1. التأكد من تحديث ملف `config.js` بمعلومات Supabase الصحيحة
2. فحص صلاحيات الوصول لجميع الجداول

## 🎯 كيفية الاستخدام

### للمطورين:
1. **تفعيل الإعلانات الاحتياطية**:
   ```javascript
   // تفعيل النظام
   window.backupAds.setEnabled(true);
   
   // عرض إعلان احتياطي يدوياً
   await window.backupAds.show(modId, modCategory);
   ```

2. **تكامل مع نظام التحميل**:
   ```javascript
   // النظام يتكامل تلقائياً مع وظائف التحميل الموجودة
   // ويعرض إعلان احتياطي عند فشل AdMob
   ```

### للإداريين:
1. **إدارة الإعلانات الاحتياطية**:
   - الدخول إلى لوحة الإدارة
   - اختيار "الإعلانات الاحتياطية"
   - إنشاء وإدارة الإعلانات

2. **إنشاء حملات موحدة**:
   - الدخول إلى لوحة الإدارة
   - اختيار "منشئ موحد"
   - اتباع الخطوات المطلوبة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:
1. **عدم ظهور الإعلانات الاحتياطية**:
   - التأكد من وجود إعلانات نشطة في قاعدة البيانات
   - فحص console للأخطاء
   - التأكد من تحميل ملف `backup-ads-integration.js`

2. **مشاكل رفع الصور**:
   - التأكد من إنشاء bucket `backup-ads` في Supabase Storage
   - فحص صلاحيات الرفع
   - التأكد من حجم الصورة (أقل من 2MB)

3. **مشاكل إعدادات المستخدم**:
   - فحص console للأخطاء في التواصل بين النوافذ
   - التأكد من تحميل جميع ملفات JavaScript
   - فحص localStorage للإعدادات المحفوظة

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- فحص console المتصفح للأخطاء
- التأكد من تحديث جميع الملفات
- مراجعة ملفات السجل في Supabase

## 🔄 التحديثات المستقبلية

### ميزات مخططة:
- تحسين خوارزمية اختيار الإعلانات الاحتياطية
- إضافة تقارير تفصيلية للإحصائيات
- دعم إعلانات تفاعلية أكثر
- تحسين واجهة المستخدم للإدارة
- إضافة نظام إشعارات للإداريين

---

**تاريخ التحديث**: يناير 2025  
**الإصدار**: 2.1.0  
**المطور**: فريق Mod Etaris
