-- ========================================
-- SQL للتنفيذ المباشر في Supabase
-- Execute this SQL directly in Supabase
-- ========================================

-- 1. إنشاء جدول لغات المستخدمين
-- Create user languages table
CREATE TABLE IF NOT EXISTS user_languages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL UNIQUE,
    selected_language VARCHAR(10) NOT NULL DEFAULT 'en' CHECK (selected_language IN ('ar', 'en')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    device_info JSONB DEFAULT '{}',
    user_agent TEXT
);

-- 2. إض<PERSON><PERSON>ة عمود الوصف العربي لجدول المودات
-- Add Arabic description column to mods table
ALTER TABLE mods ADD COLUMN IF NOT EXISTS description_ar TEXT;

-- 3. إنشاء فهارس للأداء
-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_languages_language ON user_languages(selected_language);
CREATE INDEX IF NOT EXISTS idx_user_languages_created_at ON user_languages(created_at);
CREATE INDEX IF NOT EXISTS idx_mods_description_ar ON mods USING gin(to_tsvector('arabic', description_ar));

-- 4. إضافة تعليقات توضيحية
-- Add documentation comments
COMMENT ON TABLE user_languages IS 'Stores user language preferences';
COMMENT ON COLUMN user_languages.user_id IS 'Unique user identifier';
COMMENT ON COLUMN user_languages.selected_language IS 'ar for Arabic, en for English';
COMMENT ON COLUMN mods.description_ar IS 'Arabic description for the mod';

-- 5. إنشاء view للإحصائيات
-- Create statistics view
CREATE OR REPLACE VIEW language_usage_stats AS
SELECT 
    selected_language,
    COUNT(*) as user_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage,
    MIN(created_at) as first_user,
    MAX(updated_at) as latest_update
FROM user_languages 
GROUP BY selected_language
ORDER BY user_count DESC;

-- 6. دالة لحفظ لغة المستخدم
-- Function to save user language
CREATE OR REPLACE FUNCTION save_user_language(
    p_user_id VARCHAR(255),
    p_language VARCHAR(10),
    p_user_agent TEXT DEFAULT NULL,
    p_device_info JSONB DEFAULT '{}'
)
RETURNS BOOLEAN AS $$
BEGIN
    IF p_language NOT IN ('ar', 'en') THEN
        RAISE EXCEPTION 'Invalid language code. Must be ar or en.';
    END IF;
    
    INSERT INTO user_languages (
        user_id, 
        selected_language, 
        user_agent, 
        device_info,
        created_at,
        updated_at
    ) VALUES (
        p_user_id, 
        p_language, 
        p_user_agent, 
        p_device_info,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        selected_language = EXCLUDED.selected_language,
        updated_at = CURRENT_TIMESTAMP,
        user_agent = COALESCE(EXCLUDED.user_agent, user_languages.user_agent),
        device_info = EXCLUDED.device_info;
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- 7. دالة للحصول على لغة المستخدم
-- Function to get user language
CREATE OR REPLACE FUNCTION get_user_language(p_user_id VARCHAR(255))
RETURNS VARCHAR(10) AS $$
DECLARE
    user_lang VARCHAR(10);
BEGIN
    SELECT selected_language INTO user_lang
    FROM user_languages 
    WHERE user_id = p_user_id;
    
    RETURN COALESCE(user_lang, 'en');
END;
$$ LANGUAGE plpgsql;

-- 8. دالة للحصول على وصف المود حسب لغة المستخدم
-- Function to get mod description based on user language
CREATE OR REPLACE FUNCTION get_mod_description_for_user(
    p_mod_id UUID,
    p_user_id VARCHAR(255)
)
RETURNS TEXT AS $$
DECLARE
    user_lang VARCHAR(10);
    mod_desc_en TEXT;
    mod_desc_ar TEXT;
BEGIN
    user_lang := get_user_language(p_user_id);
    
    SELECT description, description_ar 
    INTO mod_desc_en, mod_desc_ar
    FROM mods 
    WHERE id = p_mod_id;
    
    IF user_lang = 'ar' AND mod_desc_ar IS NOT NULL AND mod_desc_ar != '' THEN
        RETURN mod_desc_ar;
    ELSE
        RETURN COALESCE(mod_desc_en, 'No description available.');
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 9. تحديث بعض المودات بوصوفات عربية تجريبية (اختياري)
-- Update some mods with sample Arabic descriptions (optional)
UPDATE mods 
SET description_ar = CASE 
    WHEN category = 'Shaders' THEN 
        'شيدر متطور يحسن من جودة الإضاءة والظلال في ماين كرافت. يوفر تجربة بصرية واقعية مع تأثيرات محسنة.'
    WHEN category = 'Addons' THEN 
        'إضافة مميزة تضيف محتوى جديد ومثير إلى عالم ماين كرافت. سهلة التثبيت ومتوافقة مع الإصدارات الحديثة.'
    WHEN category = 'Texture Pack' THEN 
        'حزمة نسيج عالية الدقة تغير مظهر الكتل والعناصر في اللعبة لتجربة بصرية محسنة.'
    WHEN category = 'Maps' THEN 
        'خريطة مصممة بعناية توفر تجربة لعب فريدة ومليئة بالمغامرات والتحديات.'
    WHEN category = 'Seeds' THEN 
        'بذرة عالم مميزة تولد تضاريس خلابة وهياكل نادرة مثالية للاستكشاف والبناء.'
    ELSE 
        'محتوى رائع لماين كرافت يضيف تجربة جديدة ومثيرة للعبة.'
END
WHERE description_ar IS NULL AND description IS NOT NULL
LIMIT 20; -- تحديث 20 مود فقط كمثال

-- 10. إدراج بيانات تجريبية (اختياري - يمكن حذفها لاحقاً)
-- Insert test data (optional - can be deleted later)
INSERT INTO user_languages (user_id, selected_language, device_info) VALUES 
('test_user_ar_1', 'ar', '{"device": "mobile", "browser": "chrome"}'),
('test_user_en_1', 'en', '{"device": "desktop", "browser": "firefox"}')
ON CONFLICT (user_id) DO NOTHING;

-- ========================================
-- استعلامات مفيدة للمراقبة
-- Useful monitoring queries
-- ========================================

-- عرض إحصائيات اللغات
-- View language statistics
-- SELECT * FROM language_usage_stats;

-- عرض آخر 10 مستخدمين
-- View last 10 users
-- SELECT user_id, selected_language, created_at, device_info->>'device' as device 
-- FROM user_languages ORDER BY created_at DESC LIMIT 10;

-- عدد المودات التي تحتاج وصف عربي
-- Count mods needing Arabic description
-- SELECT 
--     category,
--     COUNT(*) as total_mods,
--     COUNT(description_ar) as with_arabic,
--     COUNT(*) - COUNT(description_ar) as missing_arabic
-- FROM mods 
-- GROUP BY category;

-- ========================================
-- تم الانتهاء من الإعداد!
-- Setup Complete!
-- ========================================
