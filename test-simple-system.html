<!DOCTYPE html>
<html dir="ltr" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple System - Mod Etaris</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e1e2e, #2d2d44);
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #ffd700;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid #22c55e;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
        }
        
        .warning {
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid #f59e0b;
        }
        
        button {
            background: linear-gradient(45deg, #ffd700, #ffb347);
            color: #1e1e2e;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
        }
        
        .mod-card-test {
            display: inline-block;
            width: 200px;
            margin: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            text-align: center;
        }
        
        .mod-card-test img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .mod-card-test h3 {
            margin: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار النظام المبسط</h1>
        <p>هذه الصفحة لاختبار النظام المبسط والتأكد من عمل جميع المكونات بشكل صحيح.</p>
        
        <div class="test-section">
            <h2>📡 اختبار الشبكة</h2>
            <button onclick="testNetwork()">اختبار الاتصال</button>
            <div id="networkResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🖼️ اختبار تحميل الصور</h2>
            <button onclick="testImageLoading()">اختبار Lazy Loading</button>
            <div id="imageResult" class="test-result"></div>
            <div id="imageContainer"></div>
        </div>
        
        <div class="test-section">
            <h2>🗄️ اختبار قاعدة البيانات</h2>
            <button onclick="testDatabase()">اختبار Supabase</button>
            <div id="databaseResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>⚡ اختبار الأداء</h2>
            <button onclick="testPerformance()">قياس الأداء</button>
            <div id="performanceResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>📱 اختبار الاستجابة</h2>
            <button onclick="testResponsiveness()">اختبار التصميم المتجاوب</button>
            <div id="responsivenessResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // اختبار الشبكة
        function testNetwork() {
            const resultDiv = document.getElementById('networkResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار الاتصال...';
            
            const startTime = performance.now();
            
            fetch('https://httpbin.org/status/200')
                .then(response => {
                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);
                    
                    if (response.ok) {
                        resultDiv.className = 'test-result success';
                        resultDiv.innerHTML = `✅ الاتصال يعمل بشكل ممتاز! زمن الاستجابة: ${responseTime}ms`;
                    } else {
                        resultDiv.className = 'test-result warning';
                        resultDiv.innerHTML = `⚠️ الاتصال يعمل لكن هناك مشكلة في الاستجابة`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ فشل الاتصال: ${error.message}`;
                });
        }
        
        // اختبار تحميل الصور
        function testImageLoading() {
            const resultDiv = document.getElementById('imageResult');
            const container = document.getElementById('imageContainer');
            
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار تحميل الصور...';
            
            // إنشاء صور اختبار
            const testImages = [
                'https://picsum.photos/200/120?random=1',
                'https://picsum.photos/200/120?random=2',
                'https://picsum.photos/200/120?random=3'
            ];
            
            container.innerHTML = '';
            let loadedCount = 0;
            const startTime = performance.now();
            
            testImages.forEach((src, index) => {
                const card = document.createElement('div');
                card.className = 'mod-card-test';
                
                const img = document.createElement('img');
                img.setAttribute('data-src', src);
                img.className = 'lazy-load';
                img.alt = `Test Image ${index + 1}`;
                
                img.onload = () => {
                    loadedCount++;
                    if (loadedCount === testImages.length) {
                        const endTime = performance.now();
                        const loadTime = Math.round(endTime - startTime);
                        resultDiv.className = 'test-result success';
                        resultDiv.innerHTML = `✅ تم تحميل جميع الصور بنجاح! الوقت المستغرق: ${loadTime}ms`;
                    }
                };
                
                img.onerror = () => {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ فشل في تحميل إحدى الصور`;
                };
                
                const title = document.createElement('h3');
                title.textContent = `صورة اختبار ${index + 1}`;
                
                card.appendChild(img);
                card.appendChild(title);
                container.appendChild(card);
                
                // محاكاة lazy loading
                setTimeout(() => {
                    img.src = img.getAttribute('data-src');
                }, 100 * index);
            });
        }
        
        // اختبار قاعدة البيانات
        function testDatabase() {
            const resultDiv = document.getElementById('databaseResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار قاعدة البيانات...';
            
            // محاكاة اختبار قاعدة البيانات
            setTimeout(() => {
                if (typeof supabaseClient !== 'undefined') {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ اتصال قاعدة البيانات يعمل بشكل صحيح!';
                } else {
                    resultDiv.className = 'test-result warning';
                    resultDiv.innerHTML = '⚠️ لم يتم العثور على عميل Supabase - قد يكون هذا طبيعي في صفحة الاختبار';
                }
            }, 1000);
        }
        
        // اختبار الأداء
        function testPerformance() {
            const resultDiv = document.getElementById('performanceResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري قياس الأداء...';
            
            const startTime = performance.now();
            
            // محاكاة عمليات مختلفة
            setTimeout(() => {
                const endTime = performance.now();
                const totalTime = Math.round(endTime - startTime);
                
                const memoryUsage = performance.memory ? 
                    Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 'غير متاح';
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `
                    ✅ نتائج الأداء:<br>
                    - وقت الاستجابة: ${totalTime}ms<br>
                    - استخدام الذاكرة: ${memoryUsage}MB<br>
                    - حالة النظام: ممتاز
                `;
            }, 500);
        }
        
        // اختبار الاستجابة
        function testResponsiveness() {
            const resultDiv = document.getElementById('responsivenessResult');
            
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            
            let deviceType = 'غير محدد';
            if (screenWidth < 768) deviceType = 'هاتف محمول';
            else if (screenWidth < 1024) deviceType = 'تابلت';
            else deviceType = 'سطح المكتب';
            
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = `
                ✅ معلومات الشاشة:<br>
                - العرض: ${screenWidth}px<br>
                - الارتفاع: ${screenHeight}px<br>
                - نوع الجهاز: ${deviceType}<br>
                - التصميم المتجاوب: يعمل بشكل صحيح
            `;
        }
        
        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            console.log('🧪 صفحة اختبار النظام المبسط جاهزة');
            console.log('📊 يمكنك الآن اختبار جميع المكونات');
        });
    </script>
</body>
</html>
