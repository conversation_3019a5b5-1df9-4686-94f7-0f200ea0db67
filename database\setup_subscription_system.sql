-- ========================================
-- نظام الاشتراك المجاني المتقدم
-- Free Subscription System Setup
-- ========================================

-- تنظيف الجداول الموجودة (للتطوير فقط)
DROP TABLE IF EXISTS user_task_progress CASCADE;
DROP TABLE IF EXISTS user_subscriptions CASCADE;
DROP TABLE IF EXISTS campaign_tasks CASCADE;
DROP TABLE IF EXISTS free_subscription_campaigns CASCADE;
DROP TABLE IF EXISTS task_types CASCADE;

-- ========================================
-- 1. جدول أنواع المهام المتاحة
-- ========================================
CREATE TABLE task_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name_ar VARCHAR(200) NOT NULL,
    display_name_en VARCHAR(200) NOT NULL,
    icon VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إدراج أنواع المهام الافتراضية
INSERT INTO task_types (name, display_name_ar, display_name_en, icon) VALUES
('telegram_subscribe', 'اشتراك في قناة تيليجرام', 'Subscribe to Telegram Channel', 'fab fa-telegram'),
('youtube_subscribe', 'اشتراك في قناة يوتيوب', 'Subscribe to YouTube Channel', 'fab fa-youtube'),
('twitter_follow', 'متابعة على تويتر', 'Follow on Twitter', 'fab fa-twitter'),
('facebook_like', 'إعجاب صفحة فيسبوك', 'Like Facebook Page', 'fab fa-facebook'),
('snapchat_add', 'إضافة على سناب شات', 'Add on Snapchat', 'fab fa-snapchat'),
('app_download', 'تحميل تطبيق', 'Download App', 'fas fa-download'),
('mod_download', 'تحميل مود', 'Download Mod', 'fas fa-cube'),
('rate_product', 'تقييم منتج', 'Rate Product', 'fas fa-star'),
('download_multiple_mods', 'تحميل عدة مودات', 'Download Multiple Mods', 'fas fa-cubes'),
('custom_task', 'مهمة مخصصة', 'Custom Task', 'fas fa-tasks');

-- ========================================
-- 2. جدول حملات الاشتراك المجاني
-- ========================================
CREATE TABLE free_subscription_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200) NOT NULL,
    description_ar TEXT NOT NULL,
    description_en TEXT NOT NULL,
    banner_image_url TEXT DEFAULT '',
    popup_image_url TEXT,
    subscription_duration_days INTEGER NOT NULL DEFAULT 30,
    max_users INTEGER DEFAULT NULL,
    current_users INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- 3. جدول مهام الحملة
-- ========================================
CREATE TABLE campaign_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id) ON DELETE CASCADE,
    task_type VARCHAR(100) NOT NULL REFERENCES task_types(name),
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    target_url TEXT,
    target_count INTEGER DEFAULT 1,
    verification_method VARCHAR(50) DEFAULT 'manual',
    display_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- 4. جدول اشتراكات المستخدمين
-- ========================================
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL,
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id),
    status VARCHAR(20) DEFAULT 'pending',
    started_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, campaign_id)
);

-- ========================================
-- 5. جدول تقدم المستخدم في المهام
-- ========================================
CREATE TABLE user_task_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL,
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id),
    task_id UUID NOT NULL REFERENCES campaign_tasks(id),
    status VARCHAR(20) DEFAULT 'pending',
    completed_at TIMESTAMP WITH TIME ZONE,
    verification_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, task_id)
);

-- ========================================
-- 6. تحديث جدول البانر الحالي
-- ========================================
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS campaign_id UUID REFERENCES free_subscription_campaigns(id);
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS banner_type VARCHAR(20) DEFAULT 'regular';
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS image_width INTEGER;
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS image_height INTEGER;

-- ========================================
-- 7. إنشاء الفهارس لتحسين الأداء
-- ========================================
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_user_id ON user_task_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_status ON user_task_progress(status);
CREATE INDEX IF NOT EXISTS idx_campaign_tasks_campaign_id ON campaign_tasks(campaign_id);
CREATE INDEX IF NOT EXISTS idx_banner_ads_campaign_id ON banner_ads(campaign_id);

-- ========================================
-- 8. دوال التحديث التلقائي
-- ========================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء triggers للتحديث التلقائي
CREATE TRIGGER update_free_subscription_campaigns_updated_at
    BEFORE UPDATE ON free_subscription_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 9. إعداد Row Level Security (RLS)
-- ========================================
ALTER TABLE free_subscription_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_task_progress ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للقراءة العامة
CREATE POLICY "Allow public read access on active campaigns" 
    ON free_subscription_campaigns FOR SELECT 
    USING (is_active = true);

CREATE POLICY "Allow public read access on campaign tasks" 
    ON campaign_tasks FOR SELECT 
    USING (true);

-- سياسات للكتابة (للمشرفين فقط)
CREATE POLICY "Allow admin write access on campaigns" 
    ON free_subscription_campaigns FOR ALL 
    USING (true);

CREATE POLICY "Allow admin write access on campaign tasks" 
    ON campaign_tasks FOR ALL 
    USING (true);

-- سياسات للمستخدمين
CREATE POLICY "Allow users to read their own subscriptions" 
    ON user_subscriptions FOR SELECT 
    USING (true);

CREATE POLICY "Allow users to insert their own subscriptions" 
    ON user_subscriptions FOR INSERT 
    WITH CHECK (true);

CREATE POLICY "Allow users to read their own task progress" 
    ON user_task_progress FOR SELECT 
    USING (true);

CREATE POLICY "Allow users to insert/update their own task progress" 
    ON user_task_progress FOR ALL 
    USING (true);

-- ========================================
-- 10. إدراج بيانات تجريبية (اختياري)
-- ========================================

-- إنشاء حملة تجريبية
INSERT INTO free_subscription_campaigns (
    title_ar, 
    title_en, 
    description_ar, 
    description_en,
    subscription_duration_days,
    max_users,
    is_active
) VALUES (
    'احصل على اشتراك مجاني لمدة 30 يوم',
    'Get Free 30-Day Subscription',
    'احصل على ميزة مجانية تمكنك من تحميل المودات بدون إعلانات لمدة 30 يوم كامل',
    'Get a free feature that allows you to download mods without ads for 30 full days',
    30,
    100,
    true
);

-- الحصول على معرف الحملة التجريبية
DO $$
DECLARE
    campaign_uuid UUID;
BEGIN
    SELECT id INTO campaign_uuid FROM free_subscription_campaigns WHERE title_ar = 'احصل على اشتراك مجاني لمدة 30 يوم' LIMIT 1;
    
    -- إضافة مهام تجريبية
    INSERT INTO campaign_tasks (campaign_id, task_type, title_ar, title_en, description_ar, description_en, target_url, display_order) VALUES
    (campaign_uuid, 'telegram_subscribe', 'اشترك في قناة التيليجرام', 'Subscribe to Telegram Channel', 'اشترك في قناتنا على التيليجرام للحصول على آخر التحديثات', 'Subscribe to our Telegram channel for latest updates', 'https://t.me/modetaris', 1),
    (campaign_uuid, 'youtube_subscribe', 'اشترك في قناة اليوتيوب', 'Subscribe to YouTube Channel', 'اشترك في قناتنا على اليوتيوب ولا تنس تفعيل الجرس', 'Subscribe to our YouTube channel and don''t forget to ring the bell', 'https://youtube.com/@modetaris', 2),
    (campaign_uuid, 'app_download', 'حمل التطبيق الجديد', 'Download New App', 'حمل تطبيقنا الجديد من متجر جوجل بلاي', 'Download our new app from Google Play Store', 'https://play.google.com/store/apps/details?id=com.modetaris.newapp', 3);
END $$;

-- ========================================
-- تم الانتهاء من الإعداد بنجاح!
-- ========================================

-- عرض ملخص الجداول المُنشأة
SELECT 
    'تم إنشاء الجداول بنجاح!' as status,
    (SELECT COUNT(*) FROM task_types) as task_types_count,
    (SELECT COUNT(*) FROM free_subscription_campaigns) as campaigns_count,
    (SELECT COUNT(*) FROM campaign_tasks) as tasks_count;
