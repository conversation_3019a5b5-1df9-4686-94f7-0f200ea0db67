<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حملة سريعة - Easy Campaign Creator</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .header h1 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5);
        }

        .quick-templates {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .template-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px solid rgba(255, 215, 0, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
            border-color: #ffd700;
        }

        .template-card.selected {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }

        .template-icon {
            font-size: 3rem;
            color: #ffd700;
            margin-bottom: 15px;
        }

        .template-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffd700;
        }

        .template-desc {
            color: #ccc;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .section-title {
            color: #ffd700;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            color: #ffd700;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .duration-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .duration-option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .duration-option:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }

        .duration-option.selected {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.2);
        }

        .duration-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffd700;
        }

        .duration-label {
            color: #ccc;
            font-size: 0.9rem;
        }

        .tasks-count-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .task-count-option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .task-count-option:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
            transform: translateY(-3px);
        }

        .task-count-option.selected {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.2);
            box-shadow: 0 5px 20px rgba(255, 215, 0, 0.3);
        }

        .count-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 8px;
        }

        .count-label {
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .count-desc {
            color: #ccc;
            font-size: 0.8rem;
        }

        .selected-count {
            color: #ffd700;
            font-size: 0.9rem;
            margin-right: 10px;
            font-weight: bold;
        }

        .platform-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .platform-priority {
            background: rgba(255, 215, 0, 0.2);
            color: #ffd700;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .platform-desc {
            color: #ccc;
            font-size: 0.8rem;
            margin-bottom: 10px;
        }

        .platform-status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            text-align: center;
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }

        .platform-card.selected .platform-status {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid #22c55e;
        }

        .platform-card.selected .platform-status::before {
            content: "✅ ";
        }

        .platform-card.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .platform-card.disabled:hover {
            transform: none;
            border-color: rgba(255, 215, 0, 0.3);
            background: rgba(255, 255, 255, 0.05);
        }

        .selection-help {
            margin-top: 20px;
            padding: 15px;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 10px;
            color: #60a5fa;
            font-size: 0.9rem;
            text-align: center;
        }

        .selection-help i {
            margin-left: 8px;
        }

        .platforms-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .platform-card {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .platform-card:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }

        .platform-card.selected {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.2);
        }

        .platform-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .platform-youtube { color: #ff0000; }
        .platform-telegram { color: #0088cc; }
        .platform-discord { color: #5865f2; }

        .platform-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .platform-input {
            margin-top: 10px;
            font-size: 0.9rem;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
        }

        .btn-success {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .actions {
            text-align: center;
            margin-top: 40px;
        }

        .actions .btn {
            margin: 0 10px;
        }

        .preview-section {
            background: rgba(34, 197, 94, 0.1);
            border: 2px solid rgba(34, 197, 94, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .preview-title {
            color: #22c55e;
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .preview-content {
            color: #ccc;
            line-height: 1.6;
        }

        .loading {
            display: none;
            text-align: center;
            color: #ffd700;
            font-size: 1.2rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .quick-templates {
                grid-template-columns: 1fr;
            }

            .duration-options {
                grid-template-columns: repeat(2, 1fr);
            }

            .platforms-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-magic"></i> إنشاء حملة سريعة</h1>
            <p>أنشئ حملة اشتراك مجاني في دقائق معدودة!</p>
        </div>

        <!-- قوالب سريعة -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-templates"></i>
                <span>اختر قالب سريع</span>
            </div>

            <div class="quick-templates">
                <div class="template-card" data-template="quick">
                    <div class="template-icon"><i class="fas fa-bolt"></i></div>
                    <div class="template-title">حملة سريعة</div>
                    <div class="template-desc">حملة لمدة يوم واحد مع المنصات الثلاث</div>
                </div>

                <div class="template-card" data-template="weekly">
                    <div class="template-icon"><i class="fas fa-calendar-week"></i></div>
                    <div class="template-title">حملة أسبوعية</div>
                    <div class="template-desc">حملة لمدة أسبوع مع مهام متنوعة</div>
                </div>

                <div class="template-card" data-template="monthly">
                    <div class="template-icon"><i class="fas fa-calendar-alt"></i></div>
                    <div class="template-title">حملة شهرية</div>
                    <div class="template-desc">حملة لمدة شهر مع مكافآت أكبر</div>
                </div>

                <div class="template-card" data-template="custom">
                    <div class="template-icon"><i class="fas fa-cog"></i></div>
                    <div class="template-title">حملة مخصصة</div>
                    <div class="template-desc">إنشاء حملة بإعدادات مخصصة</div>
                </div>
            </div>
        </div>

        <!-- معلومات الحملة -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-info-circle"></i>
                <span>معلومات الحملة</span>
            </div>

            <div class="form-group">
                <label class="form-label">عنوان الحملة (عربي)</label>
                <input type="text" class="form-input" id="titleAr" placeholder="مثال: اشتراك مجاني لمدة يوم واحد">
            </div>

            <div class="form-group">
                <label class="form-label">عنوان الحملة (إنجليزي)</label>
                <input type="text" class="form-input" id="titleEn" placeholder="Example: Free 1-Day Subscription">
            </div>

            <div class="form-group">
                <label class="form-label">وصف الحملة (عربي)</label>
                <textarea class="form-textarea" id="descAr" placeholder="احصل على اشتراك مجاني من خلال إكمال المهام البسيطة..."></textarea>
            </div>

            <div class="form-group">
                <label class="form-label">وصف الحملة (إنجليزي)</label>
                <textarea class="form-textarea" id="descEn" placeholder="Get a free subscription by completing simple tasks..."></textarea>
            </div>
        </div>

        <!-- مدة الحملة -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-clock"></i>
                <span>مدة الاشتراك</span>
            </div>

            <div class="duration-options">
                <div class="duration-option" data-days="1">
                    <div class="duration-number">1</div>
                    <div class="duration-label">يوم</div>
                </div>
                <div class="duration-option" data-days="3">
                    <div class="duration-number">3</div>
                    <div class="duration-label">أيام</div>
                </div>
                <div class="duration-option" data-days="7">
                    <div class="duration-number">7</div>
                    <div class="duration-label">أيام</div>
                </div>
                <div class="duration-option" data-days="14">
                    <div class="duration-number">14</div>
                    <div class="duration-label">يوم</div>
                </div>
                <div class="duration-option" data-days="30">
                    <div class="duration-number">30</div>
                    <div class="duration-label">يوم</div>
                </div>
            </div>
        </div>

        <!-- عدد المهام -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-list-ol"></i>
                <span>عدد المهام المطلوبة</span>
            </div>

            <div class="tasks-count-options">
                <div class="task-count-option" data-count="1">
                    <div class="count-number">1</div>
                    <div class="count-label">مهمة واحدة</div>
                    <div class="count-desc">سهل وسريع</div>
                </div>
                <div class="task-count-option" data-count="2">
                    <div class="count-number">2</div>
                    <div class="count-label">مهمتان</div>
                    <div class="count-desc">متوازن</div>
                </div>
                <div class="task-count-option" data-count="3">
                    <div class="count-number">3</div>
                    <div class="count-label">ثلاث مهام</div>
                    <div class="count-desc">شامل</div>
                </div>
            </div>
        </div>

        <!-- المنصات والمهام -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-tasks"></i>
                <span>اختر المنصات والمهام</span>
                <span class="selected-count" id="selectedCount">(0 من 1 محدد)</span>
            </div>

            <div class="platforms-grid">
                <div class="platform-card" data-platform="youtube" data-priority="1">
                    <div class="platform-header">
                        <div class="platform-icon platform-youtube"><i class="fab fa-youtube"></i></div>
                        <div class="platform-priority">أولوية: 1</div>
                    </div>
                    <div class="platform-name">يوتيوب</div>
                    <div class="platform-desc">الأكثر شعبية</div>
                    <input type="text" class="form-input platform-input" placeholder="رابط القناة" id="youtubeUrl">
                    <input type="text" class="form-input platform-input" placeholder="معرف القناة (اختياري)" id="youtubeId">
                    <div class="platform-status">غير محدد</div>
                </div>

                <div class="platform-card" data-platform="telegram" data-priority="2">
                    <div class="platform-header">
                        <div class="platform-icon platform-telegram"><i class="fab fa-telegram"></i></div>
                        <div class="platform-priority">أولوية: 2</div>
                    </div>
                    <div class="platform-name">تيليجرام</div>
                    <div class="platform-desc">تحديثات سريعة</div>
                    <input type="text" class="form-input platform-input" placeholder="رابط القناة" id="telegramUrl">
                    <input type="text" class="form-input platform-input" placeholder="@username" id="telegramUsername">
                    <div class="platform-status">غير محدد</div>
                </div>

                <div class="platform-card" data-platform="discord" data-priority="3">
                    <div class="platform-header">
                        <div class="platform-icon platform-discord"><i class="fab fa-discord"></i></div>
                        <div class="platform-priority">أولوية: 3</div>
                    </div>
                    <div class="platform-name">ديسكورد</div>
                    <div class="platform-desc">مجتمع تفاعلي</div>
                    <input type="text" class="form-input platform-input" placeholder="رابط الخادم" id="discordUrl">
                    <input type="text" class="form-input platform-input" placeholder="معرف الخادم (اختياري)" id="discordId">
                    <div class="platform-status">غير محدد</div>
                </div>
            </div>

            <div class="selection-help">
                <i class="fas fa-info-circle"></i>
                <span>سيتم اختيار المنصات حسب الأولوية المحددة أعلاه</span>
                <button class="btn btn-secondary" onclick="autoSelectPlatforms()" style="margin-top: 10px; font-size: 0.9rem; padding: 8px 15px;">
                    <i class="fas fa-magic"></i> اختيار تلقائي
                </button>
            </div>
        </div>

        <!-- معاينة الحملة -->
        <div class="preview-section" id="previewSection" style="display: none;">
            <div class="preview-title">
                <i class="fas fa-eye"></i>
                <span>معاينة الحملة</span>
            </div>
            <div class="preview-content" id="previewContent"></div>
        </div>

        <!-- أزرار العمل -->
        <div class="actions">
            <button class="btn btn-secondary" onclick="previewCampaign()">
                <i class="fas fa-eye"></i> معاينة
            </button>
            <button class="btn btn-success" onclick="createCampaign()" id="createBtn">
                <i class="fas fa-plus"></i> إنشاء الحملة
            </button>
            <a href="enhanced_tasks_admin.html" class="btn btn-secondary">
                <i class="fas fa-list"></i> عرض الحملات
            </a>
        </div>

        <div class="loading" id="loading">
            <i class="fas fa-spinner"></i> جاري إنشاء الحملة...
        </div>
    </div>

    <script src="easy_campaign_creator.js"></script>
</body>
</html>
