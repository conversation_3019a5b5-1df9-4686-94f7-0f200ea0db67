<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الاشتراك المجاني المحسنة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 215, 0, 0.4);
        }

        .header h1 {
            color: #ffd700;
            margin-bottom: 10px;
            font-size: 2.5rem;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .settings-card {
            background: rgba(255, 255, 255, 0.08);
            padding: 30px;
            border-radius: 20px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }

        .settings-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(255, 215, 0, 0.3);
            border-color: #ffd700;
        }

        .card-title {
            color: #ffd700;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setting-item {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .setting-label {
            display: block;
            color: white;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .setting-description {
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #ffd700;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .input-field {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
        }

        .input-field:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .select-field {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
        }

        .btn {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }

        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }

        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
        }

        .stat-label {
            color: #ccc;
            margin-top: 5px;
        }

        .actions-bar {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .hidden {
            display: none;
        }

        .success-message, .error-message {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }

        .success-message {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #86efac;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #fca5a5;
        }

        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .actions-bar {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-crown"></i> إعدادات الاشتراك المجاني المحسنة</h1>
            <p>تحكم كامل في نظام الاشتراك المجاني مع مميزات متقدمة</p>
        </div>

        <!-- Statistics Section -->
        <div class="statistics-grid">
            <div class="stat-card">
                <div class="stat-number" id="activeCampaigns">0</div>
                <div class="stat-label">حملات نشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeSubscribers">0</div>
                <div class="stat-label">مشتركين نشطين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTasks">0</div>
                <div class="stat-label">إجمالي المهام</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="bannerViews">0</div>
                <div class="stat-label">مشاهدات البانرات</div>
            </div>
        </div>

        <div class="settings-grid">
            <!-- Display Settings -->
            <div class="settings-card">
                <h3 class="card-title">
                    <i class="fas fa-eye"></i>
                    إعدادات العرض
                </h3>
                
                <div class="setting-item">
                    <label class="setting-label">عرض الاشتراك عند الدخول</label>
                    <div class="setting-description">عرض صفحة/مودال الاشتراك عند دخول المستخدم للتطبيق</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="showOnEntry">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-label">عرض الاشتراك قبل التحميل</label>
                    <div class="setting-description">عرض خيار الاشتراك قبل تحميل المودات مع زر التخطي</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="showBeforeDownload">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-label">استخدام صفحة الاشتراك</label>
                    <div class="setting-description">توجيه المستخدمين إلى صفحة اشتراك منفصلة بدلاً من المودال</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="useSubscriptionPage">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-label">تفعيل بانرات الاشتراك</label>
                    <div class="setting-description">عرض بانرات إعلانية للاشتراك المجاني في التطبيق</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="enableBanners">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <!-- Campaign Settings -->
            <div class="settings-card">
                <h3 class="card-title">
                    <i class="fas fa-bullhorn"></i>
                    إعدادات الحملة
                </h3>
                
                <div class="setting-item">
                    <label class="setting-label">الحملة الافتراضية</label>
                    <div class="setting-description">اختر الحملة التي ستظهر للمستخدمين بشكل افتراضي</div>
                    <select id="defaultCampaign" class="select-field">
                        <option value="">اختر حملة...</option>
                    </select>
                </div>

                <div class="setting-item" id="campaignBannerSettings" style="display: none;">
                    <label class="setting-label">إعدادات بانر الحملة</label>
                    <div class="setting-description">تخصيص بانر خاص بالحملة المختارة</div>
                    
                    <input type="hidden" id="campaignBannerId">
                    
                    <input type="text" id="campaignBannerTitle" class="input-field" placeholder="عنوان البانر" style="margin-bottom: 10px;">
                    
                    <textarea id="campaignBannerDescription" class="input-field" placeholder="وصف البانر" rows="3" style="margin-bottom: 10px; resize: vertical;"></textarea>
                    
                    <input type="url" id="campaignBannerImageUrl" class="input-field" placeholder="رابط صورة البانر" style="margin-bottom: 10px;">
                    
                    <input type="url" id="campaignBannerTargetUrl" class="input-field" placeholder="رابط الهدف (اختياري)" style="margin-bottom: 10px;">
                    
                    <input type="number" id="campaignBannerDisplayOrder" class="input-field" placeholder="ترتيب العرض" value="1" min="1" style="margin-bottom: 10px;">
                    
                    <label class="setting-label">
                        <input type="checkbox" id="campaignBannerIsActive" checked> البانر نشط
                    </label>
                    
                    <div style="margin-top: 15px;">
                        <button id="saveCampaignBanner" class="btn">حفظ بانر الحملة</button>
                        <button id="resetCampaignBannerForm" class="btn btn-danger">إعادة تعيين</button>
                    </div>
                </div>
            </div>

            <!-- Floating Icon Settings -->
            <div class="settings-card">
                <h3 class="card-title">
                    <i class="fas fa-circle"></i>
                    إعدادات الأيقونة العائمة
                </h3>
                
                <div class="setting-item">
                    <label class="setting-label">تفعيل الأيقونة العائمة</label>
                    <div class="setting-description">عرض أيقونة دائرية عائمة في يمين الشاشة للاشتراك المجاني</div>
                    <label class="toggle-switch">
                        <input type="checkbox" id="enableFloatingIcon">
                        <span class="slider"></span>
                    </label>
                </div>

                <div id="floatingIconDetails" class="setting-item hidden">
                    <label class="setting-label">رابط صورة الأيقونة</label>
                    <div class="setting-description">رابط الصورة التي ستظهر داخل الأيقونة العائمة</div>
                    <input type="url" id="floatingIconImageUrl" class="input-field" placeholder="https://example.com/icon.png">
                    
                    <div style="margin-top: 15px;">
                        <button id="saveFloatingIconSettings" class="btn">حفظ إعدادات الأيقونة</button>
                    </div>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div class="settings-card">
                <h3 class="card-title">
                    <i class="fas fa-cogs"></i>
                    إعدادات متقدمة
                </h3>
                
                <div class="setting-item">
                    <label class="setting-label">تأخير عرض الأيقونة العائمة (ثانية)</label>
                    <div class="setting-description">المدة قبل ظهور الأيقونة العائمة بعد تحميل التطبيق</div>
                    <input type="number" id="floatingIconDelay" class="input-field" value="2.5" min="0" max="10" step="0.5">
                </div>

                <div class="setting-item">
                    <label class="setting-label">تأخير عرض البانرات (ثانية)</label>
                    <div class="setting-description">المدة قبل ظهور بانرات الاشتراك بعد تحميل التطبيق</div>
                    <input type="number" id="bannerDelay" class="input-field" value="3" min="0" max="10" step="0.5">
                </div>

                <div class="setting-item">
                    <label class="setting-label">مدة دوران البانرات (ثانية)</label>
                    <div class="setting-description">المدة بين تغيير البانرات عند وجود أكثر من بانر</div>
                    <input type="number" id="bannerRotationDuration" class="input-field" value="5" min="2" max="15">
                </div>

                <div class="setting-item">
                    <label class="setting-label">حد عرض البانرات لكل مستخدم</label>
                    <div class="setting-description">عدد مرات عرض البانر للمستخدم الواحد (0 = بلا حدود)</div>
                    <input type="number" id="bannerDisplayLimit" class="input-field" value="0" min="0">
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="actions-bar">
            <button onclick="saveSettings()" class="btn btn-success">
                <i class="fas fa-save"></i> حفظ جميع الإعدادات
            </button>
            <button onclick="resetSettings()" class="btn btn-danger">
                <i class="fas fa-undo"></i> إعادة تعيين الإعدادات
            </button>
            <button onclick="testSubscriptionPage()" class="btn btn-info">
                <i class="fas fa-test-tube"></i> اختبار صفحة الاشتراك
            </button>
            <button onclick="clearUserData()" class="btn btn-danger">
                <i class="fas fa-trash"></i> مسح بيانات المستخدم للاختبار
            </button>
            <button onclick="exportSettings()" class="btn">
                <i class="fas fa-download"></i> تصدير الإعدادات
            </button>
            <button onclick="importSettings()" class="btn">
                <i class="fas fa-upload"></i> استيراد الإعدادات
            </button>
        </div>

        <!-- Messages Area -->
        <div id="messagesArea"></div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="subscription_settings.js"></script>
    <script src="enhanced-subscription-settings.js"></script>
</body>
</html>
