// ========================================
// إنشاء حملة سريعة - Easy Campaign Creator
// ========================================

// Supabase configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Global variables
let selectedTemplate = null;
let selectedDuration = 1;
let selectedTasksCount = 1;
let selectedPlatforms = [];
let maxAllowedTasks = 1;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Easy Campaign Creator loaded');
    initializeEventListeners();

    // تهيئة افتراضية
    selectTasksCount(1); // اختيار مهمة واحدة افتراضياً
    selectTemplate('quick'); // اختيار القالب السريع افتراضياً
});

// ========================================
// تهيئة مستمعي الأحداث
// ========================================
function initializeEventListeners() {
    // قوالب الحملات
    document.querySelectorAll('.template-card').forEach(card => {
        card.addEventListener('click', function() {
            const template = this.dataset.template;
            selectTemplate(template);
        });
    });

    // خيارات المدة
    document.querySelectorAll('.duration-option').forEach(option => {
        option.addEventListener('click', function() {
            const days = parseInt(this.dataset.days);
            selectDuration(days);
        });
    });

    // خيارات عدد المهام
    document.querySelectorAll('.task-count-option').forEach(option => {
        option.addEventListener('click', function() {
            const count = parseInt(this.dataset.count);
            selectTasksCount(count);
        });
    });

    // بطاقات المنصات
    document.querySelectorAll('.platform-card').forEach(card => {
        card.addEventListener('click', function() {
            const platform = this.dataset.platform;
            togglePlatform(platform);
            updatePlatformSpecificFields(); // Call new function to show/hide fields
        });
    });

    // تحديث المعاينة عند تغيير النصوص
    ['titleAr', 'titleEn', 'descAr', 'descEn'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
    });
}

// ========================================
// اختيار قالب الحملة
// ========================================
function selectTemplate(template) {
    selectedTemplate = template;

    // تحديث واجهة المستخدم
    document.querySelectorAll('.template-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-template="${template}"]`).classList.add('selected');

    // تطبيق إعدادات القالب
    applyTemplateSettings(template);
}

function applyTemplateSettings(template) {
    const templates = {
        quick: {
            titleAr: 'اشتراك مجاني سريع لمدة يوم واحد',
            titleEn: 'Quick 1-Day Free Subscription',
            descAr: 'احصل على اشتراك مجاني لمدة يوم واحد من خلال إكمال مهمة واحدة بسيطة',
            descEn: 'Get a 1-day free subscription by completing one simple task',
            duration: 1,
            tasksCount: 1,
            platforms: ['youtube']
        },
        weekly: {
            titleAr: 'اشتراك مجاني أسبوعي',
            titleEn: 'Weekly Free Subscription',
            descAr: 'احصل على اشتراك مجاني لمدة أسبوع كامل مع مهمتين متنوعتين',
            descEn: 'Get a full week free subscription with two diverse tasks',
            duration: 7,
            tasksCount: 2,
            platforms: ['youtube', 'telegram']
        },
        monthly: {
            titleAr: 'اشتراك مجاني شهري مميز',
            titleEn: 'Premium Monthly Free Subscription',
            descAr: 'اشتراك مجاني لمدة شهر كامل مع ثلاث مهام شاملة ومزايا حصرية',
            descEn: 'Full month free subscription with three comprehensive tasks and exclusive features',
            duration: 30,
            tasksCount: 3,
            platforms: ['youtube', 'telegram', 'discord']
        },
        custom: {
            titleAr: 'حملة مخصصة',
            titleEn: 'Custom Campaign',
            descAr: 'أنشئ حملتك المخصصة بالإعدادات التي تناسبك',
            descEn: 'Create your custom campaign with settings that suit you',
            duration: 7,
            tasksCount: 1,
            platforms: []
        }
    };

    const settings = templates[template];
    if (!settings) return;

    // تطبيق النصوص
    document.getElementById('titleAr').value = settings.titleAr;
    document.getElementById('titleEn').value = settings.titleEn;
    document.getElementById('descAr').value = settings.descAr;
    document.getElementById('descEn').value = settings.descEn;

    // تطبيق المدة
    selectDuration(settings.duration);

    // تطبيق عدد المهام
    selectTasksCount(settings.tasksCount || 1);

    // تطبيق المنصات
    selectedPlatforms = [];
    document.querySelectorAll('.platform-card').forEach(card => {
        card.classList.remove('selected');
    });

    settings.platforms.forEach(platform => {
        togglePlatform(platform);
    });

    // تحديث المعاينة
    updatePreview();
}

// ========================================
// اختيار مدة الحملة
// ========================================
function selectDuration(days) {
    selectedDuration = days;

    // تحديث واجهة المستخدم
    document.querySelectorAll('.duration-option').forEach(option => {
        option.classList.remove('selected');
    });
    document.querySelector(`[data-days="${days}"]`).classList.add('selected');

    updatePreview();
}

// ========================================
// اختيار عدد المهام
// ========================================
function selectTasksCount(count) {
    selectedTasksCount = count;
    maxAllowedTasks = count;

    // تحديث واجهة المستخدم
    document.querySelectorAll('.task-count-option').forEach(option => {
        option.classList.remove('selected');
    });
    document.querySelector(`[data-count="${count}"]`).classList.add('selected');

    // تحديث عداد المنصات المحددة
    updateSelectedCount();

    // إعادة تقييم المنصات المحددة
    validatePlatformSelection();

    updatePreview();
}

// ========================================
// تحديث عداد المنصات المحددة
// ========================================
function updateSelectedCount() {
    const countElement = document.getElementById('selectedCount');
    countElement.textContent = `(${selectedPlatforms.length} من ${maxAllowedTasks} محدد)`;

    // تحديث حالة المنصات
    updatePlatformsState();
}

// ========================================
// تحديث حالة المنصات
// ========================================
function updatePlatformsState() {
    const platforms = ['youtube', 'telegram', 'discord'];

    platforms.forEach((platform, index) => {
        const card = document.querySelector(`[data-platform="${platform}"]`);
        const status = card.querySelector('.platform-status');

        if (selectedPlatforms.includes(platform)) {
            // منصة محددة
            card.classList.add('selected');
            card.classList.remove('disabled');
            status.textContent = 'محدد';
        } else if (selectedPlatforms.length >= maxAllowedTasks) {
            // وصلنا للحد الأقصى
            card.classList.remove('selected');
            card.classList.add('disabled');
            status.textContent = 'غير متاح';
        } else {
            // منصة متاحة للاختيار
            card.classList.remove('selected', 'disabled');
            status.textContent = 'غير محدد';
        }
    });
}

// ========================================
// تبديل اختيار المنصة
// ========================================
function togglePlatform(platform) {
    const card = document.querySelector(`[data-platform="${platform}"]`);

    // التحقق من أن البطاقة غير معطلة
    if (card.classList.contains('disabled')) {
        showMessage('لا يمكن اختيار المزيد من المنصات. الحد الأقصى: ' + maxAllowedTasks, 'warning');
        return;
    }

    if (selectedPlatforms.includes(platform)) {
        // إلغاء الاختيار
        selectedPlatforms = selectedPlatforms.filter(p => p !== platform);
    } else if (selectedPlatforms.length < maxAllowedTasks) {
        // إضافة الاختيار إذا لم نصل للحد الأقصى
        selectedPlatforms.push(platform);
    } else {
        showMessage('وصلت للحد الأقصى من المهام: ' + maxAllowedTasks, 'warning');
        return;
    }

    // تحديث العداد والحالة
    updateSelectedCount();
    updatePreview();
    updatePlatformSpecificFields(); // Call new function to show/hide fields
}

// ========================================
// تحديث حقول الإدخال الخاصة بالمنصات
// ========================================
function updatePlatformSpecificFields() {
    const youtubeChannelIdGroup = document.getElementById('youtube-channel-id-group');
    
    if (selectedPlatforms.includes('youtube')) {
        youtubeChannelIdGroup.style.display = 'block';
    } else {
        youtubeChannelIdGroup.style.display = 'none';
    }
}

// ========================================
// التحقق من صحة اختيار المنصات
// ========================================
function validatePlatformSelection() {
    // إذا كان عدد المنصات المحددة أكبر من المسموح، قم بإزالة الزائد
    if (selectedPlatforms.length > maxAllowedTasks) {
        // احتفظ بالمنصات حسب الأولوية
        const platformPriority = ['youtube', 'telegram', 'discord'];
        const sortedSelected = selectedPlatforms.sort((a, b) => {
            return platformPriority.indexOf(a) - platformPriority.indexOf(b);
        });

        selectedPlatforms = sortedSelected.slice(0, maxAllowedTasks);

        showMessage(`تم تقليل عدد المنصات إلى ${maxAllowedTasks} حسب الأولوية`, 'info');
    }

    updateSelectedCount();
}

// ========================================
// اختيار تلقائي للمنصات حسب الأولوية
// ========================================
function autoSelectPlatforms() {
    const platformPriority = ['youtube', 'telegram', 'discord'];
    selectedPlatforms = [];

    for (let i = 0; i < maxAllowedTasks && i < platformPriority.length; i++) {
        selectedPlatforms.push(platformPriority[i]);
    }

    updateSelectedCount();
    updatePreview();

    showMessage(`تم اختيار ${selectedPlatforms.length} منصة تلقائياً حسب الأولوية`, 'success');
}

// ========================================
// تحديث المعاينة
// ========================================
function updatePreview() {
    const titleAr = document.getElementById('titleAr').value;
    const descAr = document.getElementById('descAr').value;

    if (!titleAr && !descAr) {
        document.getElementById('previewSection').style.display = 'none';
        return;
    }

    const previewContent = `
        <h3 style="color: #ffd700; margin-bottom: 15px;">${titleAr || 'عنوان الحملة'}</h3>
        <p style="margin-bottom: 15px;">${descAr || 'وصف الحملة'}</p>
        <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 15px;">
            <span style="background: rgba(255, 215, 0, 0.2); padding: 5px 10px; border-radius: 15px; font-size: 0.9rem;">
                <i class="fas fa-clock"></i> ${selectedDuration} ${selectedDuration === 1 ? 'يوم' : 'أيام'}
            </span>
            <span style="background: rgba(34, 197, 94, 0.2); padding: 5px 10px; border-radius: 15px; font-size: 0.9rem;">
                <i class="fas fa-tasks"></i> ${selectedPlatforms.length} مهام
            </span>
        </div>
        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            ${selectedPlatforms.map(platform => {
                const icons = {
                    youtube: '<i class="fab fa-youtube" style="color: #ff0000;"></i> يوتيوب',
                    telegram: '<i class="fab fa-telegram" style="color: #0088cc;"></i> تيليجرام',
                    discord: '<i class="fab fa-discord" style="color: #5865f2;"></i> ديسكورد'
                };
                return `<span style="background: rgba(255, 255, 255, 0.1); padding: 5px 10px; border-radius: 10px; font-size: 0.8rem;">${icons[platform]}</span>`;
            }).join('')}
        </div>
    `;

    document.getElementById('previewContent').innerHTML = previewContent;
    document.getElementById('previewSection').style.display = 'block';
}

// ========================================
// معاينة الحملة
// ========================================
function previewCampaign() {
    updatePreview();

    if (document.getElementById('previewSection').style.display === 'none') {
        showMessage('يرجى ملء معلومات الحملة أولاً', 'warning');
        return;
    }

    // التمرير إلى المعاينة
    document.getElementById('previewSection').scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });
}

// ========================================
// إنشاء الحملة
// ========================================
async function createCampaign() {
    try {
        // التحقق من صحة البيانات
        if (!validateCampaignData()) {
            return;
        }

        showLoading(true);

        // إنشاء الحملة
        const campaignData = prepareCampaignData();
        const { data: campaign, error: campaignError } = await supabaseClient
            .from('free_subscription_campaigns')
            .insert([campaignData])
            .select()
            .single();

        if (campaignError) {
            throw campaignError;
        }

        // إنشاء المهام
        const tasksData = prepareTasksData(campaign.id);
        if (tasksData.length > 0) {
            const { error: tasksError } = await supabaseClient
                .from('campaign_tasks')
                .insert(tasksData); // إزالة .select() هنا

            if (tasksError) {
                throw tasksError;
            }
        }

        showMessage('تم إنشاء الحملة بنجاح! 🎉', 'success');

        // إعادة توجيه بعد 3 ثوان
        setTimeout(() => {
            window.location.href = 'enhanced_tasks_admin.html';
        }, 3000);

    } catch (error) {
        console.error('خطأ في إنشاء الحملة:', error);
        showMessage('حدث خطأ أثناء إنشاء الحملة: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// ========================================
// التحقق من صحة البيانات
// ========================================
function validateCampaignData() {
    const titleAr = document.getElementById('titleAr').value.trim();
    const titleEn = document.getElementById('titleEn').value.trim();
    const descAr = document.getElementById('descAr').value.trim();
    const descEn = document.getElementById('descEn').value.trim();

    if (!titleAr || !titleEn) {
        showMessage('يرجى ملء عنوان الحملة باللغتين العربية والإنجليزية', 'warning');
        return false;
    }

    if (!descAr || !descEn) {
        showMessage('يرجى ملء وصف الحملة باللغتين العربية والإنجليزية', 'warning');
        return false;
    }

    if (selectedPlatforms.length === 0) {
        showMessage('يرجى اختيار منصة واحدة على الأقل', 'warning');
        return false;
    }

    if (selectedPlatforms.length !== selectedTasksCount) {
        showMessage(`يجب اختيار ${selectedTasksCount} منصة بالضبط. المحدد حالياً: ${selectedPlatforms.length}`, 'warning');
        return false;
    }

    // التحقق من روابط المنصات
    for (const platform of selectedPlatforms) {
        const url = document.getElementById(`${platform}Url`).value.trim();
        if (!url) {
            showMessage(`يرجى ملء رابط ${getPlatformName(platform)}`, 'warning');
            return false;
        }

        // التحقق من معرف قناة يوتيوب إذا كانت المنصة يوتيوب
        if (platform === 'youtube') {
            const channelId = document.getElementById('youtube-channel-id').value.trim();
            if (!channelId) {
                showMessage('يرجى ملء معرف قناة يوتيوب', 'warning');
                return false;
            }
        }
    }

    return true;
}

// ========================================
// تحضير بيانات الحملة
// ========================================
function prepareCampaignData() {
    const now = new Date();
    const endDate = new Date(now.getTime() + (selectedDuration * 24 * 60 * 60 * 1000));

    return {
        title_ar: document.getElementById('titleAr').value.trim(),
        title_en: document.getElementById('titleEn').value.trim(),
        description_ar: document.getElementById('descAr').value.trim(),
        description_en: document.getElementById('descEn').value.trim(),
        subscription_duration_days: selectedDuration,
        max_users: getMaxUsersForDuration(selectedDuration),
        is_active: true,
        start_date: now.toISOString(),
        end_date: endDate.toISOString()
    };
}

// ========================================
// تحضير بيانات المهام
// ========================================
function prepareTasksData(campaignId) {
    const tasks = [];
    let order = 1;

    selectedPlatforms.forEach(platform => {
        const url = document.getElementById(`${platform}Url`).value.trim();
        let targetId = '';

        if (platform === 'youtube') {
            targetId = document.getElementById('youtube-channel-id').value.trim();
        } else {
            targetId = document.getElementById(`${platform}Id`).value.trim() ||
                       document.getElementById(`${platform}Username`).value.trim() ||
                       extractIdFromUrl(platform, url);
        }

        const taskData = {
            campaign_id: campaignId,
            task_type: `${platform}_${platform === 'discord' ? 'join' : 'subscribe'}`,
            title_ar: getTaskTitle(platform, 'ar'),
            title_en: getTaskTitle(platform, 'en'),
            description_ar: getTaskDescription(platform, 'ar'),
            description_en: getTaskDescription(platform, 'en'),
            target_url: url,
            verification_method: 'smart',
            display_order: order++,
            is_required: true
        };

        tasks.push(taskData);
    });

    return tasks;
}

// ========================================
// دوال مساعدة
// ========================================
function getPlatformName(platform) {
    const names = {
        youtube: 'يوتيوب',
        telegram: 'تيليجرام',
        discord: 'ديسكورد'
    };
    return names[platform] || platform;
}

function getTaskTitle(platform, lang) {
    const titles = {
        youtube: {
            ar: 'اشترك في قناة يوتيوب',
            en: 'Subscribe to YouTube Channel'
        },
        telegram: {
            ar: 'انضم لقناة تيليجرام',
            en: 'Join Telegram Channel'
        },
        discord: {
            ar: 'انضم لخادم ديسكورد',
            en: 'Join Discord Server'
        }
    };
    return titles[platform]?.[lang] || `${platform} task`;
}

function getTaskDescription(platform, lang) {
    const descriptions = {
        youtube: {
            ar: 'اشترك في قناتنا على يوتيوب للحصول على آخر المحتوى',
            en: 'Subscribe to our YouTube channel for the latest content'
        },
        telegram: {
            ar: 'انضم لقناتنا على تيليجرام للحصول على التحديثات الفورية',
            en: 'Join our Telegram channel for instant updates'
        },
        discord: {
            ar: 'انضم لخادمنا على ديسكورد للدردشة مع المجتمع',
            en: 'Join our Discord server to chat with the community'
        }
    };
    return descriptions[platform]?.[lang] || `Complete ${platform} task`;
}

function extractIdFromUrl(platform, url) {
    // استخراج المعرف من الرابط (تطبيق بسيط)
    if (platform === 'youtube') {
        const match = url.match(/(?:youtube\.com\/channel\/|youtube\.com\/c\/|youtube\.com\/@)([^\/\?]+)/);
        return match ? match[1] : '';
    } else if (platform === 'telegram') {
        const match = url.match(/t\.me\/([^\/\?]+)/);
        return match ? '@' + match[1] : '';
    } else if (platform === 'discord') {
        const match = url.match(/discord\.gg\/([^\/\?]+)/);
        return match ? match[1] : '';
    }
    return '';
}

function getMaxUsersForDuration(days) {
    // تحديد الحد الأقصى للمستخدمين حسب مدة الحملة
    if (days === 1) return 100;
    if (days <= 7) return 500;
    if (days <= 14) return 1000;
    return 2000;
}

function showLoading(show) {
    document.getElementById('loading').style.display = show ? 'block' : 'none';
    document.getElementById('createBtn').disabled = show;
}

function showMessage(message, type = 'info') {
    // Remove existing messages
    document.querySelectorAll('.app-message').forEach(msg => msg.remove());

    const messageDiv = document.createElement('div');
    messageDiv.className = 'app-message';
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? 'linear-gradient(45deg, #22c55e, #16a34a)' :
                     type === 'error' ? 'linear-gradient(45deg, #ef4444, #dc2626)' :
                     type === 'warning' ? 'linear-gradient(45deg, #f59e0b, #d97706)' :
                     'linear-gradient(45deg, #3b82f6, #2563eb)'};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-weight: bold;
        z-index: 10002;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        animation: slideInDown 0.3s ease-out;
    `;

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 5000);
}
