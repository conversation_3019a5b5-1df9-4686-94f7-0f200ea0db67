/**
 * Supabase Manager البسيط - النسخة المبسطة
 * إدارة بسيطة لاتصال Supabase بدون تعقيدات
 */

(function() {
    'use strict';
    
    console.log('🚀 تهيئة Supabase Manager البسيط...');

    // إعدادات Supabase - المفتاح الصحيح
    const SUPABASE_CONFIG = {
        url: 'https://ytqxxodyecdeosnqoure.supabase.co',
        key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4'
    };

    // متغير عام لعميل Supabase
    let supabaseClient = null;
    let isInitialized = false;

    // دالة تهيئة عميل Supabase البسيطة
    function initializeSupabaseClient() {
        try {
            if (typeof supabase === 'undefined') {
                console.error('❌ مكتبة Supabase غير محملة');
                return false;
            }

            // إنشاء عميل Supabase
            supabaseClient = supabase.createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
            
            if (supabaseClient) {
                console.log('✅ تم إنشاء عميل Supabase بنجاح');
                isInitialized = true;
                
                // جعل العميل متاحاً عالمياً
                window.supabaseClient = supabaseClient;
                
                return true;
            } else {
                console.error('❌ فشل في إنشاء عميل Supabase');
                return false;
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة Supabase:', error);
            return false;
        }
    }

    // دالة الحصول على عميل Supabase
    function getSupabaseClient() {
        if (!isInitialized) {
            console.warn('⚠️ Supabase غير مهيأ، محاولة التهيئة...');
            initializeSupabaseClient();
        }
        return supabaseClient;
    }

    // إنشاء كائن SupabaseManager البسيط
    const SupabaseManager = {
        // الخصائص الأساسية
        isInitialized: () => isInitialized,
        getClient: getSupabaseClient,
        
        // الدوال الأساسية
        initialize: initializeSupabaseClient,
        
        // دوال مساعدة
        isReady: () => isInitialized && supabaseClient !== null,
        getConfig: () => SUPABASE_CONFIG
    };

    // جعل SupabaseManager متاحاً عالمياً
    window.supabaseManager = SupabaseManager;
    window.SupabaseManager = SupabaseManager;

    // تهيئة محسنة مع دعم التحميل الديناميكي
    function initializeImmediately() {
        if (typeof supabase !== 'undefined') {
            initializeSupabaseClient();
        } else {
            console.log('⏳ انتظار تحميل مكتبة Supabase...');

            // الاستماع لحدث تحميل المكتبة
            document.addEventListener('supabaseLoaded', function() {
                console.log('📡 تم استلام إشارة تحميل مكتبة Supabase');
                initializeSupabaseClient();
            }, { once: true });

            // الاستماع لحدث فشل التحميل
            document.addEventListener('supabaseLoadError', function() {
                console.error('❌ فشل في تحميل مكتبة Supabase - سيتم تعطيل الميزات المتصلة');
                isInitialized = false;
                supabaseClient = null;
            }, { once: true });

            // آلية احتياطية - انتظار تقليدي
            let attempts = 0;
            const maxAttempts = 100; // 10 ثوان

            const checkSupabase = setInterval(() => {
                attempts++;

                if (typeof supabase !== 'undefined') {
                    clearInterval(checkSupabase);
                    console.log('✅ تم العثور على مكتبة Supabase (آلية احتياطية)');
                    initializeSupabaseClient();
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkSupabase);
                    console.error('❌ انتهت مهلة انتظار تحميل مكتبة Supabase');

                    // إطلاق حدث فشل التحميل
                    const errorEvent = new CustomEvent('supabaseLoadError');
                    document.dispatchEvent(errorEvent);
                }
            }, 100);
        }
    }

    // تشغيل التهيئة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeImmediately);
    } else {
        initializeImmediately();
    }

    // تشغيل إضافي بعد ثانيتين للتأكد
    setTimeout(() => {
        if (!isInitialized && typeof supabase !== 'undefined') {
            console.warn('⚠️ إعادة محاولة تهيئة Supabase...');
            initializeImmediately();
        }
    }, 2000);

    console.log('✅ تم تحميل Supabase Manager البسيط');

})();
